import React, { useEffect } from 'react';
import styles from './Notification.module.css';

const Notification = ({ message, type, onClose }) => {
    useEffect(() => {
        const timer = setTimeout(() => {
            onClose();
        }, 3000);

        return () => clearTimeout(timer);
    }, [onClose]);

    return (
        <div className={`${styles.notification} ${styles[type]} ${styles.show}`}>
            <div className={styles.content}>
                {message}
                <button className={styles.closeButton} onClick={onClose} aria-label="Close notification">
                    &times;
                </button>
            </div>
        </div>
    );
};

export default Notification;
