.parent
{
    background-image:url("../images/1.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    height: 100vh;
    padding: 15%;
}

.header
{
    font-size: 26px;
    font-weight: bold;
    font-family: Verdana;
}

.forgotBox
{
    background-color: rgba(251, 237, 237, 0.8);
    width: 75% !important;
}

.userName
{
    color: black;
    font-size: 20px;
    font-family: consolas;
}

.bg-gradient-custom {
    background: linear-gradient(224.72deg, #081a51 65.18%, #123bb7 99.6%);
    color: white;
    height: 100vh;
    width: 100%;
  }
  
  .left-side {
    height: 100vh;
    display: flex;
    justify-content: center;
  }
  
  .logo-container {
    position: absolute;
    top: 30px;
    left: 40px;
  }
  
  .heading {
    font-weight: 600;
    font-size: 30px;
    line-height: 24px;
    letter-spacing: 0%;
    text-transform: capitalize;
  }
  
  .sub-heading {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0%;
  }
  
  .login-button {
    width: 373px;
    height: 44px;
    top: 495px;
    left: 110px;
    border-radius: 50px;
  }
  
  .right-heading {
    font-weight: 400;
    font-size: 30px;
    line-height: 24px;
    letter-spacing: 0%;
    text-align: center;
    text-transform: capitalize;
    color: #407BFF;
  }
  
  .input-email {
      width: 369px !important;
  }

  .formControl {
    border: none !important;
    outline: none !important;
    padding: 10px;
    width: 100%;
    box-shadow: none !important;
    background-color: rgba(255, 255, 255, 0.75);
  }