// User type definition
export const createUser = (data) => ({
  id: data.id || '',
  name: data.name || '',
  email: data.email || '',
  avatar: data.avatar || null,
  role: data.role || 'user',
  ...data
});

// Node data model
export const createDiagramNode = (data) => ({
  id: data.id || `node_${Date.now()}`,
  type: data.type || 'process',
  position: data.position || { x: 0, y: 0 },
  data: {
    label: data.label || 'New Node',
    description: data.description || '',
    assignedUsers: data.assignedUsers || [],
    connectionType: data.connectionType || 'unknown', // 'parallel', 'series', 'unknown'
    style: data.style || {
      backgroundColor: '#fff',
      borderColor: '#ccc',
      textColor: '#000',
    },
    metadata: data.metadata || {},
  },
  createdAt: data.createdAt || new Date(),
  updatedAt: data.updatedAt || new Date(),
  ...data
});

// Connection/Edge model
export const createDiagramEdge = (data) => ({
  id: data.id || `edge_${Date.now()}`,
  source: data.source || '',
  target: data.target || '',
  sourceHandle: data.sourceHandle || null,
  targetHandle: data.targetHandle || null,
  label: data.label || '',
  style: data.style || {
    stroke: '#999',
    strokeWidth: 2,
  },
  animated: data.animated || false,
  ...data
});

// Overall diagram model
export const createDiagram = (data) => ({
  id: data.id || `diagram_${Date.now()}`,
  name: data.name || 'New Diagram',
  description: data.description || '',
  nodes: data.nodes || [],
  edges: data.edges || [],
  viewport: data.viewport || { x: 0, y: 0, zoom: 1 },
  settings: data.settings || {
    gridEnabled: true,
    snapToGrid: false,
    readOnly: false,
  },
  collaborators: data.collaborators || [],
  createdBy: data.createdBy || '',
  createdAt: data.createdAt || new Date(),
  updatedAt: data.updatedAt || new Date(),
  version: data.version || 1,
  ...data
});

// Node types enum
export const NODE_TYPES = {
  PROCESS: 'process',
  SUB_PROCESS: 'sub_process',
  START: 'start',
  END: 'end',
  CUSTOM: 'custom',
  APPROVAL: 'approval',
  ESIGN: 'esign',
  ACKNOWLEDGE: 'acknowledge'
};

// Connection types enum
export const CONNECTION_TYPES = {
  PARALLEL: 'parallel',
  SERIES: 'series',
  MIXED: 'mixed',
  UNKNOWN: 'unknown'
};

// Utility functions to analyze workflow connections
export const analyzeWorkflowConnections = (nodes, edges) => {
  const connectionAnalysis = {};
  
  // First, find the main process node (first process node by position or most connected)
  const processNodes = nodes.filter(node => 
    node.type !== 'start' && 
    node.type !== 'end' 
  );
  
  if (processNodes.length === 0) {
    // No process nodes, mark all as unknown
    nodes.forEach(node => {
      connectionAnalysis[node.id] = { type: CONNECTION_TYPES.UNKNOWN, reason: 'No process nodes' };
    });
    return connectionAnalysis;
  }
  
  // Sort process nodes by position (top to bottom, left to right) to find the main node
  processNodes.sort((a, b) => {
    if (Math.abs(a.position.y - b.position.y) < 50) {
      return a.position.x - b.position.x; // Same row, sort by x
    }
    return a.position.y - b.position.y; // Different rows, sort by y
  });
  
  // The main node is typically the first process node
  const mainNode = processNodes[0];
  
  // Analyze each node's connection type based on how it's connected
  nodes.forEach(node => {
    if (node.type === 'start' || node.type === 'end') {
      connectionAnalysis[node.id] = { type: CONNECTION_TYPES.UNKNOWN, reason: 'Start/End node' };
      return;
    }
    
    // For the main node, analyze its outgoing connections to determine its pattern
    if (node.id === mainNode.id) {
      const outgoingEdges = edges.filter(edge => edge.source === node.id);
      const rightOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-right');
      const bottomOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-bottom');
      
      if (rightOutputEdges.length > 0 && bottomOutputEdges.length === 0) {
        connectionAnalysis[node.id] = { 
          type: CONNECTION_TYPES.PARALLEL, 
          reason: `Main node with ${rightOutputEdges.length} parallel connections (right output)` 
        };
      } else if (bottomOutputEdges.length > 0 && rightOutputEdges.length === 0) {
        connectionAnalysis[node.id] = { 
          type: CONNECTION_TYPES.SERIES, 
          reason: `Main node with ${bottomOutputEdges.length} serial connections (bottom output)` 
        };
      } else if (rightOutputEdges.length > 0 && bottomOutputEdges.length > 0) {
        connectionAnalysis[node.id] = { 
          type: CONNECTION_TYPES.MIXED, 
          reason: `Main node with mixed connections: ${rightOutputEdges.length} parallel + ${bottomOutputEdges.length} serial` 
        };
      } else {
        connectionAnalysis[node.id] = { 
          type: CONNECTION_TYPES.SERIES, 
          reason: 'Main node with no outgoing connections (default to series)' 
        };
      }
      return;
    }
    
    // For other nodes, determine their connection type based on incoming connections
    const incomingEdges = edges.filter(edge => edge.target === node.id);
    const outgoingEdges = edges.filter(edge => edge.source === node.id);
    
    if (incomingEdges.length === 0) {
      connectionAnalysis[node.id] = { 
        type: CONNECTION_TYPES.UNKNOWN, 
        reason: 'No incoming connections' 
      };
      return;
    }
    
    // Check the source handle of incoming connections to determine connection type
    const parallelIncoming = incomingEdges.filter(edge => edge.sourceHandle === 'output-right');
    const serialIncoming = incomingEdges.filter(edge => edge.sourceHandle === 'output-bottom');
    
    if (parallelIncoming.length > 0 && serialIncoming.length === 0) {
      connectionAnalysis[node.id] = { 
        type: CONNECTION_TYPES.PARALLEL, 
        reason: `Connected via parallel input (right output from source)` 
      };
    } else if (serialIncoming.length > 0 && parallelIncoming.length === 0) {
      connectionAnalysis[node.id] = { 
        type: CONNECTION_TYPES.SERIES, 
        reason: `Connected via serial input (bottom output from source)` 
      };
    } else if (parallelIncoming.length > 0 && serialIncoming.length > 0) {
      connectionAnalysis[node.id] = { 
        type: CONNECTION_TYPES.MIXED, 
        reason: `Mixed connections: ${parallelIncoming.length} parallel + ${serialIncoming.length} serial inputs` 
      };
    } else {
      // Fallback analysis based on connection patterns
      const analysis = determineConnectionType(node, incomingEdges, outgoingEdges, nodes, edges);
      connectionAnalysis[node.id] = analysis;
    }
  });
  
  return connectionAnalysis;
};

const determineConnectionType = (node, incomingEdges, outgoingEdges, allNodes, allEdges) => {
  // If no connections, it's unknown
  if (incomingEdges.length === 0 && outgoingEdges.length === 0) {
    return { type: CONNECTION_TYPES.UNKNOWN, reason: 'No connections' };
  }
  
  // If only incoming edges (end node pattern)
  if (incomingEdges.length > 0 && outgoingEdges.length === 0) {
    return { type: CONNECTION_TYPES.SERIES, reason: 'End node pattern' };
  }
  
  // If only outgoing edges (start node pattern)
  if (incomingEdges.length === 0 && outgoingEdges.length > 0) {
    return { type: CONNECTION_TYPES.SERIES, reason: 'Start node pattern' };
  }
  
  // Check for parallel patterns
  const isParallel = checkParallelPattern(node, incomingEdges, outgoingEdges, allNodes, allEdges);
  if (isParallel) {
    return { type: CONNECTION_TYPES.PARALLEL, reason: 'Parallel execution pattern' };
  }
  
  // Check for series patterns
  const isSeries = checkSeriesPattern(node, incomingEdges, outgoingEdges, allNodes, allEdges);
  if (isSeries) {
    return { type: CONNECTION_TYPES.SERIES, reason: 'Sequential execution pattern' };
  }
  
  return { type: CONNECTION_TYPES.UNKNOWN, reason: 'Complex or mixed pattern' };
};

const checkParallelPattern = (node, incomingEdges, outgoingEdges, allNodes, allEdges) => {
  // Pattern 1: Multiple incoming edges from different sources (fan-in)
  if (incomingEdges.length > 1) {
    const sourceNodes = incomingEdges.map(edge => edge.source);
    const uniqueSources = [...new Set(sourceNodes)];
    
    // If multiple unique sources, likely parallel
    if (uniqueSources.length > 1) {
      return true;
    }
  }
  
  // Pattern 2: Multiple outgoing edges to different targets (fan-out)
  if (outgoingEdges.length > 1) {
    const targetNodes = outgoingEdges.map(edge => edge.target);
    const uniqueTargets = [...new Set(targetNodes)];
    
    // If multiple unique targets, likely parallel
    if (uniqueTargets.length > 1) {
      return true;
    }
  }
  
  // Pattern 3: Check if this node is part of a parallel branch
  const parallelBranches = findParallelBranches(node, allNodes, allEdges);
  if (parallelBranches.length > 0) {
    return true;
  }
  
  return false;
};

const checkSeriesPattern = (node, incomingEdges, outgoingEdges, allNodes, allEdges) => {
  // Pattern 1: Single incoming and single outgoing edge
  if (incomingEdges.length === 1 && outgoingEdges.length === 1) {
    return true;
  }
  
  // Pattern 2: Linear chain pattern
  const isInLinearChain = checkLinearChain(node, allNodes, allEdges);
  if (isInLinearChain) {
    return true;
  }
  
  return false;
};

const findParallelBranches = (node, allNodes, allEdges) => {
  const branches = [];
  
  // Find nodes that have multiple outgoing edges (split points)
  const splitNodes = allNodes.filter(n => {
    const outgoing = allEdges.filter(e => e.source === n.id);
    return outgoing.length > 1;
  });
  
  // Find nodes that have multiple incoming edges (join points)
  const joinNodes = allNodes.filter(n => {
    const incoming = allEdges.filter(e => e.target === n.id);
    return incoming.length > 1;
  });
  
  // Check if current node is between a split and join
  splitNodes.forEach(splitNode => {
    joinNodes.forEach(joinNode => {
      if (splitNode.id !== joinNode.id) {
        const pathExists = checkPathExists(splitNode.id, node.id, allEdges) && 
                          checkPathExists(node.id, joinNode.id, allEdges);
        if (pathExists) {
          branches.push({ splitNode, joinNode });
        }
      }
    });
  });
  
  return branches;
};

const checkLinearChain = (node, allNodes, allEdges) => {
  // Check if node is part of a simple linear chain
  const incoming = allEdges.filter(e => e.target === node.id);
  const outgoing = allEdges.filter(e => e.source === node.id);
  
  // Each node in chain should have at most one incoming and one outgoing edge
  if (incoming.length <= 1 && outgoing.length <= 1) {
    return true;
  }
  
  return false;
};

const checkPathExists = (sourceId, targetId, edges) => {
  const visited = new Set();
  const queue = [sourceId];
  
  while (queue.length > 0) {
    const current = queue.shift();
    
    if (current === targetId) {
      return true;
    }
    
    if (visited.has(current)) {
      continue;
    }
    
    visited.add(current);
    
    // Find all outgoing edges from current node
    const outgoingEdges = edges.filter(e => e.source === current);
    outgoingEdges.forEach(edge => {
      if (!visited.has(edge.target)) {
        queue.push(edge.target);
      }
    });
  }
  
  return false;
};

// Function to update node connection types
export const updateNodeConnectionTypes = (nodes, edges) => {
  const analysis = analyzeWorkflowConnections(nodes, edges);
  
  // Enhanced level tracking system with proper parent-child relationships
  const processNodes = nodes.filter(node => 
    node.type !== 'start' && 
    node.type !== 'end' 
  );
  
  // Sort nodes by position to maintain order (top to bottom, left to right)
  processNodes.sort((a, b) => {
    if (Math.abs(a.position.y - b.position.y) < 50) {
      return a.position.x - b.position.x; // Same row, sort by x
    }
    return a.position.y - b.position.y; // Different rows, sort by y
  });
  
  const nodeLevels = new Map();
  const processedNodes = new Set();
  let currentLevel = 1;

  // Helper function to find the parent node of a given node
  const findParentNode = (nodeId) => {
    const incomingEdges = edges.filter(edge => edge.target === nodeId);
    if (incomingEdges.length === 0) return null;
    
    // Return the source node of the first incoming edge
    const parentEdge = incomingEdges[0];
    const parentNode = processNodes.find(n => n.id === parentEdge.source);
    return parentNode;
  };

  // Helper function to determine connection type from edge
  const getConnectionTypeFromEdge = (sourceNodeId, targetNodeId) => {
    const edge = edges.find(e => e.source === sourceNodeId && e.target === targetNodeId);
    if (!edge) return 'serial';
    
    if (edge.sourceHandle === 'output-right') {
      return 'parallel';
    } else if (edge.sourceHandle === 'output-bottom') {
      return 'serial';
    }
    return 'serial'; // default
  };

  // Recursive function to assign levels based on actual parent-child relationships
  const assignNodeLevel = (node) => {
    if (processedNodes.has(node.id)) {
      return nodeLevels.get(node.id);
    }

    const parentNode = findParentNode(node.id);
    
    if (!parentNode) {
      // This is a root node (no parent)
      const levelInfo = {
        level: `level${currentLevel}`,
        rootLevel: null,
        rootLevelType: null
      };
      nodeLevels.set(node.id, levelInfo);
      processedNodes.add(node.id);
      currentLevel++;
      return levelInfo;
    } else {
      // This node has a parent - ensure parent is processed first
      const parentLevelInfo = assignNodeLevel(parentNode);
      
      // Determine connection type between parent and this node
      const connectionType = getConnectionTypeFromEdge(parentNode.id, node.id);
      
      // Assign level info based on parent relationship
      const levelInfo = {
        level: `level${currentLevel}`,
        rootLevel: parentLevelInfo.level, // Parent's level becomes this node's rootLevel
        rootLevelType: connectionType
      };
      
      nodeLevels.set(node.id, levelInfo);
      processedNodes.add(node.id);
      currentLevel++;
      return levelInfo;
    }
  };

  // Process all nodes to assign proper levels
  processNodes.forEach(node => {
    assignNodeLevel(node);
  });
  
  return nodes.map(node => {
    const nodeLevelInfo = nodeLevels.get(node.id) || {
      level: `level${processNodes.findIndex(n => n.id === node.id) + 1}`,
      rootLevel: null,
      rootLevelType: null
    };
    
    return {
      ...node,
      data: {
        ...node.data,
        connectionType: analysis[node.id]?.type || CONNECTION_TYPES.UNKNOWN,
        connectionReason: analysis[node.id]?.reason || 'Unknown pattern',
        level: nodeLevelInfo.level,
        rootLevel: nodeLevelInfo.rootLevel,
        rootLevelType: nodeLevelInfo.rootLevelType
      }
    };
  });
};

// Utility function to identify main process node and analyze its output connections
export const analyzeMainNodeOutputConnections = (nodes, edges) => {
  // Find all process nodes (excluding start/end nodes)
  const processNodes = nodes.filter(node => 
    node.type !== 'start' && 
    node.type !== 'end' 
  );
  
  if (processNodes.length === 0) {
    return { 
      mainNode: null, 
      rootLevelType: 'series', 
      reason: 'No process nodes found' 
    };
  }
  
  // Find the main node (first process node or node with most outgoing connections)
  const mainNode = processNodes.reduce((main, current) => {
    const mainOutgoing = edges.filter(edge => edge.source === main.id).length;
    const currentOutgoing = edges.filter(edge => edge.source === current.id).length;
    return currentOutgoing > mainOutgoing ? current : main;
  }, processNodes[0]);
  
  // Get all outgoing edges from the main node
  const outgoingEdges = edges.filter(edge => edge.source === mainNode.id);
  
  if (outgoingEdges.length === 0) {
    return { 
      mainNode, 
      rootLevelType: 'series', 
      reason: 'No outgoing connections from main node' 
    };
  }
  
  // Check which output handles are used
  const rightOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-right');
  const bottomOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-bottom');
  
  // Determine root level type based on output handle usage
  if (rightOutputEdges.length > 0 && bottomOutputEdges.length === 0) {
    return { 
      mainNode,
      rootLevelType: 'parallel', 
      reason: `Main node connected via right output (${rightOutputEdges.length} parallel connections)`,
      rightConnections: rightOutputEdges.length,
      bottomConnections: 0
    };
  } else if (bottomOutputEdges.length > 0 && rightOutputEdges.length === 0) {
    return { 
      mainNode,
      rootLevelType: 'series', 
      reason: `Main node connected via bottom output (${bottomOutputEdges.length} series connections)`,
      rightConnections: 0,
      bottomConnections: bottomOutputEdges.length
    };
  } else if (rightOutputEdges.length > 0 && bottomOutputEdges.length > 0) {
    return { 
      mainNode,
      rootLevelType: 'mixed', 
      reason: `Main node has both parallel (${rightOutputEdges.length}) and series (${bottomOutputEdges.length}) connections`,
      rightConnections: rightOutputEdges.length,
      bottomConnections: bottomOutputEdges.length
    };
  } else {
    // Fallback: analyze based on connection patterns
    const connectionTypes = processNodes.map(node => node.data?.connectionType || 'unknown');
    const hasParallel = connectionTypes.includes('parallel');
    const hasSeries = connectionTypes.includes('series');
    
    if (hasParallel && !hasSeries) {
      return { 
        mainNode,
        rootLevelType: 'parallel', 
        reason: 'Parallel pattern detected',
        rightConnections: 0,
        bottomConnections: 0
      };
    } else if (hasSeries) {
      return { 
        mainNode,
        rootLevelType: 'series', 
        reason: 'Series pattern detected',
        rightConnections: 0,
        bottomConnections: 0
      };
    } else {
      return { 
        mainNode,
        rootLevelType: 'series', 
        reason: 'Default to series',
        rightConnections: 0,
        bottomConnections: 0
      };
    }
  }
};

// Default node styles by type
export const DEFAULT_NODE_STYLES = {
  [NODE_TYPES.PROCESS]: {
    backgroundColor: '#e1f5fe',
    borderColor: '#0277bd',
    textColor: '#01579b',
    borderRadius: '8px',
  },
  [NODE_TYPES.SUB_PROCESS]: {
    backgroundColor: '#fff3e0',
    borderColor: '#f57c00',
    textColor: '#e65100',
    borderRadius: '8px',
  },
  [NODE_TYPES.START]: {
    backgroundColor: '#e8f5e8',
    borderColor: '#388e3c',
    textColor: '#1b5e20',
    borderRadius: '50%',
  },
  [NODE_TYPES.END]: {
    backgroundColor: '#ffebee',
    borderColor: '#d32f2f',
    textColor: '#c62828',
    borderRadius: '50%',
  },
  [NODE_TYPES.CUSTOM]: {
    backgroundColor: '#f3e5f5',
    borderColor: '#7b1fa2',
    textColor: '#4a148c',
    borderRadius: '8px',
  },
  [NODE_TYPES.APPROVAL]: {
    backgroundColor: '#e8f5e8',
    borderColor: '#388e3c',
    textColor: '#1b5e20',
    borderRadius: '8px',
  },
  [NODE_TYPES.ESIGN]: {
    backgroundColor: '#fff8e1',
    borderColor: '#fbc02d',
    textColor: '#f57f17',
    borderRadius: '8px',
  },
  [NODE_TYPES.ACKNOWLEDGE]: {
    backgroundColor: '#e3f2fd',
    borderColor: '#1976d2',
    textColor: '#0d47a1',
    borderRadius: '8px',
  },
};
