import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import styles from "./TagModal.module.css"; // Import CSS module
import { editById, findById } from "../../../services/apiService";

const TagModal = ({ isOpen, onClose, onSubmit, id }) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [type, setType] = useState("boolean");
  const [useMultiple, setUseMultiple] = useState(false);

  //Type array
  const typeArray = [
    "boolean",
    "date",
    "datetime",
    "decimal",
    "email",
    "integer",
    "list",
    "longtext",
    "time",
    "url",
  ];

  useEffect(() => {
    fetchMetaDataById(id);
  }, [id]);

  const fetchMetaDataById = async (id) => {
    if (id) {
      const api = `/metadata/${id}`;
      try {
        const response = await findById(api);
        console.log(response.data);
        const data = response.data;
        setName(data.fieldName);
        setDescription(data.fieldDesc);
        setType(data.fieldType);
        setUseMultiple(data.multiple);
      } catch (error) {
        //throw error;
      }
    }
  };

  const updateMetaData = async (data) => {
    console.log("Updated metadata : ", data);
    const api = `/metadata/${id}`;
    try {
      const response = await editById(api, data);
      fetchMetaDataById();
      return response.data;
    } catch (error) {
      throw error;
    }
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      fieldName: name,
      fieldDesc: description,
      fieldType: type,
      multiple: useMultiple,
    };
    if (id) {
      updateMetaData(data);
    } else {
      onSubmit(data);
    }
  };

  return (
    <Modal show={isOpen} onHide={onClose} centered size="md">
      <Modal.Header
        closeButton
        className="bg-info"
      >
        <Modal.Title
          style={{
            fontSize: "16px",
            textAlign: "center",
            width: "100%",
            margin: 0,
          }}
        >
          {id ? "Edit Metadata Field" : "New Metadata Field"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <form
          className={styles.formModal}
          onSubmit={handleSubmit}
          style={{ marginTop: "-8px" }}
        >
          <div className="container my-3">
            <div className="row">
              <div className="col-12">
                <input
                  type="text"
                  id="name"
                  className="form-control"
                  value={name}
                  required
                  placeholder="Field Name"
                  onChange={(e) => setName(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="container my-3">
            <div className="row">
              <div className="col-12">
                <input
                  type="text"
                  id="description"
                  className="form-control"
                  value={description}
                  required
                  placeholder="Field Description"
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="container my-3">
            <div className="row">
              <div className="col-12">
                <select
                  id="type"
                  className="form-control"
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                >
                  {typeArray.map((type, index) => {
                    return (
                      <option key={index} value={type}>
                        {type}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
          </div>

          <div className="container my-3">
            <div className="row">
              <div className="col-8">
                <div className="form-check">
                  <input
                    type="checkbox"
                    className="form-check-input"
                    checked={useMultiple}
                    id="multiple"
                    onChange={(e) => setUseMultiple(e.target.checked)}
                  />
                  <label className="form-check-label" htmlFor="multiple">
                    Allow using multiple times for the same file
                  </label>
                </div>
              </div>

              <div className="col-4">
                <Button
                  type="submit"
                  className={styles.submitButton}
                  variant="primary"
                >
                  {id ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TagModal;
