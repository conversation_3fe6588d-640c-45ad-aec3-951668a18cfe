import React, { useState, useEffect } from "react";
import "./NewDashboard.css";
import { Outlet } from "react-router-dom";
import { NavLink } from "react-router-dom";
import Nav from "react-bootstrap/Nav";
import NavDropdown from "react-bootstrap/NavDropdown";
import { Modal, Form, Button, Dropdown } from "react-bootstrap";
import { useNavigate } from "react-router";
import axios from "axios";
import { GlobalConstants } from "../../constants/global-constants";
import { formatDate, deleteById, getList } from "../../services/apiService";
import Notification from "../Notification/Notification";

// Custom NavLink component that collapses sidebar on click if it was initially collapsed
const SubMenuItem = ({ to, children }) => {
  const handleClick = () => {
    // Access the setSidebarOpen function from the parent component
    // This will be passed down via context or props
    if (typeof window.collapseSidebar === 'function' && typeof window.wasSidebarInitiallyClosed === 'boolean') {
      // Only collapse if the sidebar was initially closed
      if (window.wasSidebarInitiallyClosed) {
        window.collapseSidebar();
      }
    }
  };

  return (
    <Nav.Link as={NavLink} to={to} className="dropdown-item" onClick={handleClick}>
      {children}
    </Nav.Link>
  );
};

function NewDashboard(props) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [wasSidebarInitiallyClosed, setWasSidebarInitiallyClosed] = useState(false);
  
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Function to handle submenu item click
  const handleSubmenuItemClick = () => {
    setSidebarOpen(false);
  };

  // Make the function available globally for the SubMenuItem component
  window.collapseSidebar = handleSubmenuItemClick;
  window.wasSidebarInitiallyClosed = false;

  const [showModal, setShowModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isPasswordMissMatch, setIsPasswordMissMatch] = useState(false);
  const [result, setResult] = useState(false);
  const [role, setRole] = useState(localStorage.getItem("role"));
  const [userName, setUserName] = useState(localStorage.getItem("userName"));

  const [isUserManagementOpen, setIsUserManagementOpen] = useState(false);
  const [isConfigurationOpen, setIsConfigurationOpen] = useState(false);
  const [isReportsOpen, setIsReportsOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [permisionsArray, setPermisionsArray] = useState([]);

  // New state for notifications
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [notificationModalOpen, setNotificationModalOpen] = useState(false);
  const [notificationCount, setNotificationCount] = useState(null);
  const [shouldShake, setShouldShake] = useState(false);

  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [validationModalOpen,setValidationModalOpen]= useState(false);

  const filteredNotifications = notifications.filter((notification) =>notification.notificationMessage
              .toLowerCase().includes(searchTerm.toLowerCase())
  );

  const [notification, setNotification] = useState({
    message: "",
    type: "",
    show: false,
  });

  useEffect(() => {
    localStorage.getItem("role")  !== "ROLE_SUPER_ADMIN" && getallPermissions();
    fetchNotifications();

    const intervalId = setInterval(fetchNotifications, 60000);
    return () => clearInterval(intervalId);
  }, []);

  const handleShow = () => setShowModal(true);
  const handleClose = () => {
    setShowModal(false);
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setIsPasswordMissMatch(false);
    setResult(false);
  };

  const navigate = useNavigate();

  const handleCurrentPassword =(e) => {
    setResult(false);
    setCurrentPassword(e.target.value);
  }

  const handleConfirmNewPassword = (e) => {
    setNewPassword(e.target.value)
    setIsPasswordMissMatch(false);
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log("Current Password:", currentPassword);
    console.log("New Password:", newPassword);
    console.log("Confirm New Password", confirmPassword);
    if (newPassword !== confirmPassword) {
      setIsPasswordMissMatch(true);
      setConfirmPassword("");
      return;
    }
    if(currentPassword){
      const resp=await checkCurrentPassword(currentPassword)
      if(!resp){
        setResult(true);
        // alert("current password does not match")
        return;
      }
      else{
        changePassword();
      }
    }
  };

  const checkCurrentPassword = async (password) => {
    const api=`/user/check_existing_password/${localStorage.getItem("id")}?password=${password}`
    try{
      const response= await getList(api);
      return response.data;
    }
    catch(e){    }
  }

  const changePassword = async () => {
    const existingPassword = currentPassword;
    let api = `${
      GlobalConstants.globalURL
    }/user/user_change_password/${localStorage.getItem("id")}`;
    try {
      const data = await axios.get(api, {
        params: { existingPassword, newPassword },
      });
      console.log(data.data.status)
      if(data.data.status === "success") {
        setNotification({
          message: "Password successfully changed",
          type: "success",
          show: true,
        });
        setTimeout(() => {
          navigate("/");
        }, 1000);
      }
    } catch (error) {
      // setResult(true);
    }
  };

  const closeNotification = () => {
    setNotification({ message: "", type: "", show: false });
  };

  const getallPermissions = async () => {
    let api = `${
      GlobalConstants.globalURL
    }/user/employeepermissionsfordisplay?employeeId=${localStorage.getItem(
      "id"
    )}`;
    const data = await axios.get(api);
    console.log(data.data.data);
    setPermisionsArray(data.data.data);
    // });
  };

  const handleLogout = () => {
    // Clear the token and other relevant user information
    localStorage.removeItem("token");
    localStorage.removeItem("userName");
    localStorage.removeItem("firstName");
    localStorage.removeItem("id");
    localStorage.removeItem("companyId");
    localStorage.removeItem("role");
    localStorage.removeItem("companyName");

    // Redirect to the login page
    navigate("/", { replace: true });
  };

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  const handleCloseNotificationModal = () => {
    setNotificationModalOpen(false);
  };

  const handleDismissNotification = async (id) => {
    try {
      setNotifications(notifications.filter((n) => n.id !== id));
      const count=notifications.length-1;
      setNotificationCount(count);
      const api = `/notification/${id}`;
      await deleteById(api);
    } catch (error) {
      setNotifications(notifications);
    }
  };

  const fetchNotifications = async () => {
    checkActiveUser();
    let api = `${
      GlobalConstants.globalURL
    }/notification/list-by/${localStorage.getItem("id")}`;
    try {
      const { data } = await axios.get(api);
      if (notificationCount && notificationCount < data.data.length) {
        // alert(data.data[0].notificationMessage);
        setNotificationCount(data.data.length);
        setShouldShake(true);
        setTimeout(() => setShouldShake(false), 5000);
      }
      setNotifications(data.data || []);
      const count=data.data.length;
      setNotificationCount(count);
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setNotifications([]);
    }
  };

  const checkActiveUser = async () =>{
    //alert("You're not an active user, logging out in 60 seconds!");
    let api = `${
      GlobalConstants.globalURL
    }/user/check-user-status/${localStorage.getItem("id")}`;
    try {
      const resp = await axios.get(api);
      //console.log(resp.data.data);
      if(resp.data.data){
        setValidationModalOpen(true);
        setTimeout(() => {
          navigate("/");
        }, 5000);
      }
    }catch(e){ }
  }

  return (
    <div>
      {/* Top Navigation Bar */}
      <nav className="navbar navbar-expand-lg fixed-top">
        <div className="container-fluid d-flex align-items-center">
          <div className="d-flex align-items-center">
          <button className="btn btn-primary d-flex align-items-center me-2" onClick={toggleSidebar}>
            <i className="fa fa-bars"></i>
          </button>
          <a className="navbar-brand d-flex align-items-center">
            <i className="fa fa-folder-open me-2"></i>
            <span>{localStorage.getItem("companyName")}</span>
          </a>
          </div>
          {/* <div className="d-flex align-items-center">
            <input type="text" className="form-control" placeholder="Search" />

            <Button variant="primary" className="w-100"><i className="fa fa-search"></i> Advanced Search</Button>
          </div> */}
          <div className="d-flex align-items-center">
            <Dropdown
              show={showNotifications}
              align="end"
              onToggle={(isOpen) => setShowNotifications(isOpen)}
            >
              <Dropdown.Toggle
                variant="link"
                id="dropdown-notifications"
                onClick={toggleNotifications}
                className="position-relative"
              >
                <i
                  className={`fa fa-bell fs-5 position-relative ${
                    shouldShake ? "fast-shake" : ""
                  }`}
                  // style={{
                  //   display: 'inline-block',
                  //   transform: shouldShake ? 'rotate(-50deg)' : 'rotate(0deg)',
                  //   color: shouldShake ? 'red' : 'inherit',
                  //   transition: 'all 0.1s ease-in-out'
                  // }}
                >
                  <sup
                    className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                    style={{ fontSize: "0.6em", padding: "0.25em 0.4em" }}
                  >
                    {notificationCount}
                  </sup>
                </i>
              </Dropdown.Toggle>

              <Dropdown.Menu className="notification-dropdown">
                <Dropdown.Header>Notifications</Dropdown.Header>
                {notifications.length > 0 ? (
                  notifications.slice(0, 10).map((notification, index) => (
                    <Dropdown.Item
                      key={index}
                      className="notification-item"
                      onClick={() => setShowNotifications(false)}
                      as="div"
                    >
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <div
                            className="notification-message"
                            style={{ color: "blue", fontWeight: "600" }}
                          >
                            {notification.notificationMessage}
                          </div>
                          <div className="notification-date text-muted small">
                            {formatDate(notification.createdDate)}
                          </div>
                        </div>
                        <button
                          className="btn text-danger p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDismissNotification(notification.id);
                          }}
                          title="Dismiss notification"
                        >
                          <span style={{ fontSize: "2rem" }}>&times;</span>
                        </button>
                      </div>
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item className="text-muted">
                    No notifications
                  </Dropdown.Item>
                )}
                {notifications.length > 10 && (
                  <Dropdown.Item
                    className="text-center text-primary"
                    onClick={() => {
                      setShowNotifications(false);
                      setNotificationModalOpen(true);
                    }}
                  >
                    View all notifications
                  </Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
            <Nav title={localStorage.getItem("userName")} style={{cursor:"text"}}>
              <span className="me-3 d-flex align-items-center">
                <i className="fa fa-user-circle-o me-2" ></i>
                  {localStorage.getItem("firstName")}
              </span>
              <NavDropdown
                title={
                  <i className="fa fa-cog"></i>
                }
                id="nav-dropdown"
                align="end"
                className="user-menu-dropdown"
              >
                <NavDropdown.Item
                  onClick={handleShow}
                  className="dropdown-item myDark"
                >
                  <i className="fa fa-key me-2"></i> Change Password
                </NavDropdown.Item>
                <NavDropdown.Item
                  onClick={handleLogout}
                  className="dropdown-item myDark"
                >
                  <i className="fa fa-sign-out me-2"></i> Logout
                </NavDropdown.Item>
              </NavDropdown>
            </Nav>
          </div>
        </div>
      </nav>

      {/* Layout with Sidebar */}
      <div className="dashboard-layout d-flex">
        {/* Sidebar */}
        <div className={`sidebar text-white ${sidebarOpen ? "open" : "collapsed"}`}>
          <div className={`px-3 mb-2 ${!sidebarOpen ? 'd-none' : ''}`}>
            <h6 className="text-uppercase text-muted section-header">
              Menu Navigation
            </h6>
            <hr className="section-divider" />
          </div>
          <ul className="list-group list-group-flush">
            <li className="list-group-item align-items-flex-start">
              <Nav.Link
                title={`${!sidebarOpen ? "Inbox" : ''}`}
                as={NavLink}
                to="/newDS/document-management"
                className={`nav-link ${!sidebarOpen ? 'show-tooltip' : ''}`}
                data-tooltip="Inbox"
                onClick={() => {
                  // Handle navigation to close file details if open
                  if (typeof window.handleInboxNavigation === 'function') {
                    window.handleInboxNavigation();
                  }
                }}
                end
              >
                <i className="fa fa-inbox me-2"></i>
                <span className={!sidebarOpen ? 'd-none' : ''}>Inbox</span>
                {props.count > 0 && sidebarOpen && (
                  <span className={`badge rounded-pill bg-primary ms-2`}>{props.count}</span>
                )}
              </Nav.Link>
            </li>

            <li className="list-group-item">
              <Nav.Link
                title={`${!sidebarOpen ? "Teams" : ''}`}
                as={NavLink}
                to="/newDS/team"
                className={`nav-link ${!sidebarOpen ? 'show-tooltip' : ''}`}
                data-tooltip="Teams"
                end
              >
                <i className="fa fa-users me-2"></i>
                <span className={!sidebarOpen ? 'd-none' : ''}>Teams</span>
              </Nav.Link>
            </li>
          </ul>

          <div className={`px-3 mt-4 mb-2 ${!sidebarOpen ? 'd-none' : ''}`}>
            <h6 className="text-uppercase text-muted section-header">
              Admin Tools
            </h6>
            <hr className="section-divider" />
          </div>
          <ul className="list-group list-group-flush">
           {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Workflows")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Workflows" : ''}`}
                  className="nav-link"
                  as={NavLink}
                  to="/newDS/workflows"
                  data-tooltip="Workflows"
                >
                  <span className="fa fa-tasks me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Workflows</span>
                </Nav.Link>
              </li>
            )}
            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Workflow-Management")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Workflow Management" : ''}`}
                  className="nav-link"
                  as={NavLink}
                  to="/newDS/workflow-management"
                  data-tooltip="Manageme workflows"
                >
                  <span className="fa fa-sitemap me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Manage Workflows</span>
                </Nav.Link>
              </li>
            )}
            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Dashboard")) && (
              <li className="list-group-item">
                {/*Dashboard */}
                <Nav.Link
                  title={`${!sidebarOpen ? "Dashboard" : ''}`}
                  as={NavLink}
                  to="/newDS/home"
                  className="nav-link"
                  data-tooltip="Dashboard"
                >
                  <span className="fa fa-dashboard me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Dashboard</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Reports/Duplicate-Report") ||
              permisionsArray.includes("Reports/Pending-Workflows-Report") ||
              permisionsArray.includes("Reports/Retention-End-Report") ||
              permisionsArray.includes("Reports/Due-Date-Report") ||
              permisionsArray.includes("Reports/Archived-Docs-Report") ||
              permisionsArray.includes("Reports/All-Files-Report") ||
              permisionsArray.includes("Reports/Users-Report") ||
              permisionsArray.includes("Reports/Specific-Doc-Log-Report")) && (
              <li className="list-group-item">
                <div className="nav-item" title={`${!sidebarOpen ? "Reports" : ''}`}>
                  <a
                    className="nav-link cursor-pointer"
                    onClick={() => {
                      setIsReportsOpen(!isReportsOpen);
                      if (!sidebarOpen) {
                        setSidebarOpen(true);
                        // Remember that sidebar was initially closed
                        window.wasSidebarInitiallyClosed = true;
                      } else {
                        window.wasSidebarInitiallyClosed = false;
                      }
                    }}
                    data-tooltip="Reports"
                  >
                    <span className="fa fa-flag me-2"></span>
                    <span className={!sidebarOpen ? 'd-none' : ''}>Reports</span>
                    <i
                      className={`fa fa-chevron-${
                        isReportsOpen ? "down" : "right"
                      } ${!sidebarOpen ? 'd-none' : ''}`}
                    ></i>
                  </a>
                  {isReportsOpen && (
                    <div className="dropdown-content open">
                      <Nav className="flex-column text-start">
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Duplicate-Report"
                          )) && (
                          <SubMenuItem to="/newDS/duplicatesReport">
                            Duplicate Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Pending-Workflows-Report"
                          )) && (
                          <SubMenuItem to="/newDS/pendingWorkFlows">
                            Pending Workflows Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Retention-End-Report"
                          )) && (
                          <SubMenuItem to="/newDS/retentionReport">
                            Retention End Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Due-Date-Report"
                          )) && (
                          <SubMenuItem to="/newDS/duedateReport">
                            Due Date Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Archived-Docs-Report"
                          )) && (
                          <SubMenuItem to="/newDS/archivedReport">
                            Archived Docs Report
                          </SubMenuItem>
                        )}

                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/All-Files-Report"
                          )) && (
                          <SubMenuItem to="/newDS/allfilesReport">
                            All Files Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes("Reports/Users-Report")) && (
                          <SubMenuItem to="/newDS/usersReport">
                            Users Report
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Reports/Specific-Doc-Log-Report"
                          )) && (
                          <SubMenuItem to="/newDS/specificDocLogReport">
                            Specific Doc Log Report
                          </SubMenuItem>
                        )}
                      </Nav>
                    </div>
                  )}
                </div>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("UserManagement/Manage-Users") ||
              permisionsArray.includes("UserManagement/Manage-Roles") ||
              permisionsArray.includes("UserManagement/Permissions") ||
              permisionsArray.includes("UserManagement/Password") ||
              permisionsArray.includes("UserManagement/Teams")) && (
              <li className="list-group-item" title={`${!sidebarOpen ? "User Management" : ''}`}>
                <div className="nav-item">
                  <a
                    className="nav-link cursor-pointer"
                    onClick={() => {
                      setIsUserManagementOpen(!isUserManagementOpen);
                      if (!sidebarOpen) {
                        setSidebarOpen(true);
                        // Remember that sidebar was initially closed
                        window.wasSidebarInitiallyClosed = true;
                      } else {
                        window.wasSidebarInitiallyClosed = false;
                      }
                    }}
                    data-tooltip="User Management"
                  >
                    <span className="fa fa-users me-2"></span>
                    <span className={!sidebarOpen ? 'd-none' : ''}>User Management</span>
                    <i
                      className={`fa fa-chevron-${
                        isUserManagementOpen ? "down" : "right"
                      } ${!sidebarOpen ? 'd-none' : ''}`}
                    ></i>
                  </a>
                  {isUserManagementOpen && (
                    <div className="dropdown-content open">
                      <Nav className="flex-column text-start">
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "UserManagement/Manage-Users"
                          )) && (
                          <SubMenuItem to="/newDS/createUser">
                            Manage Users
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "UserManagement/Password"
                          )) && (
                          <SubMenuItem to="/newDS/password">
                            Password Management
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "UserManagement/Manage-Roles"
                          )) && (
                          <SubMenuItem to="/newDS/createRole">
                            Roles Management
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "UserManagement/Permissions"
                          )) && (
                          <SubMenuItem to="/newDS/permissionManagement">
                            Permission Management
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes("UserManagement/Teams")) && (
                          <SubMenuItem to="/newDS/teams">
                            Manage User Teams
                          </SubMenuItem>
                        )}
                      </Nav>
                    </div>
                  )}
                </div>
              </li>
            )}
            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Recycle-Bin")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Recycle Bin" : ''}`}
                  as={NavLink}
                  to="/newDS/recyclebin"
                  className="nav-link"
                  data-tooltip="Recycle Bin"
                >
                  <span className="fa fa-trash me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Recycle Bin</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Archived-docs")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Achived Docs" : ''}`}
                  as={NavLink}
                  to="/newDS/archived"
                  className="nav-link"
                  data-tooltip="Archived Docs"
                >
                  <span className="fa fa-archive me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Archived docs</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Numbering")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Numbering" : ''}`}
                  className="nav-link"
                  as={NavLink}
                  to="/newDS/numbering"
                  data-tooltip="Numbering"
                >
                  <span className="fa fa-list-ol me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Numbering</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Metadata")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Meta Data" : ''}`}
                  className="nav-link"
                  as={NavLink}
                  to="/newDS/metadata"
                  data-tooltip="Metadata"
                >
                  <span className="fa fa-table me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Metadata</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Tags")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Tags" : ''}`}
                  className="nav-link"
                  as={NavLink}
                  to="/newDS/tags"
                  data-tooltip="Tags"
                >
                  <span className="fa fa-tags me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Tags</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Audit-Log")) && (
              <li className="list-group-item">
                <Nav.Link
                  title={`${!sidebarOpen ? "Audit Log" : ''}`}
                  as={NavLink}
                  to="/newDS/auditlog"
                  className="nav-link"
                  data-tooltip="Audit Log"
                >
                  <span className="fa fa-asterisk me-2"></span>
                  <span className={!sidebarOpen ? 'd-none' : ''}>Audit Log</span>
                </Nav.Link>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Configuration/Company")) && (
              <li className="list-group-item" title={`${!sidebarOpen ? "Configuration" : ''}`}>
                <div className="nav-item">
                  <a
                    className="nav-link cursor-pointer"
                    onClick={() => {
                      setIsConfigurationOpen(!isConfigurationOpen);
                      if (!sidebarOpen) {
                        setSidebarOpen(true);
                        // Remember that sidebar was initially closed
                        window.wasSidebarInitiallyClosed = true;
                      } else {
                        window.wasSidebarInitiallyClosed = false;
                      }
                    }}
                    data-tooltip="Configuration"
                  >
                    <span className="fa fa-cogs me-2"></span>
                    <span className={!sidebarOpen ? 'd-none' : ''}>Configuration</span>
                    <i
                      className={`fa fa-chevron-${
                        isConfigurationOpen ? "down" : "right"
                      } ${!sidebarOpen ? 'd-none' : ''}`}
                    ></i>
                  </a>
                  {isConfigurationOpen && (
                    <div className="dropdown-content open">
                      <Nav className="flex-column text-start">
                        {(role === "ROLE_SUPER_ADMIN" ||
                          permisionsArray.includes(
                            "Configuration/Company"
                          )) && (
                          <SubMenuItem to="/newDS/company">
                            Company
                          </SubMenuItem>
                        )}
                        {(role === "ROLE_SUPER_ADMIN" ||
                        permisionsArray.includes("Configuration/File-Formats")) && (
                          <Nav className="flex-column text-start">
                            <SubMenuItem to="/newDS/fileformate">
                              File Settings
                            </SubMenuItem>
                          </Nav>
                        )}
                      </Nav>
                    </div>
                  )}
                </div>
              </li>
            )}

            {(role === "ROLE_SUPER_ADMIN" ||
              permisionsArray.includes("Notification/Email") ||
              permisionsArray.includes("Notification/SMS") ||
              permisionsArray.includes(
                "Notification/Notification-Settings"
              )) && (
              <li className="list-group-item" title={`${!sidebarOpen ? "Notifications" : ''}`}>
                <div className="nav-item">
                  <a
                    className="nav-link cursor-pointer"
                    onClick={() => {
                      setIsNotificationsOpen(!isNotificationsOpen);
                      if (!sidebarOpen) {
                        setSidebarOpen(true);
                        // Remember that sidebar was initially closed
                        window.wasSidebarInitiallyClosed = true;
                      } else {
                        window.wasSidebarInitiallyClosed = false;
                      }
                    }}
                    data-tooltip="Notifications"
                  >
                    <span className="fa fa-bell me-2"></span>
                    <span className={!sidebarOpen ? 'd-none' : ''}>Notifications</span>
                    <i
                      className={`fa fa-chevron-${
                        isNotificationsOpen ? "down" : "right"
                      } ${!sidebarOpen ? 'd-none' : ''}`}
                    ></i>
                  </a>
                  {isNotificationsOpen && (
                    <div className="dropdown-content open">
                      {(role === "ROLE_SUPER_ADMIN" ||
                        permisionsArray.includes("Notification/Email")) && (
                          <Nav className="flex-column text-start">
                            <SubMenuItem to="/newDS/email">
                              Email
                            </SubMenuItem>
                          </Nav>
                        )}
                      {(role === "ROLE_SUPER_ADMIN" ||
                        permisionsArray.includes("Notification/SMS")) && (
                          <Nav className="flex-column">
                            <SubMenuItem to="/newDS/SMS">
                              SMS
                            </SubMenuItem>
                          </Nav>
                        )}
                      {(role === "ROLE_SUPER_ADMIN" ||
                        permisionsArray.includes(
                          "Notification/Notification-Settings"
                        )) && (
                          <Nav className="flex-column">
                            <SubMenuItem to="/newDS/notificationSettings">
                              Notification Settings
                            </SubMenuItem>
                          </Nav>
                        )}
                    </div>
                  )}
                </div>
              </li>
            )}
          </ul>
        </div>

        {/* Main Content */}
        <main className={`content ${sidebarOpen ? '' : 'content-expanded'}`}>
          <Outlet />
        </main>
      </div>

      <Modal
        show={showModal}
        onHide={handleClose}
        size="md"
        centered
        className="p-3"
      >
        <Modal.Header
          closeButton
          className="modal-header-modern"
        >
          <Modal.Title style={{ fontSize: "18px", color: "white" }}>
            Change Password
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group>
              <div className="input-group">
                <Form.Control
                  type={showCurrentPassword ? "text" : "password"}
                  placeholder="Enter current password"
                  value={currentPassword}
                  onChange={handleCurrentPassword}
                  required
                />
                <Button
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <i class="fa fa-eye-slash"></i>
                  ) : (
                    <i className="fa fa-eye"></i>
                  )}
                </Button>
              </div>
              <br></br>
            </Form.Group>
            {result && (
                  <p className="text-danger">
                    Current Password does not match with DB
                  </p>
                )}
            <Form.Group>
              <div className="input-group">
                <Form.Control
                  type={showNewPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  value={newPassword}
                  onChange={handleConfirmNewPassword}
                  required
                />
                <Button onClick={() => setShowNewPassword(!showNewPassword)}>
                  {showNewPassword ? (
                    <i class="fa fa-eye-slash"></i>
                  ) : (
                    <i className="fa fa-eye"></i>
                  )}
                </Button>
              </div>
            </Form.Group>
            <br></br>
            <Form.Group>
              <div className="input-group">
                <Form.Control
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Re-enter new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
                <Button
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <i class="fa fa-eye-slash"></i>
                  ) : (
                    <i className="fa fa-eye"></i>
                  )}
                </Button>
              </div>
              {isPasswordMissMatch && (
                  <h6 className="text-danger">Please Confirm New Password</h6>
              )}
            </Form.Group>
            <br></br>
            <Button variant="primary" type="submit">
              Submit
            </Button>
          </Form>
        </Modal.Body>
      </Modal>

      <Modal
        show={notificationModalOpen}
        onHide={handleCloseNotificationModal}
        size="xl"
        className="p-3"
      >
        <Modal.Header
          closeButton
          className="modal-header-modern"
        >
          <Modal.Title style={{ fontSize: "18px", color: "white" }}>
            All Notifications
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          {/* Search Input */}
          <div className="p-3 border-bottom">
            <input
              type="text"
              className="form-control"
              placeholder="Search notifications..."
              onChange={(e) => setSearchTerm(e.target.value)}
              value={searchTerm}
            />
          </div>

          {/* Notifications List */}
          {filteredNotifications.length > 0 ? (
            <>
              <div className="list-group notification-list">
                {filteredNotifications
                  .slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage
                  )
                  .map((notification, index) => (
                    <div
                      key={notification.id}
                      className={`list-group-item list-group-item-action d-flex justify-content-between align-items-center ${
                        index % 2 === 0 ? "" : "bg-light"
                      }`}
                    >
                      <div className="d-flex flex-column flex-grow-1">
                        <div
                          className="notification-message"
                          style={{ color: "blue", fontWeight: "600" }}
                        >
                          {notification.notificationMessage}
                        </div>
                        <div className="notification-date text-muted small mt-1">
                          {formatDate(notification.createdDate)}
                        </div>
                      </div>
                      <button
                        className="btn text-danger p-0 ms-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDismissNotification(notification.id);
                        }}
                        aria-label="Dismiss notification"
                      >
                        <span style={{ fontSize: "2rem" }}>&times;</span>
                      </button>
                    </div>
                  ))}
              </div>

            {/* Enhanced Pagination Footer with Ellipses */}
{filteredNotifications.length > itemsPerPage && (
  <div className="d-flex flex-column flex-md-row justify-content-between align-items-center p-3 border-top gap-3">
    {/* Left-aligned items */}
    <div className="d-flex flex-column flex-md-row align-items-center gap-3">
      {/* Items per page selector */}
      <div className="d-flex align-items-center">
        <span className="me-2">Items per page:</span>
        <select
          className="form-select form-select-sm w-auto"
          value={itemsPerPage}
          onChange={(e) => {
            setItemsPerPage(Number(e.target.value));
            setCurrentPage(1);
          }}
        >
          <option value="5">5</option>
          <option value="10">10</option>
        </select>
      </div>

      |

      {/* Results count */}
      <div className="text-muted">
        Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(
          currentPage * itemsPerPage,
          filteredNotifications.length
        )} of {filteredNotifications.length} results
      </div>
    </div>

    {/* Right-aligned pagination controls */}
    <nav className="ms-md-auto">
      <ul className="pagination mb-0">
        {/* Previous Page Button */}
        <li className={`page-item ${currentPage === 1 && "disabled"}`}>
          <a
            className="page-link border-0 bg-transparent text-primary"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              if (currentPage > 1) setCurrentPage(currentPage - 1);
            }}
            style={{ textDecoration: "none", boxShadow: "none" }}
          >
            {"<"}
          </a>
        </li>

        {/* First Page */}
        <li className={`page-item ${currentPage === 1 && "active"}`}>
          <a
            className="page-link border-0 bg-transparent text-primary"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setCurrentPage(1);
            }}
            style={{
              textDecoration: "none",
              boxShadow: "none",
              ...(currentPage === 1 && {
                fontWeight: "bold",
                color: "#0d6efd",
                textDecoration: "underline",
              }),
            }}
          >
            1
          </a>
        </li>

        {/* Ellipsis before current page if needed */}
        {currentPage > 3 && (
          <li className="page-item disabled">
            <span className="page-link border-0 bg-transparent">...</span>
          </li>
        )}

        {/* Page before current if exists */}
        {currentPage > 2 && (
          <li className="page-item">
            <a
              className="page-link border-0 bg-transparent text-primary"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                setCurrentPage(currentPage - 1);
              }}
              style={{ textDecoration: "none", boxShadow: "none" }}
            >
              {currentPage - 1}
            </a>
          </li>
        )}

        {/* Current Page if not first or last */}
        {currentPage > 1 && currentPage < Math.ceil(filteredNotifications.length / itemsPerPage) && (
          <li className="page-item active">
            <a
              className="page-link border-0 bg-transparent text-primary"
              href="#"
              style={{
                fontWeight: "bold",
                color: "#0d6efd",
                textDecoration: "underline",
                boxShadow: "none",
              }}
            >
              {currentPage}
            </a>
          </li>
        )}

        {/* Page after current if exists */}
        {currentPage < Math.ceil(filteredNotifications.length / itemsPerPage) - 1 && (
          <li className="page-item">
            <a
              className="page-link border-0 bg-transparent text-primary"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                setCurrentPage(currentPage + 1);
              }}
              style={{ textDecoration: "none", boxShadow: "none" }}
            >
              {currentPage + 1}
            </a>
          </li>
        )}

        {/* Ellipsis after current page if needed */}
        {currentPage < Math.ceil(filteredNotifications.length / itemsPerPage) - 2 && (
          <li className="page-item disabled">
            <span className="page-link border-0 bg-transparent">...</span>
          </li>
        )}

        {/* Last Page */}
        {Math.ceil(filteredNotifications.length / itemsPerPage) > 1 && (
          <li className={`page-item ${
            currentPage === Math.ceil(filteredNotifications.length / itemsPerPage) && "active"
          }`}>
            <a
              className="page-link border-0 bg-transparent text-primary"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                setCurrentPage(Math.ceil(filteredNotifications.length / itemsPerPage));
              }}
              style={{
                textDecoration: "none",
                boxShadow: "none",
                ...(currentPage === Math.ceil(filteredNotifications.length / itemsPerPage) && {
                  fontWeight: "bold",
                  color: "#0d6efd",
                  textDecoration: "underline",
                }),
              }}
            >
              {Math.ceil(filteredNotifications.length / itemsPerPage)}
            </a>
          </li>
        )}

        {/* Next Page Button */}
        <li className={`page-item ${
          currentPage === Math.ceil(filteredNotifications.length / itemsPerPage) && "disabled"
        }`}>
          <a
            className="page-link border-0 bg-transparent text-primary"
            href="#"
            onClick={(e) => {
              e.preventDefault();
              if (currentPage < Math.ceil(filteredNotifications.length / itemsPerPage)) {
                setCurrentPage(currentPage + 1);
              }
            }}
            style={{ textDecoration: "none", boxShadow: "none" }}
          >
            {">"}
          </a>
        </li>
      </ul>
    </nav>
  </div>
)}
            </>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted">
                {searchTerm
                  ? "No matching notifications found"
                  : "No notifications available"}
              </p>
            </div>
          )}
        </Modal.Body>
      </Modal>

      <Modal
        show={validationModalOpen}
        size="lg"
        centered
        className="p-3"
      >
        <Modal.Header
          className="modal-header-modern"
        >
          <Modal.Title style={{ fontSize: "18px", color: "white" }}>
            User, Not Found!
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-danger">
            <h4> This user account is no longer valid! Logging out in 3 seconds! </h4>
          </div>
        </Modal.Body>
      </Modal>
      {notification.show && <Notification message={notification.message} type={notification.type} onClose={closeNotification}/>}
    </div>
  );
}

export default NewDashboard;
