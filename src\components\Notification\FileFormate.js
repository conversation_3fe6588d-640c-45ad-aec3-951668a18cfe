import React, { Component } from "react";
import classes from "./Email.module.css";
import Notification from "./Notification";
import { addNew, getList, editById } from "../../services/apiService";
import Loader from "../loader/Loader";

class FileFormate extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      id:null,
      emailId: null,
      fileformates: "",
      showPassword: false,
      errors: {},
      testEmailError: "",
      notification: {
        message: "",
        type: "",
        show: false,
      },
      isEditMode: false,
    };
  }

  componentDidMount() {
    this.fetchEmailList();
  }

  fetchEmailList = async () => {
    const api = `/managefileformate/list`;
    try {
      const response = await getList(api);
      const data = response.data;
      if (data) {
        this.setState({
          fileformates: data.fileformates,
          id:data.id,
          isEditMode: true,
        });
      }
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  handleChange = (event) => {
    this.setState({
      [event.target.name]: event.target.value,
      errors: { ...this.state.errors, [event.target.name]: "" },
      testEmailError: "",
    });
  };

  handleSave = () => {
    const { fileformates} = this.state;
    let errors = {};
    if (!fileformates) errors.email = "File Format is required";
   

    if (Object.keys(errors).length > 0) {
      this.setState({ errors });
      return;
    }
    let obj = {
      fileformates: this.state.fileformates,
     };
    console.log("Saved Data:", obj);
    if (this.state.isEditMode) {
      this.updateData(obj);
    } else {
      this.submitData(obj);
    }
  };

  submitData = async (obj) => {
    const api = "/managefileformate";
    try {
      const response = await addNew(api, obj);
      this.setState({
        notification: {
          message: "File Format Saved Successfully",
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  updateData = async (obj) => {
    const api = `/managefileformate/${this.state.id}`;
    try {
      const response = await editById(api, obj);
      this.setState({
        notification: {
          message: "File Format updated Successfully",
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleClear = () => {
    this.setState({
      fileformates:"",
    });
  };

  togglePasswordVisibility = () => {
    this.setState((prevState) => ({ showPassword: !prevState.showPassword }));
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  diagnose = async () => {
    this.setState({ isLoading: true });
    const { testEmail } = this.state;
    if (testEmail === "") {
      this.setState({ testEmailError: "Email is required" });
      return;
    }
    console.log("Test Email:", this.state.testEmail);


      const api = `/manageemail/validateEmail?testEmail=${this.state.testEmail}`;
        try {
          const response = await getList(api);
          //alert(response);
          if(response){
        this.setState({
        notification: {
          message: "Mail working fine",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
     // this.componentDidMount();
      return response.data;
    }else{
      this.setState({
        notification: {
          message: "Please Enter valid email and passcode",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
      //throw error;
    }
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong please check mail details once",
          type: "error",
          show: true,
        },
      });
      throw error;
    }


  };

  render() {
    return (
      <div className={`${classes.bgColor} container mt-3`}>
        {/* <div className="row text-center">
          <div className="col-12">
            <h4>Email Settings</h4>
            <hr />
          </div>
        </div> */}

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 p-3 border rounded">
            <h4>
              <i className="fa fa-user"></i>&nbsp;File Settings
            </h4>
            <hr />
            <div className="row mt-3">
              <div className="col-md-6">
                <label>
                  Formats <span style={{ color: "red" }}>*</span>
                </label>
                <input
                  type="fileformates"
                  name="fileformates"
                  value={this.state.fileformates}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Formats"
                />
                {this.state.errors.email && (
                  <span className="text-danger">{this.state.errors.email}</span>
                )}
              </div>
              {/* <div className="col-md-6">
                <label>
                  Size <span style={{ color: "red" }}>*</span>
                </label>
                <input
                  type="fileformates"
                  name="fileformates"
                  value={this.state.fileformates}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Enter file size limit for upload files "
                />
                {this.state.errors.email && (
                  <span className="text-danger">{this.state.errors.email}</span>
                )}
              </div> */}

              {/* <div className="col-md-6 mt-3 mt-md-0">
                <label>
                  Name <span style={{ color: "red" }}>*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={this.state.name}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Name"
                />
                {this.state.errors.name && (
                  <span className="text-danger">{this.state.errors.name}</span>
                )}
              </div> */}

              {/* <div className="col-md-6 mt-3">
                <label>
                  Passkey <span style={{ color: "red" }}>*</span>
                </label>
                <div className="input-group">
                  <input
                    type={this.state.showPassword ? "text" : "password"}
                    name="password"
                    value={this.state.password}
                    onChange={this.handleChange}
                    className="form-control"
                    placeholder="Passkey"
                  />
                  <div className="input-group-append">
                    <span
                      className="btn btn-outline-gray"
                      type="button"
                      onClick={this.togglePasswordVisibility}
                    >
                      {this.state.showPassword ? (
                        <i className="fa fa-eye-slash"></i>
                      ) : (
                        <i className="fa fa-eye"></i>
                      )}
                    </span>
                  </div>
                </div>
                {this.state.errors.password && (
                  <span className="text-danger">
                    {this.state.errors.password}
                  </span>
                )}
              </div>

              <div className="col-md-6 mt-3">
                <label>Test Email</label>
                <input
                  type="email"
                  name="testEmail"
                  value={this.state.testEmail}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Test Email"
                />
                {this.state.testEmailError && (
                  <span className="text-danger">
                    {this.state.testEmailError}
                  </span>
                )}
              </div> */}
            </div>
            

            <div className="row mt-3">
              <div className="col-12 d-flex justify-content-start">
                <button
                  className="btn btn-primary mr-2"
                  onClick={this.handleSave}
                >
                  {this.state.isEditMode ? "Update" : "Save"}
                </button>&nbsp;&nbsp;
                <button
                  className="btn btn-danger mr-2"
                  onClick={this.handleClear}
                >
                  Clear
                </button>&nbsp;&nbsp;
                {/* <button className="btn btn-secondary" onClick={this.diagnose}>
                  Diagnose Test Mail
                </button> */}
              </div>
            </div>
          </div>
        </div>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default FileFormate;
