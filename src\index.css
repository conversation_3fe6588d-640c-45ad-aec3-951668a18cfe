body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

::placeholder {
  color: #ababab !important; /* Change placeholder text color */
  font-size: 16px; /* Adjust the font size */
  font-style: italic; /* Example: change font style to italic */
}

.cardHeader {
  background-color: #ffffff !important; /* Light grey background */
  color: #017efa !important; /* Dark text color */
  font-size: 20px; /* Font size for the header text */
  font-weight: 600; /* Bold text for the header */
  border-bottom: none !important; /* No border at the bottom */
}

.cardHeader-rtn {
  background-color: #ffffff !important; /* Light grey background */
  color: #29CE7F !important; /* Dark text color */
  font-size: 20px; /* Font size for the header text */
  font-weight: 600; /* Bold text for the header */
  border-bottom: none !important; /* No border at the bottom */
}

.cardHeader-shared {
  background-color: #ffffff !important; /* Light grey background */
  color: #FD1F9B !important; /* Dark text color */
  font-size: 20px; /* Font size for the header text */
  font-weight: 600; /* Bold text for the header */
  border-bottom: none !important; /* No border at the bottom */
}

.tableHeader {
  background-color: #ebf0fa !important; /* Light grey background */
  color: #000 !important; /* Dark text color */
  font-size: 15px; /* Font size for the header text */
  font-weight: 700; /* Bold text for the header */
}

.lockFileBtn {
  width: 140;
  height: 35;
  top: 106px;
  left: 290px;
  border-radius: 5px;
  background: #017efa;
  color: #ffffff;
}

.signBtn {
  width: 180px;
  height: 110px;
  top: 158px;
  left: 1279px;
  border-radius: 8px;
  background: red;
}

.boxShadow {
  box-shadow: 0 2px 0 2px #f1f1f1;
  border: none
}