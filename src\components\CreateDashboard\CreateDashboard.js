import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Mo<PERSON>, Button } from 'react-bootstrap';
import styles from './CreateDashboard.module.css';
import CustomBreadcrumb from '../common/CustomBreadcrumb';
import { availableWidgets } from '../../config/widgetConfig';
import Notification from '../Notification/Notification';
import { getList } from '../../services/apiService';
import Select from 'react-select';

function CreateDashboard() {
  const [notification, setNotification] = useState({
    message: "",
    type: "",
    show: false,
  });
  const navigate = useNavigate();
  const [dashboardName, setDashboardName] = useState('');
  const [dashboardDescription, setDashboardDescription] = useState('');
  const [selectedWidgets, setSelectedWidgets] = useState([]);
  const [userList, setUserList] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [reportList] = useState([
    'Total Files', 'Duplicate Files', 'Pending Workflows Files', 
    'Due Date Files', 'Archive Files', 'Retention End Files'
  ]);

  const [validateError,setValidateError] = useState(false);
  
  // State for modal and widget reports assignment
  const [showModal, setShowModal] = useState(false);
  const [currentWidget, setCurrentWidget] = useState(null);
  const [widgetReports, setWidgetReports] = useState({});
  
  const closeNotification = () => {
    setNotification({ message: "", type: "", show: false });
  };

  // Initialize reports for a widget when selected
  const initializeWidgetReports = (widgetId) => {
    if (!widgetReports[widgetId]) {
      setWidgetReports(prev => ({
        ...prev,
        [widgetId]: []
      }));
    }
  };

  useEffect(() => {
    fetchUserList();
  }, []);

  const fetchUserList = async () => {
    const api = "/user/list?page=0&size=300&search=&sort=";
    try {
      const data = await getList(api);
      const filteredList = data.data.content
      .filter(user => user.id != localStorage.getItem("id"))
      .map(user => ({
        value: user.id,
        label: `${user.firstName} ${user.lastName} (${user.email})`,
        userData: user
      }));
      //const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      setUserList(filteredList);
    } catch (error) { }
  };

  const handleWidgetToggle = (widgetId) => {
    const isSelected = selectedWidgets.includes(widgetId);
    
    if (!isSelected) {
      setCurrentWidget(widgetId);
      initializeWidgetReports(widgetId);
      setShowModal(true);
    } else {
      setSelectedWidgets(prev => prev.filter(id => id !== widgetId));
      // Remove the reports when unselecting
      setWidgetReports(prev => {
        const newReports = {...prev};
        delete newReports[widgetId];
        return newReports;
      });
    }
    closeNotification();
    setValidateError(false);
  };

  const handleReportToggle = (widgetId, report) => {
    setWidgetReports(prev => {
      const currentReports = prev[widgetId] || [];
      const updatedReports = currentReports.includes(report)
        ? currentReports.filter(r => r !== report)
        : [...currentReports, report];
      
      return {
        ...prev,
        [widgetId]: updatedReports
      };
    });
  };

  const handleModalConfirm = () => {
    if (!selectedWidgets.includes(currentWidget)) {
      setSelectedWidgets(prev => [...prev, currentWidget]);
    }
    setShowModal(false);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  const validateWidgetReports = () => {
    for (const widgetId in widgetReports) {
      if (widgetReports[widgetId].length < 2) {
        const widget = availableWidgets.find(w => w.id === widgetId);
        setValidateError(true);
        setNotification({
          message: `${widget.name}: must contain at least 2 reports to compare data`,
          type: "error",
          show: true,
        });
        return false;
      }
    }
    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate widget reports
    if (!validateWidgetReports()) {
      return;
    }

    const dashboardData = {
      dashboardName,
      dashboardDescription,
      selectedWidgets,
      widgetReports,
      assignedUsers: selectedUsers.map(user => user.value) // Send just the user IDs
    };

    console.log('Dashboard Data:', dashboardData);
    navigate('/newDS/dashboardView', { state: { dashboardData } });
  };

  return (
    <div className="container-fluid mt-4">
      <div className="d-flex align-items-center justify-content-end mb-4">
        <CustomBreadcrumb
          companyName={localStorage.getItem("companyName") || "Company"}
          featureName="Create Dashboard"
        />
      </div>

      <div className="card shadow">
        <div className="card-header bg-primary text-white">
          <h4 className="mb-0">
            <i className="fa fa-plus me-2"></i>
            Create New Dashboard
          </h4>
        </div>
        <div className="card-body">          
          <form onSubmit={handleSubmit}>
            {/* Dashboard Basic Information */}
            <div className="row mb-4">
              <div className="col-md-4">
                <label htmlFor="dashboardName" className="form-label">
                  <strong>Dashboard Name</strong>
                  <span style={{ color: 'red', marginLeft: '4px' }}>*</span>
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="dashboardName"
                  value={dashboardName}
                  onChange={(e) => setDashboardName(e.target.value)}
                  placeholder="Enter dashboard name"
                  required
                />
              </div>
              <div className="col-md-8">
                <label htmlFor="dashboardDescription" className="form-label">
                  <strong>Description</strong>
                </label>
                <textarea
                  className="form-control"
                  id="dashboardDescription"
                  rows="3"
                  value={dashboardDescription}
                  onChange={(e) => setDashboardDescription(e.target.value)}
                  placeholder="Enter dashboard description"
                />
              </div>
            </div>

            <div className="mb-4">
              <h5 className="mb-3">
                <i className="fa fa-users me-2"></i>
                Share With Users
              </h5>
              <Select
                isMulti
                options={userList}
                value={selectedUsers}
                onChange={setSelectedUsers}
                placeholder="Search and select users..."
                closeMenuOnSelect={false}
                // styles={customStyles}
                className="basic-multi-select"
                classNamePrefix="select"
                noOptionsMessage={() => "No users found"}
              />
            </div>

            {/* Widget Selection */}
            <div className="mb-4">
              <h5 className="mb-3">
                <i className="fa fa-puzzle-piece me-2"></i>
                Select Widgets
              </h5>
              <div className="row">
                {availableWidgets.map(widget => (
                  <div key={widget.id} className="col-md-4 mb-3">
                    <div 
                      className={`card h-100 widget-card ${selectedWidgets.includes(widget.id) ? 'border-primary bg-primary bg-opacity-10' : 'border-light'}`}
                      style={{ cursor: 'pointer', transition: 'all 0.2s' }}
                      onClick={() => handleWidgetToggle(widget.id)}
                    >
                      <div className="card-body text-center">
                        <div className="form-check d-flex align-items-center justify-content-center mb-2">
                          <input
                            className="form-check-input me-2"
                            type="checkbox"
                            checked={selectedWidgets.includes(widget.id)}
                            onChange={() => handleWidgetToggle(widget.id)}
                            onClick={(e) => e.stopPropagation()}
                            style={{ transform: 'scale(1.2)' }}
                          />
                        </div>
                        <i className={`fa ${widget.icon} fa-2x text-primary mb-2`}></i>
                        <h6 className="card-title">{widget.name}</h6>
                        <p className="card-text text-muted small">{widget.description}</p>
                        {selectedWidgets.includes(widget.id) && widgetReports[widget.id]?.length > 0 && (
                          <div className="mt-2">
                            <small className="text-muted">
                              Reports: {widgetReports[widget.id].join(', ')}
                            </small>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Widgets Summary */}
            {selectedWidgets.length > 0 && (
              <div className="mb-4">
                <h6>Selected Widgets ({selectedWidgets.length}):</h6>
                <div className="d-flex flex-wrap gap-2">
                  {selectedWidgets.map(widgetId => {
                    const widget = availableWidgets.find(w => w.id === widgetId);
                    return (
                      <span key={widgetId} className="badge bg-primary">
                        <i className={`fa ${widget.icon} me-1`}></i>
                        {widget.name}
                        {widgetReports[widgetId]?.length > 0 && (
                          <span className="ms-1">({widgetReports[widgetId].length} reports)</span>
                        )}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="d-flex justify-content-between">
              <button 
                type="button" 
                className="btn btn-secondary"
                onClick={() => window.history.back()}
              >
                <i className="fa fa-arrow-left me-2"></i>
                Cancel
              </button>
              <button 
                type="submit" 
                className="btn btn-primary"
                disabled={!dashboardName.trim() || selectedWidgets.length === 0}
              >
                <i className="fa fa-save me-2"></i>
                Preview Dashboard
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Modal for assigning reports to widgets */}
      <Modal show={showModal} onHide={handleModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title style={{ fontSize: '1.05rem', fontWeight: 500 }}>
            Reports to display in this widget (Select at least 2 items)
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentWidget && (
            <div>
              <div className="list-group">
                {reportList.map(report => (
                  <div key={report} className="list-group-item">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={widgetReports[currentWidget]?.includes(report) || false}
                        onChange={() => handleReportToggle(currentWidget, report)}
                        id={`report-${currentWidget}-${report}`}
                      />
                      <label 
                        className="form-check-label" 
                        htmlFor={`report-${currentWidget}-${report}`}
                      >
                        {report}
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleModalClose}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleModalConfirm}
            disabled={
              !(
                currentWidget &&
                widgetReports[currentWidget] &&
                widgetReports[currentWidget].length >= 2
              )
            }
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>

      {notification.show &&
        <Notification 
        message={notification.message} 
        type={notification.type} 
        show={notification.show} 
        onClose={closeNotification}
      />
      }
      
    </div>
  );
}

export default CreateDashboard;