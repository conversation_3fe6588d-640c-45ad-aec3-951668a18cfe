import React, { Component } from "react";
import Notification from "../Notification/Notification";
import { getList, formatDate, getDisplayPath } from "../../services/apiService";
import { Modal, Button } from "react-bootstrap";
import { GlobalConstants } from "../../constants/global-constants";
import axios from "../../services/api";
import ReactPaginate from "react-paginate";
import Loader from "../loader/Loader";
import { DataTable } from "../Table/DataTable";

class Archived extends Component {
  state = {
    uploadedFiles: [],
    notification: {
      message: "",
      type: "",
      show: false,
    },
    isEmptyBinModalOpen: false,
    isRestoreAllModalOpen: false,
    isRestoreFileModalOpen: false,
    isDeleteFileModalOpen: false,
    restoreId: null,
    fileNameToRestore: "",
    fileIdToDelete: null,
    fileNameToDelete: "",
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    searchParam: "",
    isLoading: false,
  };
  componentDidMount() {
    this.fetchArchivedDocumentList();
  }
  fetchArchivedDocumentList = async (
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    this.setState({ isLoading: true });
    const api = `/documentsattachmentdetail/all_archived_files?page=${page}&size=${itemsPerPage}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        uploadedFiles: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchArchivedDocumentList(page);
  };

  handleItemsPerPageChange = (size) => {
    this.setState({ itemsPerPage: size }, () => {
      this.fetchArchivedDocumentList(0, size);
    });
  };

  restoreFile = async (id) => {
    console.log("File id : ", id);
    let api = `${GlobalConstants.globalURL}/job-scheduling/restore/${id}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Restored Successfully",
          type: "success",
          show: true,
        },
      });
      await this.fetchArchivedDocumentList(this.state.currentPage);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
    finally{
      this.closeRestoreFileModal();
    }
  };

  openRestoreFileModal = (file) => {
    this.setState({
      isRestoreFileModalOpen: true,
      restoreId: file.id,
      fileNameToRestore: file.documentName,
    });
  };
  closeRestoreFileModal = () => {
    this.fetchArchivedDocumentList();
    this.setState({
      isRestoreFileModalOpen: false,
      restoreId: null,
      fileNameToRestore: "",
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  render() {
    const { itemsPerPage, currentPage, totalItems, uploadedFiles } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    const columns = [
      {
        key: "index",
        header: "S No.",
        width: "80px",
        render: (_, row, index) => currentPage * itemsPerPage + index + 1,
      },
      {
        key: "documentName",
        header: "Name",
        sortable: true,
        render: (value, row) => (
          <button
            style={{
              color: "blue",
              textDecoration: "underline",
              cursor: "pointer",
              background: "none",
              border: "none",
            }}
          >
            {value}
          </button>
        ),
      },
      {
        key: "filePath",
        header: "Original Location",
        sortable: true,
        render: (value) => getDisplayPath(value),
      },
      {
        key: "jobDate",
        header: "Archived On",
        sortable: true,
        render: (value) => formatDate(value),
      },
      {
        key: "actions",
        header: "Actions",
        width: "120px",
        render: (_, row) => (
          <>
            <i
              className="fa fa-undo"
              style={{
                color: "blue",
                fontSize: "18px",
                cursor: "pointer",
              }}
              aria-hidden="true"
              title="Restore"
              onClick={() => this.openRestoreFileModal(row)}
            ></i>
          </>
        ),
      },
    ];

    return (
      <div className="container mt-3">
        <div className="row">
          <div className="col-sm-4 col-12">
            <h4>Archived Documents</h4>
          </div>
          <div className="col-sm-3 offset-sm-5 col-12">
            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <DataTable
              data={uploadedFiles}
              columns={columns}
              className="table-striped"
              itemsPerPage={itemsPerPage}
              totalItems={totalItems}
              currentPage={currentPage}
              onPageChange={this.handlePageChange}
              onItemsPerPageChange={this.handleItemsPerPageChange}
            />
          </div>
        </div>

        {/* Restore File Modal */}
        <Modal
          show={this.state.isRestoreFileModalOpen}
          onHide={this.closeRestoreFileModal}
        >
          <Modal.Header closeButton className="modal-header-modern"></Modal.Header>
          <Modal.Body>
            Do you want to restore the file
            <strong>" {this.state.fileNameToRestore}"</strong> ?
          </Modal.Body>
          <Modal.Footer>
            <div className="d-flex justify-content-between gap-2">
              <Button
                variant="primary"
                onClick={() => this.restoreFile(this.state.restoreId)}
              >
                Restore
              </Button>
              <Button variant="secondary" onClick={this.closeRestoreFileModal}>
                Close
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default Archived;
