import React, { Component } from "react";
import {
  <PERSON><PERSON><PERSON>rum<PERSON>,
  <PERSON><PERSON>,
  Card,
  Dropdown,
  Modal,
  Tab,
  Table,
  Tabs,
} from "react-bootstrap";
import { DataTable } from "../Table/DataTable";
import FileDetail from "./FileDetail";
import classes from "./DocumentManagement.module.css";
import folder from "../images/folder.png";
import {
  uploadDocument,
  getList,
  formatDate,
  addNew,
  deleteById,
  findById,
  getListOnly,
} from "../../services/apiService";
import { Navigate } from "react-router-dom";
import Notification from "../Notification/Notification";
import ReactPaginate from "react-paginate";
import NumberingModal from "../Numbering/NumberingModal/NumberingModal";
import NestedObjectTable from "./NestedObjectTable/NestedObjectTable";
import Loader from "../loader/Loader";
import FileDropZone from "./FileDropZone";
import CustomBreadcrumb from "../common/CustomBreadcrumb";
import FileUpdate from "./FileUpdate";

class DocumentManagement extends Component {
  intervalId = null;
  state = {
    isLoading: false,
    uploadType: "",
    showDropdown: false,
    uploadedFiles: [],
    approveUploadedFiles: [],
    acknowledgeUploadedFiles: [],
    esignUploadedFiles: [],
    showFileDetail: false,
    selectedFile: null,
    navigate: false,
    navigateToESign: false,
    toBeApprovedFile: null,
    isUploaded: false, // Track file upload completion
    notification: {
      message: "",
      type: "",
      show: false,
    },
    fileId: null,
    isDeleteModalOpen: false,
    uploadedFileName: "",
    fileUrl: null,
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 50,
    modalOpen: false,
    numberingListId: null,
    auditnavigate: false,
    searchParam: "",
    addLinkModalOpen: false,
    linkName: "",
    linkUrl: "",
    folderName: "",
    fileExtension:"",
    isFolderOpen: false,
    advSearchNavigate: false,
    linkNameError:"",
    linkUrlError:"",
    showFileUpdate: false,
  };

  toggleDropdown = () => {
    this.setState((prevState) => ({ showDropdown: !prevState.showDropdown }));
  };

  fileInputRef = React.createRef();
  folderInputRef = React.createRef();

  upload = () => {
    this.fileInputRef.current.click();
    this.setState({ uploadType: "file" });
  };

  triggerFileInput = () => {
    this.folderInputRef.current.click();
    this.setState({ uploadType: "folder" });
  };

  componentDidMount() {
    sessionStorage.setItem("from","inbox");
    this.fetchNumberingList();
    this.fetchDocumentList();
    this.fetchApproveDocumentList();
    this.fetchAcknowledgeDocumentList();
    this.fetchEsignDocumentList();

    this.intervalId = setInterval(() => {
      this.fetchApproveDocumentList();
      this.fetchAcknowledgeDocumentList();
      this.fetchEsignDocumentList();
    }, 60000);
    
    // Register global inbox navigation handler
    window.handleInboxNavigation = this.handleInboxNavigation;
    //this.setState({ fileId: sessionStorage.getItem("fileIdToDelete") });
  }

  componentWillUnmount() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    // Clean up global event handler
    if (window.handleInboxNavigation) {
      delete window.handleInboxNavigation;
    }
  }

  // deleteFile = async () => {
  //   let api = `${
  //     GlobalConstants.globalURL
  //   }/documentsattachmentdetail/${sessionStorage.getItem("fileIdToDelete")}`;
  //   try {
  //     const response = axios.delete(api);
  //     this.setState({
  //       notification: {
  //         message: "File Deleted Successfully",
  //         type: "success",
  //         show: true,
  //       },
  //     });
  //   } catch (error) {
  //     this.setState({
  //       notification: {
  //         message: "Something went wrong",
  //         type: "error",
  //         show: true,
  //       },
  //     });
  //   }
  //   this.closeDeleteModal();
  // };

  // permanentDeleteFile = async () => {
  //   let api = `${
  //     GlobalConstants.globalURL
  //   }/documentsattachmentdetail/perminentdelete/${sessionStorage.getItem(
  //     "fileIdToDelete"
  //   )}`;
  //   try {
  //     const response = axios.delete(api);
  //     this.setState({
  //       notification: {
  //         message: "File Deleted Successfully",
  //         type: "success",
  //         show: true,
  //       },
  //     });
  //   } catch (error) {
  //     this.setState({
  //       notification: {
  //         message: "Something went wrong",
  //         type: "error",
  //         show: true,
  //       },
  //     });
  //   }
  //   this.closeDeleteModal();
  // };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openDeleteModal = () => {
    this.setState({ isDeleteModalOpen: true });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false });
  };

  componentDidUpdate(prevProps, prevState) {
    if (
      this.state.isUploaded &&
      this.state.isUploaded !== prevState.isUploaded
    ) {
      this.fetchDocumentList();
    }
  }

  handleButtonClick = (file) => {
    this.setState({ navigate: true });
    //console.log(file)
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
  };

  handleAckButtonClick = (file) => {
    this.setState({ acknavigate: true });
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
  };

  handleFileChange = (event) => {
    const files = event.target.files;
    Array.from(files).forEach((file, index) => {
      if (file) {
        const fileData = {
          slno: this.state.uploadedFiles.length + 1,
          name: file.name,
          date: new Date().toLocaleString(),
          url: URL.createObjectURL(file),
        };
        this.setState((prevState) => ({
          uploadedFiles: [...prevState.uploadedFiles, fileData],
        }));
        event.target.value = null;
        const path = sessionStorage.getItem("currentPath");
        const formData = new FormData();
        formData.append("file", file);
        formData.append("documentName", file.name);
        formData.append("folderPath", path || "");
        this.setState({ uploadedFileName: file.name });
        formData.append("ownerName", localStorage.getItem("userName"));
        formData.append("employeeId", localStorage.getItem("id"));
        formData.append("uploadType", "file");
        formData.append("type", "file");
        formData.append("status", "inbox");
        formData.append("nodeId", 0);
        this.uploadDocument(formData);
        if (path) {
          setTimeout(() => {
            window.location.reload();
          },750);
          // this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
        }
      }
    });
  };

  handleSearchInput = (event) => {
    const searchParam = event.target.value;
    this.setState({ searchParam }, () => {
      //console.log(searchParam);
      this.fetchDocumentList();
    });
  };

  fetchDocumentList = async (
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee/" +
      userId +
      `?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api);
      const data = response.data;

      // Filter files with fileSize > 2 for notification count only
      const filteredList = data.content.filter((item) => item.fileSize > 2);

      // Update state with paginated data from the server
      this.setState(
        {
          uploadedFiles: data.content,
          totalItems: data.totalElements,
          currentPage: page,
          itemsPerPage,
          isLoading: false,
        },
        () => {
          // Update the notification count in parent component
          this.props.setUploadedFiles(filteredList.length);
        }
      );
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (selectedPage) => {
    // ReactPaginate uses 0-based indexing for pages
    this.fetchDocumentList(selectedPage.selected);
  };

  handleItemsPerPageChange = (newSize) => {
    // When items per page changes, reset to first page and fetch with new size
    this.setState({ itemsPerPage: newSize }, () => {
      this.fetchDocumentList(0, newSize);
    });
  };

  getApprovalWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc1/approve/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;
      //console.log("Approval Level Details:", approvalLevelDTO);

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchApproveDocumentList = async () => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/approve/" +
      userId;
    try {
      const data = await getList(api);
      //console.log(data);
      // approveUploadedFiles = data.data;
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getApprovalWorkFlowById(
            file.documentsAttachmentId
          );
          return {
            ...file,
            levelStatus: approvalData.levelStatus, // Attach levelStatus
            createdAt: approvalData.createdAt, // Attach createdAt
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );

      const sortedFiles = updatedFiles.sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });

      this.setState({ approveUploadedFiles: sortedFiles });

      //this.setState({ approveUploadedFiles: data.data });
      // this.setState({ uploadedFiles: data.data });
    } catch (error) {
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
    }
  };

  getEsignApprovalWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc1/eSign/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;
      //console.log("Approval Level Details:", approvalLevelDTO);

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        //console.log("Matching levelStatus:", matchingLevel.levelStatus);
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchEsignDocumentList = async () => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/eSign/" + userId;
    try {
      const data = await getList(api);
      //alert(JSON.stringify(data.data));
      //console.log(data)
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      // Use Promise.all to wait for all levelStatus and createdAt to be fetched and attached
      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getEsignApprovalWorkFlowById(
            file.documentsAttachmentId
          );

          return {
            ...file,
            levelStatus: approvalData.levelStatus, // Attach levelStatus
            createdAt: approvalData.createdAt, // Attach createdAt
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );


      const sortedFiles = updatedFiles.sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });

      // alert(JSON.stringify(updatedFiles))
      // Update the state with the files that now include levelStatus and createdAt
      this.setState({ esignUploadedFiles: sortedFiles });
    } catch (error) {
      //console.log("Error fetching document list:", error);
      // Optionally, you can update the state with an error message or notification
    }
  };

  getAckWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc1/acknowledgement/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        //console.log("Matching levelStatus:", matchingLevel.levelStatus);
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchAcknowledgeDocumentList = async () => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/acknowledgement/" +
      userId;
    try {
      const data = await getList(api);
      //console.log(data);
      // acknowledgeUploadedFiles = data.data;
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getAckWorkFlowById(
            file.documentsAttachmentId
          );
          return {
            ...file,
            levelStatus: approvalData.levelStatus,
            createdAt: approvalData.createdAt,
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );

      const sortedFiles = updatedFiles.sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return dateB - dateA;
      });

      this.setState({ acknowledgeUploadedFiles: sortedFiles });
      //this.setState({ acknowledgeUploadedFiles: data.data });
      // this.setState({ acknowledgeUploadedFiles: data.data });
    } catch (error) {
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
    }
  };

  uploadDocument = async (docData) => {
    const file = docData.get('file');
    if (file instanceof File) {
      const filename= file.name;
      const filesize=file.size;
      if(filesize > 20971520){
        this.setState({
          notification: {
            message: `${filename} exceeds the file size`,
            type: "error",
            show: true,
          },
        });
        return;
      }
    } 
   // console.log(" Uploading : ", JSON.stringify(docData));
  //  if(this.state.fileExtension === ""){
  //   // alert("no file format in the file")
  //   this.setState({
  //     notification: {
  //       message: `Uploaded file must be in correct format`,
  //       type: "error",
  //       show: true,
  //     },
  //   });
  //   return;
  //  }
    const api1 = `/managefileformate/validateFile?fileExtention=${this.state.fileExtension}`;
    const response = await getListOnly(api1);
    if(!response){
      this.setState({
         notification: {
           message: `${this.state.fileExtension} file format not configured`,
           type: "error",
           show: true,
         },
       });
      //throw error;
      //  setTimeout(() => {
      //    window.location.reload();
      //  }, 1000);
      return;
    }
    this.setState({ isLoading: true });
    const api = "/documentsattachmentdetail/saveDocument";
    try {
     
      const response = await uploadDocument(api, docData);
      console.log("After Uploading : ", response);
      // Add the new document directly to `uploadedFiles` instead of refetching
      const fileName = this.state.uploadedFileName;
      const value=fileName ? fileName : "Folder";
      this.setState({ isLoading: false });
      this.setState((prevState) => ({
        // uploadedFiles: [...prevState.uploadedFiles, response.data],
        notification: {
          message: ` ${value} uploaded successfully!`,
          type: "success",
          show: true,
        },
        isUploaded: true, // Triggers refetch if necessary
      }));
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
      return response.data;
    } catch (error) {
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
      //throw error;
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
    }
    this.setState({fileExtension:""})
    this.fetchDocumentList();
  };

  handleSelect = (eventKey) => {
    console.log(`Selected: ${eventKey}`);
    this.setState({ showDropdown: false });
  };

  openFileDetail = (file) => {
    //sessionStorage.setItem("fileIdToDelete", file.documentsAttachmentId);
    //alert(JSON.stringify(file));
    this.setState({ showFileDetail: true, selectedFile: file });
    console.log(file)
  };

  closeFileDetail = () => {
    this.setState({ showFileDetail: false, selectedFile: null });
  };

  openSignDocument = (file) => {
    // alert(JSON.stringify(file));
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
    this.setState({ navigateToESign: true });
  };

  fetchNumberingList = async () => {
    this.setState({ isLoading: true });
    const api = `/numbering/list`;
    try {
      const response = await getList(api);
      const activeNumbering = response.data.find((item) => !item.isDeleted);
      if (activeNumbering) {
        this.setState({
          numberingListId: activeNumbering.id,
          isLoading: false,
        });
      }
    } catch (error) {
      this.setState({ isLoading: false });
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
      //throw error;
    }
  };

  handleOpenModal = () => {
    this.setState({ modalOpen: true });
  };

  handleCloseModal = () => {
    setTimeout(() => {
      this.setState({ modalOpen: false }, () => {
        console.log("State after update:", this.state.modalOpen);
      });
    }, 0);
  };

  handleSubmitModal = async (data) => {
    //console.log("Numbering Data Submitted:", data);
    // if (data.isDeleted === true) {
    //   const id = data.id;
      // const api = `/numbering/${id}`;
      // //alert(api);
      // try {
      //   const response = await deleteById(api);
      //   this.setState({
      //     notification: {
      //       message: response.message,
      //       type: "success",
      //       show: true,
      //     },
      //     modalOpen: false,
      //   });
      //   return response.data;
      // } catch (error) {
      //   this.setState({
      //     notification: {
      //       message: "Something went Wrong",
      //       type: "error",
      //       show: true,
      //     },
      //   });
      //   //throw error;
      // }
    // } else if (data.auto === true) {
    //   console.log("Auto selected");
    //   this.setState({ modalOpen: false });
    // } else {
      const api = `/numbering`;
      console.log("-=====",data);
      try {
        const response = await addNew(api, data);
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          modalOpen: false,
        });
        return response.data;
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
          modalOpen: false,
        });
        //throw error;
      // }
    }
  };

  openWordDocument = () => {
    const wordUrl = "https://1drv.ms/w/s!AhVV6rIULp02b1p6aU4JwSota7U?e=NhDPgh";
    window.open(wordUrl, "_blank");
  };

  openExcelDocument = () => {
    const wordUrl = "https://1drv.ms/x/s!AhVV6rIULp02cR73Bxa7g6B_rZk?e=w12537";
    window.open(wordUrl, "_blank");
  };

  openpowerpointDocument = () => {
    const wordUrl = "https://1drv.ms/p/s!AhVV6rIULp02dV5D5g4Kg0SYh9E?e=XuKIsu";
    window.open(wordUrl, "_blank");
  };

  navigateAuditLog = () => {
    this.setState({ auditnavigate: true });
  };

  navigateAdvSearch = () => {
    this.setState({ advSearchNavigate: true });
  };

  handleAddLink = () => {
    this.setState({ addLinkModalOpen: true });
  };

  closeAddLinkModal = () => {
    this.setState({ addLinkModalOpen: false,linkUrl: "", linkName: "" });
  };

  handleLinkSubmit = async () => {
    const { linkName } = this.state;
    const path = sessionStorage.getItem("currentPath") || "";
    const fullPath = path ? `${path}/${linkName}` : linkName;

    console.log("New link Path:", fullPath);

    if(!linkName || !this.state.linkUrl){
      if(!linkName){
        this.setState({linkNameError:"Link Name is Required"})
      }
      if(!this.state.linkUrl){
        this.setState({linkUrlError:"Link Url is Required"})
      }
      return;
    }

    const data = {
      documentName: this.state.linkName,
      filePath: fullPath,
      linkPath: this.state.linkUrl,
      fileType: "link",
      status:"inbox",
      employee: {
        id: localStorage.getItem("id"),
      },
    };
    //console.log("Data submitted : ", data);
    const api = `/documentsattachmentdetail`;
    try {
      const response = await addNew(api, data);
      //console.log("After Uploading : " + response);
      // Add the new document directly to `uploadedFiles` instead of refetching
      this.setState((prevState) => ({
        // uploadedFiles: [...prevState.uploadedFiles, response.data],
        notification: {
          message: `${linkName} added successfully!`,
          type: "success",
          show: true,
        },
        isUploaded: true, // Triggers refetch if necessary
      }));
      // window.location.reload();
      this.closeAddLinkModal();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      //throw error;
    }
  };

  handleFolderOpen = () => {
    this.setState({ isFolderOpen: true });
  };

  handleFolderCloseModal = () => {
    this.setState({ isFolderOpen: false, folderName: "" });
  };

  getSampleFile = () => {
    return new File(["a"], this.state.folderName + ".txt", {
      type: "text/plain",
    });
  };

  handleSubmitFolderModal = async () => {
    const { folderName } = this.state;
    const path = sessionStorage.getItem("currentPath") || "";
    const fullPath = path ? `${path}/${folderName}` : folderName;
    const file = this.getSampleFile();
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentName", folderName);
      formData.append("folderPath", fullPath);
      formData.append("ownerName", localStorage.getItem("userName"));
      formData.append("employeeId", localStorage.getItem("id"));
      formData.append("uploadType", "file");
      formData.append("type", "folder");
      formData.append("status", "inbox");
      this.setState({ uploadedFileName: file.name });
      this.uploadDocuments(formData);
      if (path) {
        setTimeout(() => {
          window.location.reload();
        },750);
        // this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
      }
      const fileData = {
        slno: this.state.uploadedFiles.length + 1,
        name: file.name,
        date: new Date().toLocaleString(),
        url: URL.createObjectURL(file),
        folderPath: fullPath,
      };
      this.setState((prevState) => ({
        uploadedFiles: [...prevState.uploadedFiles, fileData],
      }));
    }
    this.handleFolderCloseModal();
  };

  uploadDocuments = async (docData) => {
    this.setState({ isLoading: true });
    const api = "/documentsattachmentdetail/saveDocument";
    try {
     
      const response = await uploadDocument(api, docData);
      console.log("After Uploading : ", response);
      this.setState({ isLoading: false });
      const folderName = this.state.folderName;
      this.setState((prevState) => ({
        notification: {
          message: `${folderName} folder created successfully!`,
          type: "success",
          show: true,
        },
        isUploaded: true, // Triggers refetch if necessary
      }));
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
      return response.data;
    } catch (error) {
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
    }
    this.fetchDocumentList();
  };

  removeLastWord(folderPath) {
    // Split the string by spaces and store the words in an array
    const words = folderPath.split("/");

    // If there is more than one word, remove the last one and join the rest back into a string
    if (words.length > 1) {
      words.pop(); // Remove the last word
      return words.join("/"); // Join the remaining words with a space
    }

    // If the string contains only one word, return an empty string
    return "";
  }

  handleFolderUpload = (event) => {
    const files = event.target.files;
    const filesArray = Array.from(files);
    filesArray.forEach((file) => {
      const parts = file.name.split('.');
      let extension=null;
      if (parts.length > 1) {
        extension = parts[parts.length - 1]?.toLowerCase() || null;
        console.log(extension)
        //this.state.fileExtension=parts.pop()?.toLowerCase() || null;
      }
      const folderPath = this.removeLastWord(file.webkitRelativePath);
      const currentPath = sessionStorage.getItem("currentPath");
      const fullPath = currentPath
        ? currentPath + "/" + folderPath
        : folderPath;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentName", file.name);
      formData.append("nodeId", 0);
      formData.append("folderPath", fullPath);
      formData.append("ownerName", localStorage.getItem("userName"));
      formData.append("employeeId", localStorage.getItem("id"));
      formData.append("uploadType", "folder");
      formData.append("type", "folder");
      formData.append("status", "inbox");
      this.setState({ fileExtension: extension },()=> this.uploadDocument(formData));
      if (currentPath) {
        setTimeout(() => {
          window.location.reload();
        },750);
        //this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
      }
    });
  };

  handleFilesDropped = (files) => {
    files.forEach((file, index) => {
      const parts = file.name.split('.');
      let extension=null;
      if (parts.length > 1) {
        extension = parts[parts.length - 1]?.toLowerCase() || null;
        //this.state.fileExtension=parts.pop()?.toLowerCase() || null;
      }
      if (file) {
        const fileData = {
          slno: this.state.uploadedFiles.length + 1,
          name: file.name,
          date: new Date().toLocaleString(),
          url: URL.createObjectURL(file),
        };
        this.setState((prevState) => ({
          uploadedFiles: [...prevState.uploadedFiles, fileData],
        }));
        if (file.webkitRelativePath !== file.name) {
          const folderPath = this.removeLastWord(file.webkitRelativePath);
          const currentPath = sessionStorage.getItem("currentPath");
          const fullPath = currentPath
            ? currentPath + "/" + folderPath
            : folderPath;
          const formData = new FormData();
          formData.append("file", file);
          formData.append("documentName", file.name);
          formData.append("nodeId", 0);
          formData.append("folderPath", fullPath || "");
          this.setState({ uploadedFileName: file.name });
          formData.append("ownerName", localStorage.getItem("userName"));
          formData.append("employeeId", localStorage.getItem("id"));
          formData.append("uploadType", "file");
          formData.append("type", "folder");
          formData.append("status", "inbox");
          this.uploadDocument(formData);
          if (currentPath) {
            setTimeout(() => {
              window.location.reload();
            },750)
            //this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
          }
        } else {
          const currentPath = sessionStorage.getItem("currentPath");
          const formData = new FormData();
          formData.append("file", file);
          formData.append("documentName", file.name);
          formData.append("nodeId", 0);
          formData.append("folderPath", currentPath || "");
          this.setState({ uploadedFileName: file.name });
          formData.append("ownerName", localStorage.getItem("userName"));
          formData.append("employeeId", localStorage.getItem("id"));
          formData.append("uploadType", "file");
          formData.append("type", "file");
          formData.append("status", "inbox");
          this.setState({ fileExtension: extension },()=> this.uploadDocument(formData));
          if (currentPath) {
            setTimeout(() => {
              window.location.reload();
            },750);
            //this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
          }
        }
      }
    });
  };

  handleFilesSelected = (files) => {
    console.log("Files received in parent component:");
    files.forEach((file, index) => {
      const parts = file.name.split('.');
      let extension=null;
      if (parts.length > 1) {
        extension = parts[parts.length - 1]?.toLowerCase() || null;
        //this.state.fileExtension=parts.pop()?.toLowerCase() || null;
      }
      if (file) {
        const fileData = {
          slno: this.state.uploadedFiles.length + 1,
          name: file.name,
          date: new Date().toLocaleString(),
          url: URL.createObjectURL(file),
        };
        this.setState((prevState) => ({
          uploadedFiles: [...prevState.uploadedFiles, fileData],
        }));
          const currentPath = sessionStorage.getItem("currentPath");
          const formData = new FormData();
          formData.append("file", file);
          formData.append("documentName", file.name);
          formData.append("nodeId", 0);
          formData.append("folderPath", currentPath || "");
          this.setState({ uploadedFileName: file.name });
          // formData.append('documentId', this.documentForm.value.documentType);
          formData.append("ownerName", localStorage.getItem("userName"));
          formData.append("employeeId", localStorage.getItem("id"));
          formData.append("uploadType", "file");
          formData.append("type", "file");
          formData.append("status", "inbox");
          this.setState({ fileExtension: extension },()=>this.uploadDocument(formData));
          if (currentPath) {
            setTimeout(() => {
              window.location.reload();
            },750)
            //this.fetchDocumentList(this.state.currentPage, this.state.itemsPerPage);
          }
      }
    });
  };

  toggleDetailView = (file) => {
    console.log("Toggle Detail View", file);
    this.openFileDetail(file);
  };

  // Add a method to handle inbox navigation
  handleInboxNavigation = () => {
    if (this.state.showFileDetail) {
      this.closeFileDetail();
    }
  };

  // Utility function to get file type icon and text
  getFileTypeIcon = (filePath, fileType) => {
    let iconClass = "fa fa-file-o";
    let iconColor = "#6b7280"; // default gray
    let fileTypeText = "File";
    
    // Check if it's a link type
    if (fileType === "link") {
      iconClass = "fa fa-link";
      iconColor = "#8b5cf6"; // purple for links
      fileTypeText = "Link";
    } else if (filePath) {
      const ext = filePath.split(".").pop().toLowerCase();
      
      switch (ext) {
        case "pdf":
          iconClass = "fa fa-file-pdf-o";
          iconColor = "#dc2626"; // red for PDF
          fileTypeText = "PDF";
          break;
        case "xls":
        case "xlsx":
          iconClass = "fa fa-file-excel-o";
          iconColor = "#059669"; // green for Excel
          fileTypeText = "Excel";
          break;
        case "doc":
        case "docx":
          iconClass = "fa fa-file-word-o";
          iconColor = "#2563eb"; // blue for Word
          fileTypeText = "Word";
          break;
        case "ppt":
        case "pptx":
          iconClass = "fa fa-file-powerpoint-o";
          iconColor = "#ea580c"; // orange for PowerPoint
          fileTypeText = "PowerPoint";
          break;
        case "txt":
          iconClass = "fa fa-file-text-o";
          iconColor = "#6b7280"; // gray for text files
          fileTypeText = "Text";
          break;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
        case "svg":
          iconClass = "fa fa-file-image-o";
          iconColor = "#d946ef"; // magenta for images
          fileTypeText = "Image";
          break;
        case "zip":
        case "rar":
        case "7z":
        case "tar":
          iconClass = "fa fa-file-archive-o";
          iconColor = "#7c3aed"; // purple for archives
          fileTypeText = "Archive";
          break;
        case "mp4":
        case "avi":
        case "mov":
        case "wmv":
        case "mkv":
          iconClass = "fa fa-file-video-o";
          iconColor = "#dc2626"; // red for videos
          fileTypeText = "Video";
          break;
        case "mp3":
        case "wav":
        case "flac":
        case "aac":
          iconClass = "fa fa-file-audio-o";
          iconColor = "#16a34a"; // green for audio
          fileTypeText = "Audio";
          break;
        default:
          iconClass = "fa fa-file-o";
          iconColor = "#6b7280";
          fileTypeText = "File";
      }
    }
    
    return { iconClass, iconColor, fileTypeText };
  };

  render() {
    const {
      showFileDetail,
      selectedFile,
      uploadedFiles,
      approveUploadedFiles,
      acknowledgeUploadedFiles,
      esignUploadedFiles,
    } = this.state;

    const { itemsPerPage, currentPage, totalItems } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    const splitFilePaths = uploadedFiles
      .map((file) => file?.filePath) // Ensure `filePath` exists
      .filter((filePath) => typeof filePath === "string") // Exclude undefined/null values
      .map((filePath) => filePath.split("/").slice(5).filter(Boolean)); // Split and clean paths

    const routeTree = {};

    splitFilePaths.forEach((pathParts) => {
      let current = routeTree;

      pathParts.forEach((segment) => {
        if (!current[segment]) {
          current[segment] = {};
        }
        current = current[segment];
      });
    });

    // console.log(routeTree);

    if (this.state.navigate) {
      return <Navigate to="/newDS/emailApproval" />;
    }
    if (this.state.auditnavigate) {
      return <Navigate to="/newDS/auditlog" />;
    }
    if (this.state.advSearchNavigate) {
      return <Navigate to="/newDS/advancedSearch" />;
    }
    if (this.state.acknavigate) {
      return <Navigate to="/newDS/emailAcknowledge" />;
    }

    if (this.state.navigateToESign) {
      return <Navigate to="/newDS/esignApproval" />;
    }

    if (this.state.reload) {
      return <Navigate to={this.props.location.pathname} />;
    }

    return (
      <>
        {/* <CustomBreadcrumb
          companyName={localStorage.getItem("companyName") || "Company"}
          featureName="Inbox"
        /> */}
        {!showFileDetail ? (
          <>
            <div className="container-fluid py-1">
              <div className="row align-items-center justify-content-between">
                <div className="col-12 d-flex align-items-center justify-content-between flex-wrap">
                  <div className="d-flex align-items-center gap-2 flex-wrap">
                    <Dropdown
                      show={this.state.showDropdown}
                      onToggle={this.toggleDropdown}
                    >
                      <Dropdown.Toggle
                        variant="primary"
                        className="d-flex align-items-center gap-2"
                      >
                        Add New
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <Dropdown.Item onClick={this.triggerFileInput}>
                          <i className="fa fa-folder-open"></i> Upload a Folder
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.handleAddLink}>
                          <i className="fa fa-link"></i> Add a Link
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openWordDocument}>
                          <i className="fa fa-file-word-o"></i> New Word
                          Document
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openExcelDocument}>
                          <i className="fa fa-file-excel-o"></i> New Excel
                          Spreadsheet
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openpowerpointDocument}>
                          <i className="fa fa-file-powerpoint-o"></i> New
                          PowerPoint Presentation
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                    <Button
                      variant="dark"
                      className="d-flex align-items-center gap-2"
                      onClick={this.handleFolderOpen}
                    >
                      <i className="fa fa-folder"></i> Create Folder
                    </Button>
                    <Button
                      variant="success"
                      className="d-flex align-items-center gap-2"
                      onClick={this.navigateAuditLog}
                    >
                      <i className="fa fa-file-text-o"></i> Audit Log
                    </Button>
                    <Button
                      style={{
                        background: "#ffc107",
                        border: "none",
                        color: "#222",
                      }}
                      className="d-flex align-items-center gap-2"
                      onClick={this.handleOpenModal}
                    >
                      <i className="fa fa-list-ol"></i> Numbering
                    </Button>
                    <Button
                      style={{
                        background: "#17c9f7",
                        border: "none",
                        color: "#fff",
                      }}
                      className="d-flex align-items-center gap-2"
                      onClick={this.navigateAdvSearch}
                    >
                      <i className="fa fa-search"></i> Adv. Search
                    </Button>
                    <input
                      title="search"
                      type="search"
                      name="searchParam"
                      className="form-control"
                      placeholder="Search docs, tags, etc.."
                      style={{ height: "40px", maxWidth: "205px" }}
                      value={this.state.searchParam}
                      onChange={this.handleSearchInput}
                    />
                  </div>
                  <CustomBreadcrumb
                    companyName={localStorage.getItem("companyName") || "Company"}
                    featureName="Inbox"
                  /> 
                </div>
              </div>
            </div>

            {this.state.modalOpen && (
              <NumberingModal
                isOpen={this.state.modalOpen}
                id={this.state.numberingListId}
                onClose={this.handleCloseModal}
                onSubmit={this.handleSubmitModal}
              />
            )}
            
            <input
              ref={this.folderInputRef}
              style={{ display: "none" }}
              type="file"
              webkitdirectory="true"
              directory=""
              onChange={this.handleFolderUpload}
            />
            
            <FileDropZone onFilesDropped={this.handleFilesDropped} onFilesSelected={this.handleFilesSelected} style={{ minHeight: '80px' }} />

            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}
            <div className="py-1">
              <Card className="shadow-sm border-1 mb-2">
                <Card.Body className="col-sm-12 col-12 d-flex align-items-center justify-content-between p-2">
                  <NestedObjectTable
                    from={"inbox"}
                    data={routeTree}
                    fileSelected={selectedFile}
                    uploadedFiles={this.state.uploadedFiles}
                    toggleDetailView={this.toggleDetailView}
                  />
                </Card.Body>
              </Card>
            </div>

            <div className="container-fluid py-1">
              <Tabs>
                <Tab
                  eventKey="approvals"
                  title="My Apporvals"
                  className={classes.tab}
                >
                  <DataTable
                    data={approveUploadedFiles || []}
                    columns={[
                      {
                        key: "index",
                        header: "SNo",
                        width: "60px",
                        render: (value, row, index) => index + 1,
                      },
                      {
                        key: "documentName",
                        header: "File Name",
                        sortable: true,
                        render: (value, row) => (
                          <>
                            <span
                              onClick={() => this.handleButtonClick(row)}
                              style={{
                                color: "blue",
                                textDecoration: "underline",
                                cursor: "pointer",
                              }}
                            >
                              {value}
                            </span>
                          </>
                        ),
                      },
                      {
                        key: "createdAt",
                        header: "Assigned At",
                        sortable: true,
                        render: (value) => formatDate(value),
                      },
                      {
                        key: "fileType",
                        header: "Type",
                        width: "100px",
                        sortable: true,
                        render: (value, row) => {
                          const { iconClass, iconColor, fileTypeText } = this.getFileTypeIcon(row.filePath, row.fileType);
                          return (
                            <div className="d-flex align-items-center">
                              <i
                                className={iconClass}
                                style={{ 
                                  fontSize: "20px", 
                                  color: iconColor,
                                  marginRight: "6px"
                                }}
                              ></i>
                              <small className="text-muted">{fileTypeText}</small>
                            </div>
                          );
                        },
                      },
                      {
                        key: "status",
                        header: "Status",
                        sortable: true,
                        render: (value, row) => (
                          <span
                            onClick={() => this.handleButtonClick(row)}
                            style={{ cursor: "pointer" }}
                          >
                            {row.rejectStatus
                              ? "Rejected"
                              : row.levelStatus
                                ? "Approved"
                                : "Yet to Approve"}
                          </span>
                        ),
                      },
                    ]}
                    className={classes.fileTable}
                    itemsPerPage={this.state.itemsPerPage}
                    currentPage={this.state.currentPage}
                    onPageChange={(page) => this.handlePageChange({ selected: page })}
                    onItemsPerPageChange={this.handleItemsPerPageChange}
                  />
                </Tab>
                <Tab eventKey="esign" title="My E-Sign" className={classes.tab}>
                  <DataTable
                    data={esignUploadedFiles || []}
                    columns={[
                      {
                        key: "index",
                        header: "SNo",
                        width: "60px",
                        render: (value, row, index) => index + 1,
                      },
                      {
                        key: "documentName",
                        header: "File Name",
                        sortable: true,
                        render: (value, row) => (
                          <>
                            <span
                              onClick={() => this.openSignDocument(row)}
                              style={{
                                color: "blue",
                                textDecoration: "underline",
                                cursor: "pointer",
                              }}
                            >
                              {value}
                            </span>
                          </>
                        ),
                      },
                      {
                        key: "createdAt",
                        header: "Assigned At",
                        sortable: true,
                        render: (value) => formatDate(value),
                      },
                      {
                        key: "fileType",
                        header: "Type",
                        width: "100px",
                        sortable: true,
                        render: (value, row) => {
                          const { iconClass, iconColor, fileTypeText } = this.getFileTypeIcon(row.filePath, row.fileType);
                          return (
                            <div className="d-flex align-items-center">
                              <i
                                className={iconClass}
                                style={{ 
                                  fontSize: "20px", 
                                  color: iconColor,
                                  marginRight: "6px"
                                }}
                              ></i>
                              <small className="text-muted">{fileTypeText}</small>
                            </div>
                          );
                        },
                      },
                      {
                        key: "status",
                        header: "Status",
                        sortable: true,
                        render: (value, row) => (
                          <span
                            onClick={() => this.openSignDocument(row)}
                            style={{ cursor: "pointer" }}
                          >
                            {row.rejectStatus
                              ? "Rejected"
                              : row.levelStatus
                                ? "Signed"
                                : "Yet to Sign"}
                          </span>
                        ),
                      },
                    ]}
                    className={classes.fileTable}
                    itemsPerPage={this.state.itemsPerPage}
                    currentPage={this.state.currentPage}
                    onPageChange={(page) => this.handlePageChange({ selected: page })}
                    onItemsPerPageChange={this.handleItemsPerPageChange}
                  />
                </Tab>
                <Tab
                  eventKey="acknowledge"
                  title="My Acknowledge"
                  className={classes.tab}
                >
                  <DataTable
                    data={acknowledgeUploadedFiles || []}
                    columns={[
                      {
                        key: "index",
                        header: "SNo",
                        width: "60px",
                        render: (value, row, index) => index + 1,
                      },
                      {
                        key: "documentName",
                        header: "File Name",
                        sortable: true,
                        render: (value, row) => (
                          <>
                            <span
                              onClick={() => this.handleAckButtonClick(row)}
                              style={{
                                color: "blue",
                                textDecoration: "underline",
                                cursor: "pointer",
                              }}
                            >
                              {value}
                            </span>
                          </>
                        ),
                      },
                      {
                        key: "createdAt",
                        header: "Assigned At",
                        sortable: true,
                        render: (value) => formatDate(value),
                      },
                      {
                        key: "fileType",
                        header: "Type",
                        width: "100px",
                        sortable: true,
                        render: (value, row) => {
                          const { iconClass, iconColor, fileTypeText } = this.getFileTypeIcon(row.filePath, row.fileType);
                          return (
                            <div className="d-flex align-items-center">
                              <i
                                className={iconClass}
                                style={{ 
                                  fontSize: "20px", 
                                  color: iconColor,
                                  marginRight: "6px"
                                }}
                              ></i>
                              <small className="text-muted">{fileTypeText}</small>
                            </div>
                          );
                        },
                      },
                      {
                        key: "status",
                        header: "Status",
                        sortable: true,
                        render: (value, row) => (
                          <span
                            onClick={() => this.handleAckButtonClick(row)}
                            style={{ cursor: "pointer" }}
                          >
                            {row.levelStatus
                              ? "Acknowledged"
                              : "Yet to acknowledge"}
                          </span>
                        ),
                      },
                    ]}
                    className={classes.fileTable}
                    itemsPerPage={this.state.itemsPerPage}
                    currentPage={this.state.currentPage}
                    onPageChange={(page) => this.handlePageChange({ selected: page })}
                    onItemsPerPageChange={this.handleItemsPerPageChange}
                  />
                </Tab>
              </Tabs>
            </div>
          </>
        ) : (
          this.state.showFileUpdate ? (
            <FileUpdate
              onClose={(file) => this.setState({ showFileUpdate: false, showFileDetail: true, selectedFile: file })}
            />
          ) : (
            <FileDetail
              fileName={selectedFile.documentName}
              fileUrl={selectedFile.filePath}
              fileId={selectedFile.documentsAttachmentId}
              onClose={this.closeFileDetail}
              companyName={this.state.companyName}
            />
          )
        )}

        {/* Add link modal */}
        <div>
          <Modal
            show={this.state.addLinkModalOpen}
            onHide={this.closeAddLinkModal}
          >
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title>New Link</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div>
                <div className="row">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="linkName"
                      id="linkName"
                      placeholder="Link Name"
                      className="form-control"
                      required
                      value={this.state.linkName}
                      onChange={(event) => {
                        this.setState({ linkNameError:"", linkName: event.target.value });
                      }}
                    />
                  </div>
                </div>
                <span className="text-danger">{this.state.linkNameError}</span>
                <div className="row mt-2">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="linkUrl"
                      placeholder="Link URL"
                      id="linkUrl"
                      className="form-control"
                      value={this.state.linkUrl}
                      onChange={(event) => {
                        this.setState({ linkUrlError:"", linkUrl: event.target.value });
                      }}
                      required
                    />
                  </div>
                </div>
                <span className="text-danger">{this.state.linkUrlError}</span>
                <div className="row mt-3">
                  <div className="col-12 text-end">
                    <Button variant="primary" onClick={this.handleLinkSubmit}>
                      Create
                    </Button>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </div>

        {/* folder modal*/}
        <div>
          <Modal
            show={this.state.isFolderOpen}
            onHide={this.handleFolderCloseModal}
          >
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title>Create Folder</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {/* <h5>Create Folder</h5> */}
              <div>
                <div className="row">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="folderName"
                      id="folderName"
                      placeholder="Folder Name"
                      className="form-control"
                      required
                      value={this.state.folderName}
                      onChange={(event) => {
                        this.setState({ folderName: event.target.value });
                      }}
                    />
                  </div>
                </div>
                <div className="row mt-3">
                  <div className="col-12 text-center">
                    <Button
                      variant="primary"
                      onClick={this.handleSubmitFolderModal}
                      disabled={!this.state.folderName}
                    >
                      Create
                    </Button>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </div>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default DocumentManagement;
