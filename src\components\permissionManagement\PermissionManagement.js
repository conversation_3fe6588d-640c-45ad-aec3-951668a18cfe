import React, { Component } from "react";
import classes from "./PermissionManagement.module.css";
import { <PERSON><PERSON>, Button } from "react-bootstrap";
import { getList, lockUser, unlockUser } from "../../services/apiService";
import axios from '../../services/api';
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import { DataTable } from "../Table/DataTable";

class PermissionManagement extends Component {
  state = {
    notification: {
      message: "",
      type: "",
      show: false,
    },
    userList: [],
    selectedUser: null,
    permissions: [],
    roles: [],
    assignedPermissions: [],
    assignedRoles: [],
    selectedAvailablePermissions: [],
    selectedAssignedPermissions: [],
    selectedAvailableRoles: [],
    selectedAssignedRoles: [],
    selectAllAvailable: false,
    selectAllAssigned: false,
    selectAllAvailableRoles: false,
    selectAllAssignedRoles: false,
    searchQuery: "",
    searchAssignedQuery: "",
    searchRoleQuery: "",
    searchAssignedRoleQuery: "",
    isLockModalOpen: false,
    userIdToLock: null,
    isUnlockModalOpen: false,
    userIdToUnLock: null,
    currentPage:0,
    totalItems:0,
    itemsPerPage:10,
    search:"",
  };
  componentDidMount() {
    this.fetchUserList();
  }

  handleSearchInputChange = (event) => {
    const search = event.target.value;
    this.setState({ search:search }, () => {
      console.log(search);
      this.fetchUserList();
    });
  };

  fetchUserList = async (page=0) => {
    const {itemsPerPage}=this.state;
    const api = `/user/list-all?page=${page}&size=${itemsPerPage}&search=${this.state.search}&sort=`;
    try {
      const response = await getList(api);
      const data=response.data;
      this.setState({
        userList: data.content,
        totalItems:data.totalElements,
        currentPage:page,
       });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      console.log(error);
    }
  };

  handlePageChange = (page) => {
    this.fetchUserList(page);
  };
  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleSearchChange = (e) => {
    const { value } = e.target;
    this.setState({ searchQuery: value });
  };
  handleAssignedSearchChange = (e) => {
    const { value } = e.target;
    this.setState({ searchAssignedQuery: value });
  };

  getFilteredPermissions = () => {
    const { permissions, searchQuery } = this.state;
    if (!searchQuery) return permissions;
    return permissions.filter((permission) =>
      permission.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  getFilteredAssignedPermissions = () => {
    const { assignedPermissions, searchAssignedQuery } = this.state;
    if (!searchAssignedQuery) return assignedPermissions;
    return assignedPermissions.filter((permission) =>
      permission.toLowerCase().includes(searchAssignedQuery.toLowerCase())
    );
  };

  openModal = (user) => {
    console.log(user.id);
    this.viewUserPermissions(user);
    this.setState({
      isOpen: true,
      selectedUser: user,
    });
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      selectedUser: null,
      selectedAvailablePermissions: [],
      selectedAssignedPermissions: [],
      selectedAvailableRoles: [],
      selectedAssignedRoles: [],
      userId: null,
    });
  };
  userLock = async (userId) => {
    try {
      await lockUser(userId);
      this.setState((prevState) => ({
        userList: prevState.userList.map((user) =>
          user.id === userId ? { ...user, isLocked: true } : user
        ),
      }));
      this.setState({
        notification: {
          message: "User Locked Successfully",
          type: "success",
          show: true,
        },
      });
      this.fetchUserList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      console.error("Error locking user:", error);
    }
    this.closeLockModal();
  };

  userUnlock = async (userId) => {
    try {
      await unlockUser(userId);
      this.setState((prevState) => ({
        userList: prevState.userList.map((user) =>
          user.id === userId ? { ...user, isLocked: false } : user
        ),
      }));
      this.setState({
        notification: {
          message: "User Un-Locked Successfully",
          type: "success",
          show: true,
        },
      });
      this.fetchUserList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      console.error("Error unlocking user:", error);
    }
    this.closeUnLockModal();
  };
  viewUserPermissions = async (user) => {
    const employeeId = user.id;
    let api = `${GlobalConstants.globalURL}/user/dropdownList/`;
    try {
      const data = await axios.get(api, { params: { employeeId } });
      const permissionsArray = data.data.data.restircted;
      const rolesArray = data.data.data.restirctedRoles;

      this.setState({
        userId: user.id,
        permissions: permissionsArray || [],
        roles: rolesArray || [],
        assignedPermissions: data.data.data.permissions || [],
        assignedRoles: data.data.data.permissionsRoles || [],
        selectedAvailablePermissions: [],
        selectedAssignedPermissions: [],
        selectedAvailableRoles: [],
        selectedAssignedRoles: [],
        selectAllAvailable: false,
        selectAllAssigned: false,
        selectAllAvailableRoles: false,
        selectAllAssignedRoles: false,
      });
    } catch (err) {
      console.log(err);
    }
  };

  handleAvailableCheckboxChange = (permissionName) => {
    this.setState((prevState) => {
      const selectedAvailablePermissions =
        prevState.selectedAvailablePermissions.includes(permissionName)
          ? prevState.selectedAvailablePermissions.filter(
              (name) => name !== permissionName
            )
          : [...prevState.selectedAvailablePermissions, permissionName];

      return { selectedAvailablePermissions };
    });
  };

  handleAvailableRoleCheckboxChange = (roleName) => {
    this.setState((prevState) => {
      const selectedAvailableRoles = prevState.selectedAvailableRoles.includes(
        roleName
      )
        ? prevState.selectedAvailableRoles.filter((name) => name !== roleName)
        : [...prevState.selectedAvailableRoles, roleName];
      return { selectedAvailableRoles };
    });
  };

  handleAssignedRoleCheckboxChange = (roleName) => {
    this.setState((prevState) => {
      const selectedAssignedRoles = prevState.selectedAssignedRoles.includes(
        roleName
      )
        ? prevState.selectedAssignedRoles.filter((name) => name !== roleName)
        : [...prevState.selectedAssignedRoles, roleName];
      return { selectedAssignedRoles };
    });
  };

  handleAssignedCheckboxChange = (permissionName) => {
    this.setState((prevState) => {
      const selectedAssignedPermissions =
        prevState.selectedAssignedPermissions.includes(permissionName)
          ? prevState.selectedAssignedPermissions.filter(
              (name) => name !== permissionName
            )
          : [...prevState.selectedAssignedPermissions, permissionName];

      return { selectedAssignedPermissions };
    });
  };

  moveSelectedPermissionsToAssigned = () => {
    this.setState((prevState) => {
      const newAssignedPermissions = [
        ...prevState.assignedPermissions,
        ...prevState.selectedAvailablePermissions,
      ];
      const newAvailablePermissions = prevState.permissions.filter(
        (permission) =>
          !prevState.selectedAvailablePermissions.includes(permission)
      );
      return {
        permissions: newAvailablePermissions,
        assignedPermissions: newAssignedPermissions,
        selectedAvailablePermissions: [],
      };
    });
  };

  moveSelectedPermissionsToAvailable = () => {
    this.setState((prevState) => {
      const newAvailablePermissions = [
        ...prevState.permissions,
        ...prevState.selectedAssignedPermissions,
      ];
      const newAssignedPermissions = prevState.assignedPermissions.filter(
        (permission) =>
          !prevState.selectedAssignedPermissions.includes(permission)
      );
      return {
        permissions: newAvailablePermissions,
        assignedPermissions: newAssignedPermissions,
        selectedAssignedPermissions: [],
      };
    });
  };
  moveSelectedRolesToAssigned = () => {
    this.setState((prevState) => {
      const newAssignedRoles = [
        ...prevState.assignedRoles,
        ...prevState.selectedAvailableRoles,
      ];
      const newAvailableRoles = prevState.roles.filter(
        (role) => !prevState.selectedAvailableRoles.includes(role)
      );
      return {
        roles: newAvailableRoles,
        assignedRoles: newAssignedRoles,
        selectedAvailableRoles: [],
      };
    });
  };

  moveSelectedRolesToAvailable = () => {
    this.setState((prevState) => {
      const newAvailableRoles = [
        ...prevState.roles,
        ...prevState.selectedAssignedRoles,
      ];
      const newAssignedRoles = prevState.assignedRoles.filter(
        (role) => !prevState.selectedAssignedRoles.includes(role)
      );
      return {
        roles: newAvailableRoles,
        assignedRoles: newAssignedRoles,
        selectedAssignedRoles: [],
      };
    });
  };

  toggleSelectAllAvailable = () => {
    const { selectAllAvailable, permissions } = this.state;
    const newSelected = selectAllAvailable ? [] : permissions;

    this.setState({
      selectedAvailablePermissions: newSelected,
      selectAllAvailable: !selectAllAvailable,
    });
  };

  toggleSelectAllAvailableRoles = () => {
    const { selectAllAvailableRoles, roles } = this.state;
    const newSelected = selectAllAvailableRoles ? [] : roles;

    this.setState({
      selectedAvailableRoles: newSelected,
      selectAllAvailableRoles: !selectAllAvailableRoles,
    });
  };

  toggleSelectAllAssigned = () => {
    const { selectAllAssigned, assignedPermissions } = this.state;
    const newSelected = selectAllAssigned ? [] : assignedPermissions;

    this.setState({
      selectedAssignedPermissions: newSelected,
      selectAllAssigned: !selectAllAssigned,
    });
  };

  toggleSelectAllAssignedRoles = () => {
    const { selectAllAssignedRoles, assignedRoles } = this.state;
    const newSelected = selectAllAssignedRoles ? [] : assignedRoles;

    this.setState({
      selectedAssignedRoles: newSelected,
      selectAllAssignedRoles: !selectAllAssignedRoles,
    });
  };

  updatePermissions = async () => {
    console.log("Update Permissions called");
    const { permissions, assignedPermissions, assignedRoles, roles, userId } =
      this.state;
    console.log("User ID:", userId);
    console.log("Available Permissions:", permissions);
    console.log("Assigned Permissions:", assignedPermissions);
    console.log("Available Roles:", roles);
    console.log("Assigned Roles:", assignedRoles);

    let api = `${GlobalConstants.globalURL}/user/${userId}`;
    try {
      const body = {
        rolesAssigned: assignedRoles.join(","),
        empPermissions: assignedPermissions.join(","),
        permissions: assignedPermissions,
        restircted: permissions,
        permissionsRoles: assignedRoles,
        restirctedRoles: roles,
      };
      console.log(body);

      const response = await axios.put(api, body);
      console.log("Response from API:", response.data);

      // Fetch updated permissions after the update
      //await this.viewUserPermissions({ user }); // You can pass a mock role object or create a function to get it

      this.setState({
        notification: {
          message: response.data.message,
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
    this.closeModal();
  };

  openLockModal = (id) => {
    this.setState({ isLockModalOpen: true, userIdToLock: id });
  };
  closeLockModal = () => {
    this.setState({ isLockModalOpen: false, userIdToLock: null });
  };

  openUnLockModal = (id) => {
    this.setState({ isUnlockModalOpen: true, userIdToUnLock: id });
  };
  closeUnLockModal = () => {
    this.setState({ isUnlockModalOpen: false, userIdToUnLock: null });
  };

  handleItemsPerPageChange = (newItemsPerPage) => {
    this.setState({ itemsPerPage: newItemsPerPage }, () => {
      this.fetchUserList(0);
    });
  };

  render() {
    const {
      selectedUser,
      userList,
      permissions,
      roles,
      assignedPermissions,
      assignedRoles,
      selectedAvailablePermissions,
      selectedAssignedPermissions,
      selectedAvailableRoles,
      selectedAssignedRoles,
      selectAllAvailable,
      selectAllAssigned,
      selectAllAvailableRoles,
      selectAllAssignedRoles,
    } = this.state;

    const {currentPage,itemsPerPage,totalItems}=this.state;
    const start=currentPage*itemsPerPage+1;
    const end=Math.min((currentPage+1)*itemsPerPage,totalItems);
    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Create or Update Permission</h4>
            <hr/>
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 d-flex align-items-center">
            <h4>
              <i className="fa fa-user"></i>&nbsp;List of Users
            </h4>
          </div>
        </div>

        <DataTable
          data={this.state.userList}
          columns={[
            {
              key: 'index',
              header: 'S.No',
              render: (_, row, index) => start + index,
              width: '80px'
            },
            {
              key: 'userName',
              header: 'User Name',
              sortable: true
            },
            {
              key: 'firstName',
              header: 'First Name',
              sortable: true
            },
            {
              key: 'lastName',
              header: 'Last Name',
              sortable: true
            },
            {
              key: 'email',
              header: 'Email ID',
              sortable: true
            },
            {
              key: 'actions',
              header: 'Actions',
              width: '150px',
              render: (_, user) => (
                <div className="text-center">
                  <Button
                    type="button"
                    title="view user permissions"
                    className="btn btn-primary btn-sm"
                    onClick={() => this.openModal(user)}
                  >
                    <i className="fa fa-eye"></i>
                  </Button>
                  &nbsp;&nbsp;&nbsp;
                  {user.activated ? (
                    <i
                      title="lock user"
                      className="fa fa-lock"
                      style={{ color: "red", cursor: "pointer" }}
                      onClick={() => this.openLockModal(user.id)}
                    ></i>
                  ) : (
                    <i
                      title="unlock user"
                      className="fa fa-unlock"
                      style={{ color: "green", cursor: "pointer" }}
                      onClick={() => this.openUnLockModal(user.id)}
                    ></i>
                  )}
                </div>
              )
            }
          ]}
          searchable={true}
          className="table-bordered"
          itemsPerPage={this.state.itemsPerPage}
          totalItems={this.state.totalItems}
          currentPage={this.state.currentPage}
          onPageChange={this.handlePageChange}
          onItemsPerPageChange={this.handleItemsPerPageChange}
        />


        <Modal
          show={this.state.isOpen}
          onHide={this.closeModal}
          size="xl"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title>Permission Management</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p>
              <strong>
                Permissions for User :
                {selectedUser
                  ? `${selectedUser.firstName}`
                  : ""}
              </strong>
            </p>
            <div className="row mt-3">
              <div className="col-5">
                <h5 className="text-center" style={{backgroundColor: '#445BFF', color: 'white', borderRadius: '8px 8px 0 0', padding: '10px'}}>List of Permissions</h5>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search...."
                  value={this.state.searchQuery}
                  onChange={this.handleSearchChange}
                  style={{borderRadius: '0 0 8px 8px', marginBottom: '8px'}}
                />

                <div style={{background: 'white', borderRadius: '0 0 8px 8px', boxShadow: '0 2px 8px rgba(0,0,0,0.05)', padding: '15px', minHeight: '300px', border: '1px solid #b1b1b1', height: '350px', overflowY: 'auto'}}>
                  <div className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="selectAllAvailableRoles"
                      checked={selectAllAvailableRoles}
                      onChange={this.toggleSelectAllAvailableRoles}
                    />
                    <label
                      className="form-check-label"
                      htmlFor="selectAllAvailableRoles"
                    >
                      <strong>Roles</strong>
                    </label>
                  </div>

                  {roles.length > 0 && roles.some((role) => role) && roles.map((role) => (
                    <div key={role} className="form-check">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id={role}
                        checked={selectedAvailableRoles.includes(role)}
                        onChange={() =>
                          this.handleAvailableRoleCheckboxChange(role)
                        }
                      />
                      <label className="form-check-label" htmlFor={role}>
                        {role}
                      </label>
                    </div>
                  ))}
                  <div className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="selectAllAvailable"
                      checked={selectAllAvailable}
                      onChange={this.toggleSelectAllAvailable}
                    />
                    <label
                      className="form-check-label"
                      htmlFor="selectAllAvailable"
                    >
                      <strong>Permissions</strong>
                    </label>
                  </div>
                  { this.getFilteredPermissions().some((permission) => permission) && this.getFilteredPermissions().map((permission) => (
                    <div key={permission} className="form-check">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id={permission}
                        checked={selectedAvailablePermissions.includes(
                          permission
                        )}
                        onChange={() =>
                          this.handleAvailableCheckboxChange(permission)
                        }
                      />
                      <label className="form-check-label" htmlFor={permission}>
                        {permission}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="col-2 text-center" style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
                <div className="mb-2">
                  <Button
                    style={{
                      backgroundColor: '#445BFF',
                      color: 'white',
                      border: 'none',
                      borderRadius: '50%',
                      width: '40px',
                      height: '40px',
                      margin: '10px 0',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={() => {
                      this.moveSelectedPermissionsToAssigned();
                      this.moveSelectedRolesToAssigned();
                    }}
                    disabled={
                      selectedAvailablePermissions.length === 0 &&
                      selectedAvailableRoles.length === 0
                    }
                  >
                    <i className="fa fa-angle-double-right fa-lg"></i>
                  </Button>
                </div>
                <div>
                  <Button
                    style={{
                      backgroundColor: '#FFA726',
                      color: 'white',
                      border: 'none',
                      borderRadius: '50%',
                      width: '40px',
                      height: '40px',
                      margin: '10px 0',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={() => {
                      this.moveSelectedPermissionsToAvailable();
                      this.moveSelectedRolesToAvailable();
                    }}
                    disabled={
                      selectedAssignedPermissions.length === 0 &&
                      selectedAssignedRoles.length === 0
                    }
                  >
                    <i className="fa fa-angle-double-left fa-lg"></i>
                  </Button>
                </div>
              </div>

              <div className="col-5">
                <h5 className="text-center" style={{backgroundColor: '#FFA726', color: 'white', borderRadius: '8px 8px 0 0', padding: '10px'}}>Assigned Permissions</h5>

                <input
                  type="text"
                  className="form-control"
                  placeholder="Search ..."
                  value={this.state.searchAssignedQuery}
                  onChange={this.handleAssignedSearchChange}
                  style={{borderRadius: '0 0 8px 8px', marginBottom: '8px'}}
                />

                <div style={{background: 'white', borderRadius: '0 0 8px 8px', boxShadow: '0 2px 8px rgba(0,0,0,0.05)', padding: '15px', minHeight: '300px', border: '1px solid #b1b1b1', height: '350px', overflowY: 'auto'}}>
                  {/* Check if there are assigned roles */}
                  {assignedRoles.length > 0 && (
                    <>
                      <div className="form-check">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          id="selectAllAssignedRoles"
                          checked={selectAllAssignedRoles}
                          onChange={this.toggleSelectAllAssignedRoles}
                        />
                        <label
                          className="form-check-label"
                          htmlFor="selectAllAssignedRoles"
                        >
                          <strong>Assigned Roles</strong>
                        </label>
                      </div>
                      {assignedRoles.length > 0 &&
                        assignedRoles.some((role) => role) &&
                        assignedRoles.map(
                          (role) =>
                            role && (
                              <div key={role} className="form-check">
                                <input
                                  type="checkbox"
                                  className="form-check-input"
                                  id={role}
                                  checked={selectedAssignedRoles.includes(role)}
                                  onChange={() =>
                                    this.handleAssignedRoleCheckboxChange(role)
                                  }
                                />
                                <label
                                  className="form-check-label"
                                  htmlFor={role}
                                >
                                  {role}
                                </label>
                              </div>
                            )
                        )}
                    </>
                  )}

                  {/* Check if there are assigned permissions */}
                  {assignedPermissions.length > 0 && (
                    <>
                      <div className="form-check">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          id="selectAllAssigned"
                          checked={selectAllAssigned}
                          onChange={this.toggleSelectAllAssigned}
                        />
                        <label
                          className="form-check-label"
                          htmlFor="selectAllAssigned"
                        >
                          <strong>Assigned Permissions</strong>
                        </label>
                      </div>
                      {this.getFilteredAssignedPermissions().some((permission)=>permission) && this.getFilteredAssignedPermissions().map(
                        (permission) => (
                          <div key={permission} className="form-check">
                            <input
                              type="checkbox"
                              className="form-check-input"
                              id={permission}
                              checked={selectedAssignedPermissions.includes(
                                permission
                              )}
                              onChange={() =>
                                this.handleAssignedCheckboxChange(permission)
                              }
                            />
                            <label
                              className="form-check-label"
                              htmlFor={permission}
                            >
                              {permission}
                            </label>
                          </div>
                        )
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button
              style={{
                backgroundColor: '#4CAF50',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                padding: '8px 24px',
                marginRight: '8px'
              }}
              onClick={this.updatePermissions}
            >
              Update
            </Button>
            <Button
              style={{
                backgroundColor: '#BDBDBD',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                padding: '8px 24px'
              }}
              onClick={this.closeModal}
            >
              Cancel
            </Button>
          </Modal.Footer>
        </Modal>
        {/* {Lock modal} */}
        <Modal
          show={this.state.isLockModalOpen}
          onHide={this.closeLockModal}
          size="md"
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title style={{fontSize:'16px', color:'white', textAlign:'center', width:'100%', margin:0}}>Lock User</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div>Are you sure you want to lock the user?</div>
          </Modal.Body>
          <Modal.Footer>
            <div>
              <Button
                variant="primary"
                onClick={() => this.userLock(this.state.userIdToLock)}
              >
                Lock
              </Button>
            </div>
          </Modal.Footer>
        </Modal>

        {/* {unlock modal} */}
        <Modal
          show={this.state.isUnlockModalOpen}
          onHide={this.closeUnLockModal}
          size="md"
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title style={{fontSize:'16px', color:'white', textAlign:'center', width:'100%', margin:0}}>Unlock User</Modal.Title>
          </Modal.Header>
          <Modal.Body>Are you sure do you want to un-lock the user?</Modal.Body>
          <Modal.Footer>
            <div>
              <Button
                variant="primary"
                onClick={() => this.userUnlock(this.state.userIdToUnLock)}
              >
                UnLock
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

export default PermissionManagement;
