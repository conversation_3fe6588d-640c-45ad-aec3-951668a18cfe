import React, { useState, useEffect  } from 'react';
import classes from './ForgotPassword.module.css';
import { useNavigate } from 'react-router';
import { GlobalConstants } from '../../constants/global-constants';
import axios from 'axios';
import { Button, ButtonGroup, Col, Container, Form, Row } from 'react-bootstrap';
import logo from "../images/logo1.png";
import loginImage from '../images/loginImage.png';
import Notification from '../Notification/Notification';
import { getList } from "../../services/apiService"

function ForgotPassword(props) {
  const [userName, setUserName] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [deployedURL, setDeployedURL] = useState('');
  const [notification, setNotification] = useState({
      message: "",
      type: "",
      show: false,
  });
  const navigate = useNavigate();

  useEffect(() => {
    const deployedURL = window.location.href.split('/#')[0];
    setDeployedURL(deployedURL);
  }, []);

  const cancelButtonHandler = () => {
    window.location.replace(`${deployedURL}`);
  }
  const handleChange = (event) => {
    setUserName(event.target.value);
    setErrorMessage('');
    console.log(event.target.value)
  }
  const submitButtonHandler = async () => {
    console.log(userName)
    try {
      const response = await axios.get(`${GlobalConstants.globalURL}/user/validate`, { params: { userName } });
      if (!response.data.data) {
        setErrorMessage('');
        await sendEmail();
      }
      else {
        setErrorMessage('User Name Does Not Exist in DB');
      }
    }
    catch (error) {
      console.log(error)
    }
  }
  const sendEmail = async () => {
    const api = `/user/sendEmail?userName=${userName}`;
    try {
      const response = await getList(api);
      // if (response.data.data === true || response.data.data === "true") {
        setNotification({
          message: "Password is sent to registered email please check!",
          type: "success",
          show: true,
        });
        setTimeout(() => {
          navigate("/", { replace: true });
        }, 500);
      // } else {
      //   setNotification({
      //     message: "Failed to reset the password, Please contact admin!",
      //     type: "error",
      //     show: true,
      //   });
      // }
    } catch (error) {
      setNotification({
        message: "Failed to reset the password, Please contact admin!",
        type: "error",
        show: true,
      });
      console.log(error);
    }
  }

  const closeNotification = () => {
    setNotification({ message: "", type: "", show: false });
  };

  return (
    <Container fluid className="p-0 d-flex align-items-center">
      <Row className="w-100 m-0">
        {/* Left side */}
        <Col md={6} className={`${classes["left-side"]}`}>
          <div className={classes["logo-container"]}>
            <img src={logo} alt="Datadot Logo" style={{ width: "150px" }} />
          </div>

          <div className="d-flex flex-column align-items-center justify-content-center h-100">
            <h2 className={`${classes["heading"]}`}>DMS - Forgot Password</h2>
            <div className='d-flex align-items-center justify-content-between gap-2 w-100 m-2 bg-light p-2 rounded'>
              <label>Username:</label>
              <input
                type="email"
                name="username"
                value={userName}
                onChange={handleChange}
                placeholder="Please enter your username"
                className={`${classes['formControl']}`}
              />
              {errorMessage && (
                <div className="form-group row">
                  <div className="col-sm-8 offset-sm-2">
                    <span className="text-danger">{errorMessage}</span>
                  </div>
                </div>
              )}
            </div>


            <div className='d-flex g-1 justify-content-center align-items-center'>
              <Button variant="primary" type="submit" className={`${classes['login-button']} me-3 w-100`} onClick={submitButtonHandler}>
                Submit
              </Button>
              <Button variant="danger" type="submit" className={`${classes['login-button']} w-100`} onClick={cancelButtonHandler}>
                Cancel
              </Button>
            </div>
          </div>
        </Col>

        {/* Right side */}
        <Col
          md={6}
          className={`${classes["bg-gradient-custom"]} d-flex align-items-center justify-content-center`}
        >
          <div className="text-center px-4">
            <h4 className={`${classes['right-heading']}`}>Manage All Your</h4>
            <h2 className="fw-bold">Documents & Folders</h2>
            <img
              src={loginImage}
              alt="Document illustration"
              className="img-fluid mt-4"
              style={{ maxHeight: "400px" }}
            />
          </div>
        </Col>
      </Row>
      {notification.show && <Notification message={notification.message} type={notification.type} onClose={closeNotification}/>}
    </Container>
  );
}

export default ForgotPassword;