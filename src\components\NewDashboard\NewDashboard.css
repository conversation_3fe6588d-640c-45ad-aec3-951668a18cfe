/* Modern Font Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #4361ee;
  --secondary-color: #3f37c9;
  --accent-color: #4cc9f0;
  --success-color: #4ade80;
  --warning-color: #fbbf24;
  --danger-color: #f87171;
  --dark-color: #1e293b;
  --darker-color: #0f172a;
  --light-color: #f8fafc;
  --text-primary: #334155;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: #e2e8f0;
  --card-bg: #ffffff;
  --body-bg: #f1f5f9;
  --sidebar-bg-start: #1a365d;
  --sidebar-bg-end: #0f172a;
  --sidebar-item-hover: rgba(79, 129, 255, 0.15);
  --sidebar-item-active: rgba(79, 129, 255, 0.25);
  --sidebar-text: rgba(255, 255, 255, 0.85);
  --sidebar-text-hover: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
  background-color: var(--body-bg);
}

/* Top Navigation Bar */
.navbar {
  background:#fff;
  color: var(--text-primary);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 56px;
  padding: 0 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1050;
}

.navbar .navbar-brand {
  font-weight: 600;
  font-size: 1.1rem;
  letter-spacing: 0.01em;
}

.navbar .navbar-brand i {
  color: var(--primary-color);
}

.navbar .btn-primary {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-primary);
  transition: all 0.2s ease;
  padding: 0.4rem 0.75rem;
}

.searchBar {
  position: relative;
    margin-left: 20px;
    border-left: 1px solid #e1e1e1;
    padding-left: 25px;
}

.searchBar .fa-search {
  position: absolute;
  top: 10px;
  left: 10px;
}

.searchBar input {
border: transparent;
}

.navbar .searchBtn {
  color: var(--text-primary);
  background-color: #F2F4FD;
  position: relative;
font-size: 14px;
font-weight: 500;
  
}

.navbar .searchBtn .fa-search{
  opacity: .5;
  font-weight: 400;
}

.navbar .btn-primary:hover {
  /* background-color: rgba(255, 255, 255, 0.2); */
}

.navbar .user-menu-dropdown .dropdown-toggle::after {
  display: none;
}

.navbar .user-menu-dropdown .fa-cog {
  font-size: 18px;
}

.navbar .user-menu-dropdown .dropdown-menu {
  background-color: var(--card-bg);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 0.5rem;
  min-width: 200px;
  margin-top: 10px;
}

.navbar .user-menu-dropdown .dropdown-item {
  color: var(--text-primary);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.navbar .user-menu-dropdown .dropdown-item:hover {
  background-color: rgba(67, 97, 238, 0.08);
  color: var(--primary-color);
}

.navbar .user-menu-dropdown .dropdown-item i {
  width: 20px;
  text-align: center;
  margin-right: 8px;
  color: var(--text-secondary);
}

.navbar .user-menu-dropdown .dropdown-item:hover i {
  color: var(--primary-color);
}

/* Sidebar styles - fixing positioning issues */
.sidebar {
  width: 260px;
  position: fixed;
  top: 56px; /* Adjust based on navbar height */
  left: 0; /* Change from -260px to 0 to keep it visible */
  height: calc(100vh - 56px); /* Full height minus navbar */
  overflow-y: auto;
  transition: all 0.3s ease;
  z-index: 1040;
  background: linear-gradient(to bottom, var(--sidebar-bg-start), var(--sidebar-bg-end)) !important;
  color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  border-right: 1px solid var(--sidebar-border);
  padding-top: 10px;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Replace redundant sidebar styles with improved ones */
.sidebar {
  min-height: 100vh;
  padding-top: 60px;
  transition: width 0.3s ease, transform 0.3s ease;
  position: fixed; /* Keep it fixed */
  top: 0;
  left: 0; /* Always position from left edge */
  background: linear-gradient(to bottom, var(--sidebar-bg-start), var(--sidebar-bg-end)) !important;
  overflow-x: hidden;
  z-index: 100;
}

.sidebar.open {
  width: 260px;
  min-width: 260px;
  transform: translateX(0); /* Keep it visible */
}

.sidebar.collapsed {
  width: 60px;
  min-width: 60px;
  overflow-x: hidden;
  transform: translateX(0); /* Keep it visible */
}

/* Content area styles - fix the margins */
.content {
  margin-left: 260px; /* Default margin for open sidebar */
  width: calc(100% - 260px);
  transition: margin-left 0.3s ease, width 0.3s ease;
  margin-top: 56px; /* Match navbar height */
  padding: 20px;
  height: calc(100vh - 56px);
  overflow-y: auto;
}

.content-expanded {
  margin-left: 60px; /* For collapsed sidebar */
  width: calc(100% - 60px);
}

/* Add these styles to your existing CSS file */

.sidebar {
  min-height: 100vh;
  padding-top: 60px;
  transition: width 0.3s ease;
  position: sticky;
  top: 0;
  background-color: #343a40;
  overflow-x: hidden;
  z-index: 100;
}

.sidebar.open {
  width: 270px;
  min-width: 270px;
}

.sidebar.collapsed {
  width: 70px;
  min-width: 70px;
  overflow-x: hidden;
}

.sidebar.collapsed .nav-link {
  text-align: center;
  padding: 0.5rem 0;
}

.sidebar.collapsed .list-group-item {
  padding: 0.5rem 0;
}

.content {
  flex-grow: 1;
  transition: margin-left 0.3s ease;
  padding: 15px;
}

.content-expanded {
  margin-left: 0;
}

.cursor-pointer {
  cursor: pointer;
}

/* Make sure icons are always visible even when sidebar is collapsed */
.sidebar .fa, .sidebar .fas {
  text-align: center;
  width: 20px;
}

/* Adjust badge position for collapsed sidebar */
.sidebar.collapsed .badge {
  position: absolute;
  top: 0;
  right: 5px;
  font-size: 0.65rem;
}

/* Custom scrollbar for webkit browsers */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Section headers in sidebar */
.sidebar h6.text-uppercase {
  font-size: 12px !important;
  letter-spacing: 1px !important;
  font-weight: 600 !important;
  color: rgba(255, 255, 255, 0.5) !important;
  margin-top: 20px;
  margin-bottom: 0px;
  text-transform: uppercase;
}

.sidebar .section-header {
  font-size: 12px !important;
  letter-spacing: 1px !important;
  font-weight: 600 !important;
}

.sidebar hr {
  border-color: rgba(255, 255, 255, 0.08);
  margin: 8px 0;
  opacity: 0.15;
}

.sidebar .section-divider {
  margin: 8px 0;
  opacity: 0.15;
}

.sidebar .list-group-item {
  background-color: transparent !important;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  font-weight: 500;
  letter-spacing: 0.01em;
  margin-bottom: 2px;
  border-radius: 0 8px 8px 0;
  padding-left: 10px; /* Ensure consistent left padding */
  text-align: left; /* Force left alignment */
}

.sidebar .list-group-item:hover {
  background: var(--sidebar-item-hover) !important;
  border-left: 3px solid var(--primary-color);
}

.sidebar .list-group-item .nav-link {
  display: flex !important;
  align-items: center !important;
  color: var(--sidebar-text) !important;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  padding-left: 10px; /* Consistent left padding */
  justify-content: flex-start; /* Force left alignment */
}

.sidebar .list-group-item .nav-link:hover {
  color: var(--sidebar-text-hover) !important;
}

.sidebar .list-group-item .nav-link.active {
  color: var(--sidebar-text-hover) !important;
  font-weight: 600;
}

.sidebar .list-group-item .nav-link i {
  font-size: 16px;
  width: 24px;
  text-align: center;
  margin-right: 8px;
}

.sidebar.open {
  left: 0;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.dropdown-item {
  color: var(--sidebar-text);
  padding: 0.6rem 0.75rem 0.6rem 48px !important;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 2px 0;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background-color: var(--sidebar-item-hover) !important;
  color: var(--sidebar-text-hover);
}

.dropdown-item.active {
  background-color: var(--sidebar-item-active) !important;
  color: var(--sidebar-text-hover);
  font-weight: 600;
}

/* Teams nav styling - enhanced for better visibility */
.sidebar .list-group-item .nav-link[href="/newDS/team"] {
  color: var(--sidebar-text-hover) !important;
  position: relative;
  font-weight: 600;
  background-color: rgba(67, 97, 238, 0.08);
  border-radius: 6px;
  padding: 0.6rem 0.8rem;
  margin: -0.2rem 0;
}

.sidebar .list-group-item .nav-link[href="/newDS/team"]:before {
  content: '';
  position: absolute;
  left: -15px;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--primary-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar .list-group-item .nav-link[href="/newDS/team"]:hover {
  background-color: rgba(67, 97, 238, 0.15);
}

.sidebar .list-group-item .nav-link[href="/newDS/team"]:hover:before,
.sidebar .list-group-item .nav-link[href="/newDS/team"].active:before {
  opacity: 1;
}

.sidebar .list-group-item .nav-link[href="/newDS/team"] i {
  color: var(--primary-color);
  font-size: 17px;
}

.sidebar .list-group-item .nav-link[href="/newDS/team"].active {
  background-color: rgba(67, 97, 238, 0.2);
}

.myDark {
  color: var(--text-primary) !important;
}

/* Content area styles */
.content {
  margin-left: 0;
  width: 100%;
  transition: all 0.3s ease;
  margin-top: 56px; /* Match navbar height */
  padding: 0 20px;
  height: calc(100vh - 56px);
  overflow-y: auto;
  position: relative;
}

/* Modern table styles */
.modern-table-container {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
}

.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--light-color);
  color: var(--text-secondary);
  font-weight: 600;
  text-align: left;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.95rem;
}

.modern-table tr:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

/* Card styles for dashboard */
.dashboard-card {
  background-color: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid var(--border-color);
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.dashboard-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
}

.dashboard-card-header .icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: rgba(67, 97, 238, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.dashboard-card-header .icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.dashboard-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.01em;
}

.dashboard-card-content {
  color: var(--text-secondary);
}

.dashboard-card-content h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.dashboard-card-footer {
  margin-top: 1.25rem;
  display: flex;
  justify-content: flex-end;
}

/* Action buttons */
.action-btn {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
  border: none;
  margin-left: 0.5rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.action-btn.delete:hover {
  background-color: var(--danger-color);
}

.list-group-item {
  border: 0 !important;
}

.navbar .nav-item {
  margin-bottom: -2px;
}

/* Dropdown menu styling */
.nav-item .nav-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.cursor-pointer {
  cursor: pointer;
}

.nav-item .nav-link:hover {
  background-color: var(--sidebar-item-hover);
}

.nav-item .nav-link i.fa-chevron-right,
.nav-item .nav-link i.fa-chevron-down {
  font-size: 12px;
  opacity: 0.7;
  transition: transform 0.2s ease;
  margin-left: auto;
}

.nav-item .nav-link i.fa-chevron-down {
  transform: rotate(0deg);
}

.dropdown-content {
  padding-left: 10px;
  margin-top: 5px;
  max-height: 0; /* Collapsed state */
  overflow: hidden;
  transition: all 0.3s ease-out; /* Smooth transition */
  opacity: 0;
}

.dropdown-content.open {
  max-height: 500px; /* Adjust based on content */
  opacity: 1;
}

/* Mobile-specific dropdown styles */
@media (max-width: 500px) {
  .dropdown-content.open {
    position: relative;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    margin: 5px 0;
    padding: 5px 0;
    max-height: none;
    overflow: visible;
  }
  
  .dropdown-content.open .nav-link {
    padding: 8px 15px;
    color: var(--sidebar-text) !important;
    font-size: 0.9rem;
  }
  
  .dropdown-content.open .nav-link:hover {
    background-color: var(--sidebar-item-hover) !important;
    color: var(--sidebar-text-hover) !important;
  }
}

.dropdown-content .nav-link {
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
}

.fa {
  font-size: 14px;
}

/* Tab styling */
.nav-tabs {
  border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
  color: var(--text-secondary);
  border: none;
  border-bottom: 2px solid transparent;
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
  color: var(--primary-color);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: transparent;
  border-bottom: 2px solid var(--primary-color);
}

/* Responsive adjustments */

@media (max-width: 768px) {
  .dashboard-row {
    flex-direction: column;
  }
  
  .dashboard-col {
    width: 100%;
  }
  
  .sidebar.collapsed {
    width: 50px;
    min-width: 50px;
  }
  
  .content {
    margin-left: 50px;
    width: calc(100% - 50px);
  }
  
  .content-expanded {
    margin-left: 50px;
    width: calc(100% - 50px);
  }
}

@media (max-width: 500px) {
  .content {
    margin-top: 56px;
    padding: 10px;
    margin-left: 50px;
    width: calc(100% - 50px);
  }
  .sidebar {
    transition: .3s ease;
  }

  .sidebar.open + .content {
    margin-left: 0;
    width: 100%;
  }
  
  .sidebar.open {
    transform: translateX(0);
    width: 100%;
  }
  
  .sidebar.collapsed {
    transform: translateX(-100%);
    width: 50px;
  }

  .sidebar.open{
    position: fixed;
    top: 0;
    left: -100%;
  }

  .sidebar.collapsed {
    position: fixed;
    top: 0;
    left: 0px;
    width: 260px;
    transform: none;
  }

  .sidebar .list-group-item .d-none {
    display: block  !important;
  }
    .sidebar.collapsed .list-group-item .fa {
      width: 30px;
    }

    .navbar-brand span {
      font-size: 10px;
            text-overflow: ellipsis;
        width: 60px;
        overflow: hidden;
    }
    .navbar  .searchBtn {
      display: none;
    }
    .searchBar input {
      width:100px
    }
    .navbar .nav .me-3 {
      display: none !important;
    }
}

/* Fix sidebar nav item alignment */
.sidebar .list-group-item {
  background-color: transparent !important;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  font-weight: 500;
  letter-spacing: 0.01em;
  margin-bottom: 2px;
  border-radius: 0 8px 8px 0;
  padding-left: 10px; /* Ensure consistent left padding */
  text-align: left; /* Force left alignment */
}

.sidebar .list-group-item .nav-link {
  display: flex !important;
  align-items: center !important;
  color: var(--sidebar-text) !important;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  padding-left: 10px; /* Consistent left padding */
  justify-content: flex-start; /* Force left alignment */
}

/* Reset center alignment in collapsed sidebar */
.sidebar.collapsed .nav-link {
  text-align: left; /* Override center alignment */
  padding: 0.5rem 0.5rem; /* Padding to maintain proper spacing */
  justify-content: flex-start; /* Force left alignment even when collapsed */
}

.sidebar.collapsed .list-group-item {
  padding: 0.25rem 0.25rem 0.25rem 0.5rem; /* Maintain left padding */
  display: block; /* Reset any flex display */
  text-align: left; /* Force left alignment */
}

/* Icon positioning */
.sidebar .fa, .sidebar .fas {
  margin-right: 8px;
  text-align: center;
  width: 20px;
  min-width: 20px; /* Ensure icon width is maintained */
}

.sidebar.collapsed .fa, 
.sidebar.collapsed .fas {
  margin-right: 0; /* Remove margin when collapsed */
  width: 100%; /* Take full width to allow centering */
}

/* Badge positioning for collapsed sidebar */
.sidebar.collapsed .badge {
  position: absolute;
  top: 5px; /* Adjust as needed */
  right: 5px; /* Position to the right */
  font-size: 0.65rem;
}

/* Fix alignment of sidebar items and submenus */

/* Ensure all list items are left-aligned */
.sidebar .list-group-item {
  text-align: left;
  padding-left: 15px;
}

/* Make all nav links align left */
.sidebar .nav-link {
  justify-content: flex-start !important;
  text-align: left !important;
  padding-left: 15px;
}

/* Fix collapsed sidebar icon alignment */
.sidebar.collapsed .list-group-item {
  display: block;
  text-align: left;
}

.sidebar.collapsed .nav-link {
  justify-content: flex-start !important;
  padding-left: 15px;
}

/* Fix dropdown items to align left */
.dropdown-item {
  text-align: left !important;
  padding-left: 35px !important;
}

.nav.flex-column .dropdown-item {
  padding-left: 35px !important;
}

/* Fix submenu alignment */
.sidebar .nav-item .dropdown-content .nav-link,
.sidebar .nav-item .nav.flex-column .nav-link {
  padding-left: 35px !important;
  text-align: left !important;
}

/* Add tooltips for collapsed sidebar items */
.sidebar.collapsed .list-group-item .nav-link {
  position: relative;
}

.sidebar.collapsed .list-group-item .nav-link:hover::after,
.sidebar.collapsed .nav-item .nav-link:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 60px; /* Position to the right of the sidebar */
  top: 50%;
  transform: translateY(-50%);
  background: var(--darker-color);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1050;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none; /* Prevent the tooltip from interfering with hover */
  opacity: 1;
  transition: opacity 0.2s ease;
}

/* Arrow for tooltip */
.sidebar.collapsed .list-group-item .nav-link:hover::before,
.sidebar.collapsed .nav-item .nav-link:hover::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: transparent var(--darker-color) transparent transparent;
  z-index: 1050;
}

/* Ensure tooltip is properly positioned for dropdown menu items */
.sidebar.collapsed .nav-item {
  position: relative;
}

@keyframes fastShake {
  0%, 100% { transform: rotate(0deg); color: inherit; }
  10% { transform: rotate(-15deg); }
  20% { transform: rotate(15deg); }
  30% { transform: rotate(-12deg); color: red; }
  40% { transform: rotate(12deg); color: red; }
  50% { transform: rotate(-10deg); color: red; }
  60% { transform: rotate(10deg); color: red; }
  70% { transform: rotate(-5deg); color: red; }
  80% { transform: rotate(5deg); color: red; }
  90% { transform: rotate(-2deg); color: red; }
}

.fast-shake {
  animation: fastShake 5s ease-in-out;
  display: inline-block;
}
