import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON> } from "react-bootstrap";
import styles from "./NumberingModal.module.css"; // Import CSS module
import { findById } from "../../../services/apiService";

const NumberingModal = ({ isOpen, onClose, onSubmit, id }) => {
  const [numberingType, setNumberingType] = useState("auto");
  const [name, setName] = useState("");
  const [numberingScheme, setNumberingScheme] = useState();
  const [nextNumber, setNextNumber] = useState(1);
  const [preview, setPreview] = useState("");
  const [selectedSchemeOptions, setSelectedSchemeOptions] = useState([]);

  const [selectedValues, setSelectedValues] = useState([]);
  const options = ["Files", "Links"];
  const [itemsOpen, setItemsOpen] = useState(false);

  const [reset, setReset] = useState("Never");
  const [showYear, setShowYear] = useState(false);
  const [showMonth, setShowMonth] = useState(false);
  const [showWeek, setShowWeek] = useState(false);

  const [month, setMonth] = useState("January");
  const [day, setDay] = useState("1");
  const [weekday, setWeekday] = useState("Sunday");

  const [currentTime, setCurrentTime] = useState({
    hours: "",
    minutes: "",
    seconds: "",
  });

  // Months Array
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Days Array
  const days = Array.from({ length: 31 }, (v, i) => i + 1);

  // Weekdays array
  const weekdays = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  // Scheme Options Array
  const schemeOptions = [
    "",
    "Number",
    "Day",
    "Month",
    "Year",
    "Hours",
    "Minutes",
    "Seconds",
  ];

  const [createdBy, setCreatedBy] = useState("");
  const [createdDate, setCreatedDate] = useState("");

  // Handle Reset frequency
  const handleReset = (event) => {
    let resetValue = event.target.value;
    setReset(resetValue);
    if (resetValue === "Never" || resetValue === "Daily") {
      setShowYear(false);
      setShowMonth(false);
      setShowWeek(false);
    }
    if (resetValue === "Yearly") {
      setShowYear(true);
      setShowMonth(false);
      setShowWeek(false);
    }
    if (resetValue === "Monthly") {
      setShowYear(false);
      setShowMonth(true);
      setShowWeek(false);
    }
    if (resetValue === "Weekly") {
      setShowYear(false);
      setShowMonth(false);
      setShowWeek(true);
    }
  };

  // Handle items open
  const handleItemsOpen = () => {
    setItemsOpen(!itemsOpen);
  };

  // Handle checkbox changes
  const handleCheckboxChange = (event, option) => {
    if (event.target.checked) {
      setSelectedValues([...selectedValues, option]);
    } else {
      setSelectedValues(selectedValues.filter((val) => val !== option));
    }
  };

  // Handle scheme type selection and update preview dynamically
  const handleSchemeOptionChange = (e) => {
    const selectedOption = e.target.value;

    // Only add selectedOption if it's not empty and not already in the scheme
    if (selectedOption && !selectedSchemeOptions.includes(selectedOption)) {
      setSelectedSchemeOptions((prev) => {
        const updatedOptions = [...prev, selectedOption];
        return updatedOptions;
      });

      setNumberingScheme((prev) => {
        // Wrap the selected option with angular brackets
        const newOptionWithBrackets = `<< ${selectedOption} >>`;
        const newScheme = prev
          ? `${prev} ${newOptionWithBrackets}`
          : newOptionWithBrackets; // Avoid starting with an empty space
        setPreview(generatePreview(newScheme)); // Update the preview dynamically
        return newScheme; // Return the updated scheme with angular brackets around the selected option
      });
    }
  };

  const removeSchemeOption = (option) => {
    // Remove the option from selectedSchemeOptions
    const updatedOptions = selectedSchemeOptions.filter(
      (item) => item !== option
    );
    setSelectedSchemeOptions(updatedOptions);

    // Create a regex pattern to match the option within `<< >>` and remove only that part from numberingScheme
    const optionPattern = new RegExp(`<<\\s*${option}\\s*>>`, "g");
    const updatedScheme = numberingScheme
      .replace(optionPattern, "")
      .replace(/\s{2,}/g, " ")
      .trim();

    // Update numberingScheme without affecting other text
    setNumberingScheme(updatedScheme);
    setPreview(generatePreview(updatedScheme));
  };

  // Generate preview based on selected scheme
  const generatePreview = (scheme) => {
    const currentDate = new Date();
    let newPreview = scheme;

    // Remove angular brackets (<< >>) from the preview text
    newPreview = newPreview.replace(/<<\s*|\s*>>/g, "").trim();

    // Handle each option in the scheme (replace placeholders)
    if (newPreview.includes("Number")) {
      newPreview = newPreview.replace("Number", nextNumber); // Replace 'Number' with the next number
    }
    if (newPreview.includes("Day")) {
      newPreview = newPreview.replace(
        "Day",
        currentDate.toLocaleString("en-US", { weekday: "short" })
      );
    }
    if (newPreview.includes("Month")) {
      newPreview = newPreview.replace(
        "Month",
        currentDate.toLocaleString("en-US", { month: "short" })
      );
    }
    if (newPreview.includes("Year")) {
      newPreview = newPreview.replace("Year", currentDate.getFullYear());
    }
    if (newPreview.includes("Hours")) {
      newPreview = newPreview.replace("Hours", currentDate.getHours());
    }
    if (newPreview.includes("Minutes")) {
      newPreview = newPreview.replace("Minutes", currentDate.getMinutes());
    }
    if (newPreview.includes("Seconds")) {
      newPreview = newPreview.replace("Seconds", currentDate.getSeconds());
    }

    return newPreview;
  };

  const fetchNumberingById = async (id) => {
    const api = `/numbering/${id}`;
    try {
      const response = await findById(api);
      console.log(response.data);
      const data = response.data;
      if (data.isDeleted === true) {
        setNumberingType("inactive");
      } else {
        setNumberingType("active");
      }
      setName(data.numberingName);
      setNumberingScheme(data.schemeName);
      setPreview(generatePreview(data.schemeName));
      setNextNumber(data.startNumber);
      const schemeTypeValues = data.schemeType.map((item) => item.schemeType);
      setSelectedSchemeOptions(schemeTypeValues);
      const entityTypeValues = data.entityType.map((i) => i.entityType);
      setSelectedValues(entityTypeValues);
      setReset(data.resetFrequency);
      if (data.resetFrequency === "Yearly") {
        const [month, day] = data.resetTime.split("-");
        setMonth(month);
        setDay(day);
        setShowYear(true);
        setShowMonth(false);
        setShowWeek(false);
      }
      if (data.resetFrequency === "Monthly") {
        setDay(data.resetTime);
        setShowYear(false);
        setShowMonth(true);
        setShowWeek(false);
      }
      if (data.resetFrequency === "Weekly") {
        setWeekday(data.resetTime);
        setShowYear(false);
        setShowMonth(false);
        setShowWeek(true);
      }
      setCreatedBy(data.createdBy);
      setCreatedDate(data.createdDate);
    } catch (error) {
      //throw error;
    }
  };

  useEffect(() => {
    fetchNumberingById(id);
    // Update current time every second
    const interval = setInterval(() => {
      const currentDate = new Date();
      setCurrentTime({
        hours: currentDate.getHours(),
        minutes: currentDate.getMinutes(),
        seconds: currentDate.getSeconds(),
      });
    }, 1000);

    return () => clearInterval(interval); // Cleanup interval on component unmount
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // if (numberingType === "inactive") {
    //   const data = {
    //     id: id,
    //     isDeleted: true,
    //   };
    //   onSubmit(data);
    // } else if (numberingType === "inactive") {
    //   const data = {
    //     auto: true,
    //   };
    //   onSubmit(data);
    // } else {
      let resetDuration = "";

      if (reset === "Never") {
        resetDuration = "";
      } else if (reset === "Yearly") {
        resetDuration = month + "-" + day;
      } else if (reset === "Monthly") {
        resetDuration = day;
      } else if (reset === "Weekly") {
        resetDuration = weekday;
      } else {
        resetDuration = "";
      }

      const cleanedScheme = numberingScheme.replace(/\s+/g, "");

      let schemeType = selectedSchemeOptions.map((option) => ({
        schemeType: option,
      }));

      let entityType = selectedValues.map((option) => ({ entityType: option }));

      console.log("Numbering Scheme:", cleanedScheme);
      if (id) {
        const data = {
          id: id,
          numberingName: name,
          schemeName: cleanedScheme,
          schemeType: schemeType,
          startNumber: nextNumber,
          entityType: entityType,
          resetFrequency: reset,
          resetTime: resetDuration,
          createdBy: createdBy,
          createdDate: createdDate,
        };
        onSubmit(data);
      } else {
        const data = {
          numberingName: name,
          schemeName: cleanedScheme,
          schemeType: schemeType,
          startNumber: nextNumber,
          entityType: entityType,
          resetFrequency: reset,
          resetTime: resetDuration,
        };
        onSubmit(data);
      }
    // }
    onClose();
  };

  return (
    <Modal
      show={isOpen}
      onHide={() => {onClose();}}
      centered
      size="lg"
      style={{ maxHeight: "90vh" }}
    >
      <Modal.Header
        closeButton
        className="modal-header-modern"
      >
        <Modal.Title style={{ fontSize: "18px", width: "100%", margin: 0 }}>Set Numbering Scheme</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "70vh", overflowY: "auto", padding: "15px" }}>
        <form className={styles.formModal} onSubmit={handleSubmit}>
          {/* <div className="mb-3">
            <p className={styles.description} style={{ marginBottom: "15px" }}>
              Please Select a numbering scheme
            </p>
          </div> */}

          {/* Radio Button Options */}
          {/* <div className="mb-3">
            <div className="d-flex gap-3">
              <label className={styles.label}>
                <input
                  type="radio"
                  name="numberingType"
                  value="auto"
                  checked={numberingType === "auto"}
                  onChange={() => setNumberingType("auto")}
                  className={styles.inputRadio}
                />
                Auto
              </label>
              <label className={styles.label}>
                <input
                  type="radio"
                  name="numberingType"
                  value="active"
                  checked={numberingType === "active"}
                  onChange={() => setNumberingType("active")}
                  className={styles.inputRadio}
                />
                Active
              </label>
              <label className={styles.label}>
                <input
                  type="radio"
                  name="numberingType"
                  value="inactive"
                  checked={numberingType === "inactive"}
                  onChange={() => setNumberingType("inactive")}
                  className={styles.inputRadio}
                />
                Inactive
              </label>
            </div>
          </div> */}

          {/* Active Configuration */}
          {/* {numberingType === "active" && ( */}
            {/*  <> */}
              {/* Name Field */}
              <div className="row mb-3">
                <div className="col-9">
                <label htmlFor="name" className={styles.label}>
                  Name
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  placeholder="Enter numbering name"
                />
                </div>
                <div className="col-3">
                  <label htmlFor="schemeOptions" className={styles.label}>
                    <i
                      className="fa fa-plus-circle"
                      aria-hidden="true"
                      style={{ color: "blue", fontSize: "20px" }}
                    ></i> Select scheme type
                  </label>
                  <select
                    id="schemeOptions"
                    className="form-control"
                    onChange={handleSchemeOptionChange}
                  >
                    {schemeOptions.map((option, index) => (
                      <option key={index} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Scheme and Options */}
              <div className="row mb-3">
                <div className="">
                  <label htmlFor="scheme" className={styles.label}>
                    Scheme
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="scheme"
                    required
                    value={numberingScheme}
                    placeholder="enter scheme name"
                    onChange={(e) => setNumberingScheme(e.target.value)}
                  />
                </div>
               
              </div>


                            {/* Next Number and Preview */}
                            <div className="row mb-3">
                <div className="col-4">
                  <label htmlFor="nextNumber" className={styles.label}>
                    Next Number
                  </label>
                  <input
                    type="number"
                    className="form-control"
                    id="nextNumber"
                    value={nextNumber}
                    onChange={(e) => setNextNumber(e.target.value)}
                  />
                </div>
                <div className="col-8">
                  <label htmlFor="preview" className={styles.label}>
                    Preview
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="preview"
                    value={preview}
                    readOnly
                    style={{ backgroundColor: "#f8f9fa" }}
                  />
                </div>
              </div>
              
              {/* Selected Scheme Options */}
              {selectedSchemeOptions.length > 0 && (
                <div className="mb-3 col-4">
                  <label className={styles.label}>
                    Selected Scheme Options
                  </label>
                  <div style={{ maxHeight: "100px", overflowY: "auto", border: "1px solid #ddd", padding: "8px", borderRadius: "4px" }}>
                    {selectedSchemeOptions.map((option, index) => (
                      <div
                        key={index}
                        className="d-flex justify-content-between align-items-center py-1"
                      >
                        <span>{option}</span>
                        <button
                          type="button"
                          onClick={() => removeSchemeOption(option)}
                          className="btn btn-sm btn-outline-danger"
                        >
                          <i className="fa fa-trash"></i>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Number Following Items and Reset Frequency in one row */}
{/* Number Following Items and Reset Frequency in one row */}
<div className="row mb-3">
  {/* Number Following Items */}
  <div className="col-4">
    <label htmlFor="items" className={styles.label}>
      Number the Following items
    </label>
    <input
      type="text"
      id="items"
      value={selectedValues.join(", ")}
      className="form-control"
      onClick={handleItemsOpen}
      readOnly
      style={{ cursor: "pointer", backgroundColor: "#f8f9fa" }}
    />
    {itemsOpen && (
      <div
        style={{
          border: "1px solid #ccc",
          padding: "10px",
          marginTop: "10px",
          borderRadius: "4px",
          backgroundColor: "#f9f9f9",
        }}
      >
        {options.map((option, index) => (
          <div
            key={index}
            className="d-flex justify-content-between align-items-center py-1"
          >
            <span>{option}</span>
            <input
              type="checkbox"
              checked={selectedValues.includes(option)}
              onChange={(e) => handleCheckboxChange(e, option)}
            />
          </div>
        ))}
      </div>
    )}
  </div>

  {/* Reset Frequency */}
  <div className="col-4">
    <label htmlFor="reset" className={styles.label}>
      Reset Frequency
    </label>
    <select
      id="reset"
      className="form-control"
      value={reset}
      onChange={handleReset}
    >
      <option value="Never">Never</option>
      <option value="Yearly">Yearly</option>
      <option value="Monthly">Monthly</option>
      <option value="Weekly">Weekly</option>
      <option value="Daily">Daily</option>
    </select>
  </div>

  {/* Conditional Reset Options - All in same row */}
  {showYear && (
    <div className="col-2">
      <label htmlFor="month" className={styles.label}>
        Month
      </label>
      <select
        id="month"
        className="form-control"
        value={month}
        onChange={(e) => setMonth(e.target.value)}
      >
        {months.map((month, index) => (
          <option key={index} value={month}>
            {month}
          </option>
        ))}
      </select>
    </div>
  )}
  {showYear && (
    <div className="col-2">
      <label htmlFor="daySelect" className={styles.label}>
        Day
      </label>
      <select
        id="daySelect"
        className="form-control"
        value={day}
        onChange={(e) => setDay(e.target.value)}
      >
        {days.map((day, index) => (
          <option key={index} value={day}>
            {day}
          </option>
        ))}
      </select>
    </div>
  )}

  {showMonth && (
    <div className="col-4">
      <label htmlFor="daySelectMonth" className={styles.label}>
        Day
      </label>
      <select
        id="daySelectMonth"
        className="form-control"
        value={day}
        onChange={(e) => setDay(e.target.value)}
      >
        {days.map((day, index) => (
          <option key={index} value={day}>
            {day}
          </option>
        ))}
      </select>
    </div>
  )}

  {showWeek && (
    <div className="col-4">
      <label htmlFor="week" className={styles.label}>
        WeekDay
      </label>
      <select
        id="week"
        className="form-control"
        value={weekday}
        onChange={(e) => setWeekday(e.target.value)}
      >
        {weekdays.map((weekday, index) => (
          <option key={index} value={weekday}>
            {weekday}
          </option>
        ))}
      </select>
    </div>
  )}
</div>
            {/* </> */}
          {/* )} */}

          {/* Submit Button */}
          <div className="d-flex justify-content-end mt-4">
            <Button
              type="submit"
              className={styles.submitButton}
              variant="primary"
            >
              Save
            </Button>
          </div>
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default NumberingModal;
