import React, { Component } from "react";
import Loader from "../loader/Loader";
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import DownloadReport from '../common/DownloadReport';
import { formatDate, getDisplayPath, getList } from "../../services/apiService";
import { DataTable } from "../Table/DataTable";
import CustomBreadcrumb from "../common/CustomBreadcrumb";

class RetentionReport extends Component {
  state = {
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    retentionList: [],
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    deployedURL: "",
  };

  componentDidMount() {
    this.fetchRetentionEndList();
    const deployedURL = window.location.href.split('/#')[0];
    this.setState({deployedURL: deployedURL});
  }

  fetchRetentionEndList = async (page = 0, itemsPerPage = this.state.itemsPerPage) => {
    this.setState({ isLoading: true });
    const api = `/documentreports/list_by_all_retention_end_files?page=${page}&size=${itemsPerPage}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        retentionList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (e) {
      console.log(e);
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchRetentionEndList(page);
  };

  handleItemsPerPageChange = (newSize) => {
    this.setState({ itemsPerPage: newSize }, () => {
      this.fetchRetentionEndList(0, newSize);
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true,
      },
    });
  };

  calculateDaysUntil = (jobDate) => {
    const now = new Date();
    const jobDateObj = new Date(jobDate);

    const differenceInTime = jobDateObj - now; // Time difference in milliseconds

    if (differenceInTime > 0) {
      const differenceInDays = Math.floor(
        differenceInTime / (1000 * 60 * 60 * 24)
      ); // Convert to days
      if (differenceInDays >= 1) {
        return `Due in ${differenceInDays} day${
          differenceInDays > 1 ? "s" : ""
        }`;
      } else {
        const hours = Math.floor(differenceInTime / (1000 * 60 * 60)); // Convert to hours
        const minutes = Math.floor(
          (differenceInTime % (1000 * 60 * 60)) / (1000 * 60)
        ); // Remaining minutes
        return `Due in ${hours} hour${hours > 1 ? "s" : ""} ${minutes} minute${
          minutes > 1 ? "s" : ""
        }`;
      }
    } else {
      // If the due date has passed, return an empty string
      return "";
    }
  };

  render() {
    const { retentionList, deployedURL, itemsPerPage, currentPage, totalItems } = this.state;

    const columns = [
      {
        key: 'index',
        header: 'S.No',
        render: (_, row, index) => currentPage * itemsPerPage + index + 1
      },
      {
        key: 'documentName',
        header: 'File Name',
        sortable: true,
        render: (value) => (
          <span style={{ color: "#333" }}>
            {value}
          </span>
        )
      },
      {
        key: 'filePath',
        header: 'Location',
        sortable: true,
        render: (value) => value && getDisplayPath(value)
      },
      {
        key: 'jobDate',
        header: 'Retention End',
        sortable: true,
        render: (value) => formatDate(value),
      },
      {
        key: 'daysUntilRetention', // Changed key to be unique
        header: 'Days until Retention Ends',
        sortable: true,
        render: (_, row) => this.calculateDaysUntil(row.jobDate), // Use row.jobDate since we changed the key
      },
      {
        key: 'messageStatus',
        header: 'Action',
        sortable: true
      }
    ];

    return (
      <>
        <div className="container mt-3">
          {this.state.notification.show && (
            <Notification
              message={this.state.notification.message}
              type={this.state.notification.type}
              onClose={this.closeNotification}
            />
          )}
          <div className="row align-items-center">
            <div className="col-8">
              <h5 className="pt-1">Nearing Retention End Report</h5>
            </div>
            <div className="col-4 text-end">
              <div className="d-inline-flex align-items-center gap-2">
                <DownloadReport
                  excelEndpoint={`${GlobalConstants.globalURL}/documentreports/retentionEndReport/exportAllRetentionEndExcel`}
                  pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/retentionEndReport/exportAllRetentionEndPDF`}
                  reportName="RetentionEndReport"
                  onError={this.handleDownloadError}
                  buttonStyle={{ background: '#fff', color: '#333', border: '1px solid #ccc', fontSize: '16px' }}
                />
                <button
                  className="btn btn-link"
                  style={{ textDecoration: 'underline', color: '#1976d2', fontWeight: 500, fontSize: '16px' }}
                  onClick={() => window.history.back()}
                >
                  Back
                </button>
              </div>
            </div>
          </div>


          <div className="row mt-3">
            <h6 style={{ fontWeight: "500" }}>
              {this.state.totalItems} documents with retention end in the next
              30 days
            </h6>
            <div className="col-12">
              <DataTable
                data={retentionList.filter(file => file !== null)}
                columns={columns}
                totalItems={totalItems}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                className="font-weight-normal"
              />
            </div>
          </div>
        </div>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default RetentionReport;
