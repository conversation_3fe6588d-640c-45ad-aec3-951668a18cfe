import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { DataTable } from "../Table/DataTable";
import NumberingModal from "./NumberingModal/NumberingModal";
import Notification from "../Notification/Notification";
import { getList ,addNew,deleteById} from "../../services/apiService";

class Numbering extends Component {
  state = {
    modalOpen: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    modalId: null,
    numberingList:[],
    statusModalOpen:false,
  };

  componentDidMount() 
  {
    this.fetchNumberingList();
  }

  fetchNumberingList = async () => {
    const api = `/numbering/list`;
    try{
      const response=await getList(api);
      console.log(response)
      this.setState({
        numberingList:response.data,
      })
    }
    catch(error){
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleOpenModal = (id) => {
    this.setState({ modalOpen: true,modalId:id });
  };

  handleOpenStatusModal = (id) => {
    this.setState({ statusModalOpen: true,modalId:id });
  };

  handleCloseStatusModal = (id) => {
    this.setState({ statusModalOpen: false,modalId:id });
  };

  handleCloseModal = () => {
    this.setState({ modalOpen: false,modalId:null });
  };

  handleSubmitModal = async (data) => {
    console.log("Numbering Data Submitted:", data);
    if (data.isDeleted === true) {
      const id = data.id;
      const api = `/numbering/${id}`;
      //alert(api);
      try {
        const response = await deleteById(api);
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
        });
        this.setState({ modalOpen: false });
        await this.fetchNumberingList();
        return response.data;
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
        });
        //throw error;
      }
      finally{
        this.fetchNumberingList();
      }
    } 
    else if (data.auto===true){
      console.log("Auto selected")
      await this.fetchNumberingList();
    }
    else {
      const api = `/numbering`;
      try {
        const response = await addNew(api, data);
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
        });
        this.setState({ modalOpen: false });
        return response.data;
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
        });
        //throw error;
      }
      finally{
        this.fetchNumberingList();
      }
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleSetActiveNumbering = async () => {
    const { modalId } =this.state; 
    const api=`/numbering/change-status/${modalId}`;
    try{
      const resp=await getList(api);
      const msg=resp.data.isDeleted;
      this.setState({
        notification: {
          message: `Numbering set as ${msg ? "inactive" : "active"} successfully`,
          type: "success",
          show: true,
        },
      });
      this.fetchNumberingList();
      this.handleCloseStatusModal();
    }
    catch(e){}
  }

  render() {
    return (
      <div className="container mt-3">
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row">
        <div className="col-sm-4 col-12">
            <h4>Numbering</h4>
          </div>
          <div className="col-12">
            <DataTable
              data={this.state.numberingList.map((num, i) => ({
                ...num,
                serialNo: i + 1,
                status: num.isDeleted ? "Inactive" : "Active"
              }))}
              columns={[
                {
                  key: "serialNo",
                  header: "S No.",
                  sortable: true,
                  width: "10%"
                },
                {
                  key: "numberingName",
                  header: "Name",
                  sortable: true,
                  width: "50%"
                },
                {
                  key: "status",
                  header: "Status",
                  sortable: true,
                  width: "20%",
                  render: (value) => (
                    <Badge bg={value === "Active" ? "success" : "danger"}>
                    {value}
                    </Badge>
                    // <span className={value === "Active" ? "text-success" : "text-danger"}>
                    //   {value}
                    // </span>
                  )
                },
                {
                  key: "settings",
                  header: "Settings",
                  width: "20%",
                  render: (_, row) => (
                    <>
                    <Button
                      variant="gray"
                      onClick={() => this.handleOpenModal(row.id)}
                    >
                      <i className="fa fa-gear"></i>
                    </Button>
                    <Button
                      variant="gray"
                      onClick={() => this.handleOpenStatusModal(row.id)}
                    >
                      {row.isDeleted ? "Make active" : "Make inactive"}
                    </Button>
                    </>
                  )
                }
              ]}
              searchable={true}
              className="table-hover"
              itemsPerPage={10}
            />
            {this.state.modalOpen && (
              <NumberingModal
                id={this.state.modalId}
                isOpen={this.state.modalOpen}
                onClose={this.handleCloseModal}
                onSubmit={this.handleSubmitModal}
              />
            )}
          </div>
        </div>

         <div>
                    <Modal
                      show={this.state.statusModalOpen}
                      onHide={this.handleCloseStatusModal}
                      size="sm"
                    >
                      
                      <Modal.Header closeButton className="modal-header-modern py-2">
                        <Modal.Title style={{ fontSize: "1rem" }}>
                          Set Status
                        </Modal.Title>
                      </Modal.Header>
                      <Modal.Body className="p-3">
                        <p className="small mb-2">
                          Do you want to change the status for this numbering
                        </p>
                        <Button variant="info" size="sm" onClick={this.handleSetActiveNumbering}>
                          Change status
                        </Button>
                      </Modal.Body>
                    </Modal>
                  </div>
      </div>
    );
  }
}

export default Numbering;
