import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Table, Form, InputGroup, Pagination, Badge, Button } from 'react-bootstrap';
import { ChevronDown, ChevronUp, Search, X } from 'lucide-react';

export function DataTable({
  data,
  columns,
  searchable = true,
  className,
  itemsPerPage = 5,
  totalItems,
  currentPage: externalCurrentPage,
  onPageChange,
  onItemsPerPageChange,
  onSearch,
  searchTerm: externalSearchTerm,
  showSno = false,
  onSort,
  sortColumn,
  sortDirection,
  hidePagination = false
}) {
  // Use internal sort config, but initialize from props if provided
  const [internalSortConfig, setInternalSortConfig] = useState(null);
  
  // Use external sort config if provided, otherwise use internal
  const sortConfig = onSort && sortColumn ? 
    { key: sortColumn, direction: sortDirection || 'asc' } : 
    internalSortConfig;
    
  const [searchTerm, setSearchTerm] = useState(externalSearchTerm || '');

  // Use ref to store the actual items per page value
  const itemsPerPageRef = useRef(itemsPerPage);

  // Listen for the custom itemsPerPageChange event
  useEffect(() => {
    const handleItemsPerPageChange = (event) => {
      const { newSize } = event.detail;
      itemsPerPageRef.current = newSize;
    };

    window.addEventListener('itemsPerPageChange', handleItemsPerPageChange);

    return () => {
      window.removeEventListener('itemsPerPageChange', handleItemsPerPageChange);
    };
  }, []);

  // Use external pagination if provided, otherwise use internal
  const currentPage = externalCurrentPage !== undefined ? externalCurrentPage + 1 : 1;

  // Use the effective items per page (either from props or internal state)
  const effectiveItemsPerPage = onItemsPerPageChange ? itemsPerPage : itemsPerPageRef.current;

  // Calculate SNO start
  const snoStart = ((currentPage - 1) * effectiveItemsPerPage) + 1;

  // If showSno, prepend SNO column definition
  const columnsWithSno = useMemo(() => {
    if (!showSno) return columns;
    return [
      {
        key: '__sno__',
        header: 'S.No',
        width: '60px',
        sortable: true,
        render: (_value, _row, index) => snoStart + index,
        sortValue: (_row, index) => snoStart + index
      },
      ...columns
    ];
  }, [showSno, columns, snoStart]);

  const handleSort = (key) => {
    // If external sort handler is provided, use it
    if (onSort) {
      // Determine the new direction
      let newDirection = 'asc';
      if (sortColumn === key && sortDirection === 'asc') {
        newDirection = 'desc';
      }
      
      // Call the external sort handler
      onSort(key);
      
      // Reset to first page when sorting
      if (onPageChange) {
        onPageChange(0);
      }
      
      return;
    }
    
    // Otherwise use internal sorting
    setInternalSortConfig(current => {
      console.log('current::::', current)
      if (!current || current.key !== key) {
        return { key, direction: 'asc' };
      }
      if (current.direction === 'asc') {
        return { key, direction: 'desc' };
      }
      return null;
    });

    // Reset to first page when sorting
    if (onPageChange) {
      onPageChange(0);
    }
  };

  const filteredAndSortedData = useMemo(() => {
    let processed = [...data];

    if (searchTerm) {
      processed = processed.filter(item => {
        // Only search columns that are searchable (default true)
        const searchableColumns = columnsWithSno.filter(col => col.key !== '__sno__' && col.searchable !== false);
        // First check regular fields
        const hasMatch = searchableColumns.some(col => {
          const value = item[col.key];
          return value !== null && value !== undefined && String(value).toLowerCase().includes(searchTerm.toLowerCase());
        });
        if (hasMatch) return true;
        // If no match in regular fields, check fields used in column render functions
        return searchableColumns.some(column => {
          // Get the displayed value from the render function if available
          if (column.render) {
            const renderedValue = column.render(item[column.key], item);
            if (renderedValue !== null && renderedValue !== undefined) {
              if (typeof renderedValue === 'string') {
                return renderedValue.toLowerCase().includes(searchTerm.toLowerCase());
              } else if (typeof renderedValue === 'number') {
                return String(renderedValue).toLowerCase().includes(searchTerm.toLowerCase());
              }
            }
          }
          return false;
        });
      });
    }

    // if (sortConfig) {
    //   const column = columnsWithSno.find(col => col.key === sortConfig.key);
    //   processed.sort((a, b) => {
    //     // Use sortValue if available, then fall back to the raw property
    //     let aValue, bValue;
        
    //     if (sortConfig.key === '__sno__') {
    //       // Special handling for S.No column
    //       const aIndex = processed.indexOf(a);
    //       const bIndex = processed.indexOf(b);
    //       aValue = snoStart + aIndex;
    //       bValue = snoStart + bIndex;
    //     } else {
    //       aValue = a[sortConfig.key];
    //       bValue = b[sortConfig.key];

    //       if (column && column.sortValue) {
    //         aValue = column.sortValue(a, processed.indexOf(a));
    //         bValue = column.sortValue(b, processed.indexOf(b));
    //       }
    //     }

    //     // Handle null/undefined values for proper sorting
    //     if (aValue === null || aValue === undefined) aValue = '';
    //     if (bValue === null || bValue === undefined) bValue = '';

    //     // Convert to strings for comparison if they're not numbers
    //     if (typeof aValue !== 'number' && typeof bValue !== 'number') {
    //       aValue = String(aValue);
    //       bValue = String(bValue);
    //     }

    //     if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
    //     if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
    //     return 0;
    //   });
    // }

    if (sortConfig) {
      const column = columnsWithSno.find(col => col.key === sortConfig.key);
      processed.sort((a, b) => {
        // Use sortValue if available, then fall back to the raw property
        let aValue, bValue;
        
        if (sortConfig.key === '__sno__') {
          // Special handling for S.No column
          const aIndex = processed.indexOf(a);
          const bIndex = processed.indexOf(b);
          aValue = snoStart + aIndex;
          bValue = snoStart + bIndex;
        } else {
          aValue = a[sortConfig.key];
          bValue = b[sortConfig.key];
    
          if (column && column.sortValue) {
            aValue = column.sortValue(a, processed.indexOf(a));
            bValue = column.sortValue(b, processed.indexOf(b));
          }
        }
    
        // Handle null/undefined values for proper sorting
        if (aValue === null || aValue === undefined) aValue = '';
        if (bValue === null || bValue === undefined) bValue = '';
    
        // Convert to strings for comparison if they're not numbers
        if (typeof aValue !== 'number' && typeof bValue !== 'number') {
          aValue = String(aValue);
          bValue = String(bValue);
          
          // Case-insensitive comparison for strings
          const aLower = aValue.toLowerCase();
          const bLower = bValue.toLowerCase();
          if (aLower < bLower) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aLower > bLower) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }
    
        // Original comparison for numbers
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return processed;
  }, [data, sortConfig, searchTerm, columnsWithSno]);

  // Use external totalItems if provided, otherwise use filtered data length
  const effectiveTotalItems = totalItems !== undefined ? totalItems : filteredAndSortedData.length;
  const totalPages = Math.ceil(effectiveTotalItems / effectiveItemsPerPage);

  // If using external pagination, use all data provided (already paginated from server)
  // Otherwise, paginate the filtered data client-side
  const paginatedData = onPageChange
    ? filteredAndSortedData
    : filteredAndSortedData.slice(
        (currentPage - 1) * effectiveItemsPerPage,
        currentPage * effectiveItemsPerPage
      );

  const handlePageChange = (page) => {
    if (onPageChange) {
      // External pagination - convert to 0-based index for API
      onPageChange(page - 1);
    }
  };

  // Update internal search term when external one changes
  useEffect(() => {
    if (externalSearchTerm !== undefined) {
      setSearchTerm(externalSearchTerm);
    }
  }, [externalSearchTerm]);

  // Smart pagination logic
  const getPageNumbers = () => {
    const delta = 1; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    // Always include first page
    range.push(1);

    // Add pages around current page
    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    // Always include last page (if different from first)
    if (totalPages > 1) {
      range.push(totalPages);
    }

    let l;
    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push('...');
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  };

  return (
    <div className="table-responsive">
      {searchable && (
        <div className="mb-3">
          <InputGroup>
            <InputGroup.Text>
              <Search size={20} />
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="Search all columns..."
              value={searchTerm}
              onChange={(e) => {
                const newSearchTerm = e.target.value;
                setSearchTerm(newSearchTerm);
                
                // Call external handler if provided
                if (onSearch) {
                  onSearch(newSearchTerm);
                }
                
                // Reset to first page when searching
                if (onPageChange && !onSearch) {
                  onPageChange(0);
                }
              }}
            />
            {searchTerm && (
              <Button 
                variant="outline-secondary"
                onClick={() => {
                  setSearchTerm('');
                  if (onSearch) {
                    onSearch('');
                  }
                  if (onPageChange && !onSearch) {
                    onPageChange(0);
                  }
                }}
              >
                <X size={16} />
              </Button>
            )}
          </InputGroup>
        </div>
      )}

      <Table hover className={className}>
        <thead>
          <tr>
            {columnsWithSno.map((column) => (
              <th
                key={String(column.key)}
                style={{
                  width: column.width,
                  backgroundColor: '#d4e6ff'
                }}
              >
                <div className="d-flex align-items-center gap-2">
                  <span>{column.header}</span>
                  {column.sortable && (
                    <Button
                      variant="link"
                      className="p-0 text-muted"
                      onClick={() => handleSort(column.key)}
                    >
                      {sortConfig?.key === column.key ? (
                        sortConfig.direction === 'asc' ? (
                          <ChevronUp size={16} />
                        ) : (
                          <ChevronDown size={16} />
                        )
                      ) : (
                        <ChevronDown size={16} className="text-muted" />
                      )}
                    </Button>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {paginatedData.map((row, index) => (
            <tr key={index}>
              {columnsWithSno.map((column) => (
                <td key={String(column.key)}>
                  {column.render
                    ? column.render(column.key === '__sno__' ? undefined : row[column.key], row, index)
                    : column.key === '__sno__'
                      ? snoStart + index
                      : column.filterValue
                        ? String(column.filterValue(row))
                        : row[column.key] !== undefined ? String(row[column.key]) : ''}
                </td>
              ))}
            </tr>
          ))}
          {filteredAndSortedData.length === 0 && (
            <tr>
              <td colSpan={columnsWithSno.length} className="text-center py-4 text-muted">
                No results found
              </td>
            </tr>
          )}
        </tbody>      </Table>

      {totalPages > 0 && !hidePagination && (
        <div className="mt-3">
          <div className="d-flex align-items-center justify-content-between mb-2">
            <div className="d-flex align-items-center gap-2">
              <div className="text-muted">
                {effectiveTotalItems > 0 ? (
                  <>
                    Showing {((currentPage - 1) * effectiveItemsPerPage) + 1} to {Math.min(currentPage * effectiveItemsPerPage, effectiveTotalItems)} of {effectiveTotalItems} results
                  </>
                ) : (
                  <>No results found</>
                )}
              </div>

              <>
                <span className="text-muted mx-2">|</span>
                <span className="text-muted">Items per page:</span>
                <Form.Select
                  size="sm"
                  style={{ width: '70px' }}
                  value={effectiveItemsPerPage}
                  onChange={(e) => {
                    const newSize = parseInt(e.target.value, 10);
                    if (onItemsPerPageChange) {
                      onItemsPerPageChange(newSize);
                    } else {
                      // If no external handler, update internal state
                      // and reset to first page
                      handlePageChange(1);
                      // We need to update the itemsPerPage in the component's state
                      // This is a workaround since we can't directly modify the prop
                      const event = new CustomEvent('itemsPerPageChange', {
                        detail: { newSize }
                      });
                      window.dispatchEvent(event);
                    }
                  }}
                >
                  {[10, 25, 50, 100].includes(effectiveItemsPerPage) ? (
                    <>
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </>
                  ) : (
                    <>
                      <option value={effectiveItemsPerPage}>{effectiveItemsPerPage}</option>
                      <option value="10">10</option>
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                    </>
                  )}
                </Form.Select>
              </>
            </div>
            <div className="d-flex justify-content-center">
            <div>
              {/* << First page */}
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (currentPage !== 1) handlePageChange(1);
                }}
                style={{
                  margin: '0 8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  color: currentPage === 1 ? '#6c757d' : '#007bff',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  pointerEvents: currentPage === 1 ? 'none' : 'auto',
                  userSelect: 'none',
                }}
              >
                {'<<'}
              </a>
              {/* < Previous page */}
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (currentPage > 1) handlePageChange(currentPage - 1);
                }}
                style={{
                  margin: '0 8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  color: currentPage === 1 ? '#6c757d' : '#007bff',
                  cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                  pointerEvents: currentPage === 1 ? 'none' : 'auto',
                  userSelect: 'none',
                }}
              >
                {'<'}
              </a>
              {/* Smart page numbers with ellipsis */}
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <span
                      style={{
                        margin: '0 4px',
                        color: '#6c757d',
                        userSelect: 'none',
                      }}
                    >
                      ...
                    </span>
                  ) : (
                    <a
                      href="#"
                      onClick={e => {
                        e.preventDefault();
                        handlePageChange(page);
                      }}
                      style={{
                        margin: '0 4px',
                        textDecoration: currentPage === page ? 'underline' : 'none',
                        fontWeight: currentPage === page ? 'bold' : 'normal',
                        color: currentPage === page ? '#0d6efd' : '#007bff',
                        cursor: currentPage === page ? 'default' : 'pointer',
                        pointerEvents: currentPage === page ? 'none' : 'auto',
                        userSelect: 'none',
                      }}
                    >
                      {page}
                    </a>
                  )}
                </React.Fragment>
              ))}
              {/* > Next page */}
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (currentPage < totalPages) handlePageChange(currentPage + 1);
                }}
                style={{
                  margin: '0 8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  color: currentPage === totalPages ? '#6c757d' : '#007bff',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  pointerEvents: currentPage === totalPages ? 'none' : 'auto',
                  userSelect: 'none',
                }}
              >
                {'>'}
              </a>
              {/* >> Last page */}
              <a
                href="#"
                onClick={e => {
                  e.preventDefault();
                  if (currentPage !== totalPages) handlePageChange(totalPages);
                }}
                style={{
                  margin: '0 8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  color: currentPage === totalPages ? '#6c757d' : '#007bff',
                  cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                  pointerEvents: currentPage === totalPages ? 'none' : 'auto',
                  userSelect: 'none',
                }}
              >
                {'>>'}
              </a>
            </div>
          </div>
          </div>


        </div>
      )}
    </div>
  );
}
