import React from "react";
import classes from "./FileDetail.module.css";
import <PERSON>Viewer from "react-file-viewer";
import DocumentPreview from "./DocumentPreview";
import {
  downloadeDocument,
  editById,
  findById,
  getList,
  addNew,
  deleteById,
  formatDate,
  uploadDocument,
  formatDateOnly,
  getDisplayPath,
  convertFilePathToInboxFormat,
} from "../../services/apiService";
import { Navigate } from "react-router-dom";
import {
  Modal,
  Button,
  Form,
  Card,
  Row,
  Col,
  Container,
  Breadcrumb,
} from "react-bootstrap";
import ReactDatePicker from "react-datepicker";
import axios from "../../services/api";
import "react-datepicker/dist/react-datepicker.css";
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import Loader from "../loader/Loader";
import {
  <PERSON>s<PERSON><PERSON>,
  BsFileEarmarkArrow<PERSON>p<PERSON>ill,
  BsFileEarmarkLock2Fill,
  BsFileEarmarkTextFill,
  BsGear,
  BsSend,
  BsTrash,
  BsUiRadiosGrid,
  BsPencil,
  BsMap,
} from "react-icons/bs";
import { MdEdit, MdEditDocument, MdEditSquare } from "react-icons/md";
import { DataTable } from "../Table/DataTable";
import CustomBreadcrumb from "../common/CustomBreadcrumb";
import DownloadReport from "../common/DownloadReport";
import * as XLSX from 'xlsx';
import Select from 'react-select';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// SortableItem component
const SortableItem = ({ id, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    marginLeft: "20px",
    marginBottom: "8px",
    padding: "4px",
    background: isDragging ? "lightblue" : "transparent",
    border: isDragging ? "1px dashed #ccc" : "none",
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </div>
  );
};

class FileDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    assignType: "",
    updateType: "",
    navigate: false,
    isPreviewModalOpen: false,
    filePath: "",
    fileType: "",
    linkPath: "",
    previewing: false,
    zoomLevel: 1,
    documentDetails: {
      documentId: "",
      documentNumber: "",
      createdDate: "",
      modifiedDate: "",
      ownerName: "",
      dueDate: "",
      remarks: "",
    },
    isShareModalOpen: false,
    isShareExpirationEnabled: false,
    shareExpirationDate: new Date(),
    shareEmail: "",
    shareUserGroup: "",
    isShareCustomSelected: false,
    isShareInputGiven: false,
    isRetentionModalOpen: false,
    selectedOption: "auto",
    retentionEnd: new Date(),
    action: "",
    isRemModalOpen: false,
    selectedRemDate: new Date(),
    remEmail: "",
    remUserGroup: "",
    message: "",
    isLockModalOpen: false,
    isApproval: false,
    isAcknowledge: false,
    isSwitchOn: false,
    isEsign: false,
    emailFields: [{ id: "" }],
    filePreviewClicked: false,
    isDeleteModalOpen: false,
    isMoveModalOpen: false,
    isDuplicateModalOpen: false,
    folderName: "",
    folderError: "",
    folderList: [],
    desc: "",
    type: "",
    folder: "",
    logList: [],
    filterdLogsList: [],
    userList: [],
    approveworkflowList: [],
    isApprovalStarted: false,
    workflowData: null,
    isAckStarted: false,
    isAckSwitchOn: false,
    ackEmailFields: [{ id: "" }],
    ackDesc: "",
    ackFolder: "",
    ackWorkflowData: null,
    isESignStarted: false,
    isESignSwitchOn: false,
    eSignEmailFields: [{ id: "" }],
    eSignDesc: "",
    eSignType: "",
    eSignFolder: "",
    eSignWorkflowData: null,
    usersGroup: [],
    viewType: "Viewer",
    customPermissions: {
      previewFiles: false,
      downloadFiles: false,
      editAttributes: false,
      uploadFileVersions: false,
      editFilesOnline: false,
      duplicateFiles: false,
      shareFiles: false,
      seeAuditTrails: false,
    },
    remainderList: [],
    retentionList: [],
    shareList: [],
    remainderId: null,
    retentionId: null,
    shareId: null,
    isLocked: false,
    errorMessage: "",
    metaDataList: [],
    tagList: [],
    deployedURL: "",
    versionHistoryList: [],
    deletedVersionHistoryList: [],
    versionPath: "",
    isDeleteVersionModalOpen: false,
    isRestoreVersionModalOpen: false,
    showTooltip: false,
    sortColumn: "Time",
    sortDirection: "asc",
    userFilter: "",
    actionFilter: "",

    isPermDeleteVersionModalOpen: false,
    isDeletedRestoreModalOpen: false,
    activeTab: "share",
    companyList: [],
    selectedCompanyId: null,
    shareUserList: [],
    selectedUserId: null,
    isShareToCompanyExpirationEnabled: false,
    shareToCompanyExpirationDate: new Date(),
    shareToCompanyViewType: "Viewer",
    userError: "",
    approvalWorkFlowId:null,
    acknowledgementWorkFlowId:null,
    eSignWorkFlowId:null,
    showEditIcon:false,
    showEditModal: false,
    editFileType: null,
    editFileUrl: null,
    editFileName: null,
    editContent: null,
    workbook: null,
    sheetName: null, 
    isDocMappingModalOpen:false,
    docListToMap:[],
    docSelectOptions:[],
    mappedDocsExists:false,
    mappedDocList:[],
    approvalUsers:[],
    approvalId:null,
    ackId:null,
    eSignId:null,
    ackworkflowList:[],
    ackUsers:[],
    eSignworkflowList:[],
    eSignUsers:[],
    mappedDoc:null,
    selectedApprovalWorkFlowId:null,
    selectedAcknowledgementWorkFlowId:null,
    selectedESignWorkFlowId:null, 
    selectedApprovalWorkFlowName:"",
    selectedAcknowledgementWorkFlowName:"",
    selectedESignWorkFlowName:"", 
    isViewer:false,
    isEditor:false,
    emailError:"",
    from:"",
    };
  };

  editableFileTypes = ['.txt', '.docx', '.doc', '.xls', '.xlsx', '.pptx', '.ppt'];
  //editableFileTypes = ['.txt', '.docx', '.xlsx', '.pptx'];
  componentDidMount() {
    this.setState({from: sessionStorage.getItem("from")})
    this.fetchUserGroup();
    this.fetchUserList();
    this.fetchDocumentMappingListByFileId();
    this.fetchFileDetails();
    this.fetchReminderJobList();
    this.fetchRetentionJobList();
    this.fetchShareJobList();
    this.fetchFileLogs();
    this.getApprovalWorkFlowById();
    this.getAckWorkFlowById();
    this.getESignWorkFlowById();
    this.showEditIcon();
    const deployedURL = window.location.href.split("/#")[0];
    console.log(deployedURL);
    this.setState({ deployedURL: deployedURL });
  }

  componentWillUnmount() {
    this.setState({
      documentDetails: {
        documentId: "",
        documentNumber: "",
        createdDate: "",
        modifiedDate: "",
        ownerName: "",
        remarks: "",
      },
      navigate: false,
      isViewer:false,
      isEditor:false,
    });
    this.setState({ logList: [], filterdLogsList: [] });
  }
  formatISODate = (isoDateString) => {
    const dateObject = new Date(isoDateString);
    const day = String(dateObject.getDate()).padStart(2, "0"); // Day
    const month = String(dateObject.getMonth() + 1).padStart(2, "0"); // Month (0-based index)
    const year = dateObject.getFullYear(); // Year
    const hours = String(dateObject.getHours()).padStart(2, "0"); // Hours
    const minutes = String(dateObject.getMinutes()).padStart(2, "0"); // Minutes
    const seconds = String(dateObject.getSeconds()).padStart(2, "0"); // Seconds
    return `${day}-${month}-${year} ${hours}:${minutes}:${seconds}`;
  };

  formatISODateOnly = (isoDateString) => {
    const dateObject = new Date(isoDateString);
    const day = String(dateObject.getDate()).padStart(2, "0"); // Day
    const month = String(dateObject.getMonth() + 1).padStart(2, "0"); // Month (0-based index)
    const year = dateObject.getFullYear(); // Year
    return `${day}-${month}-${year}`;
  };

  fetchFileDetails = async () => {
    this.setState({ isLoading: true });
    let api = `/documentsattachmentdetail/${this.props.fileId}`;
    try {
      const data = await findById(api);
      console.log(data.data)
      const createdDate = data.data.createdDate;
      const modifiedDate = data.data.modifiedDate
        ? data.data.modifiedDate
        : data.data.createdDate;
      const dueDate = data.data.dueDate ? data.data.dueDate : null;

      const formattedCreatedDate = this.formatISODate(createdDate);
      const formattedModifiedDate = this.formatISODate(modifiedDate);
      const formattedDueDate = dueDate ? this.formatISODateOnly(dueDate) : "";
      if(this.state.from === "inbox"){
        if(data.data.sharedUsers!=null){
          const isShared=data.data.sharedUsers.find(x => x.id == localStorage.getItem("id"));
          console.log("=====",isShared);
          isShared.permission === "Viewer" ?  this.setState({isViewer:true}) : this.setState({isEditor:true});
        }
      }else {        
        if(data.data.sharedTeams!=null){
          const userId = Number(localStorage.getItem("id"));
          const matchingSharedTeam = data.data.sharedTeams.find(sharedTeam => {
            const fullTeamDetails = this.state.usersGroup.find(team => team.id === sharedTeam.id);
    
              return fullTeamDetails && 
                (fullTeamDetails.teamLead === userId || 
                  (fullTeamDetails.teamMemebers && fullTeamDetails.teamMemebers.includes(userId)));
          });

          console.log("======",matchingSharedTeam)

          if (matchingSharedTeam) {
            console.log("Matching shared team item:", matchingSharedTeam);
            matchingSharedTeam.permission === "Viewer" ? this.setState({isViewer:true}) : this.setState({isEditor:true})
          }
        } 
      }
      

      const otherCompanyId = data.data.sharedCompanies.find(x => x == localStorage.getItem("companyId"));
      console.log("matched shared company id ",otherCompanyId);

      this.setState({
        documentDetails: {
          documentId: data.data.documentsAttachmentId,
          documentNumber: data.data.documentNumber,
          createdDate: formattedCreatedDate,
          modifiedDate: formattedModifiedDate,
          dueDate: formattedDueDate,
          ownerName: data.data.createdBy,
          remarks: data.data.remarks,
        },
        linkPath: data.data.linkPath,
        isLoading: false,
        isLocked: data.data.isLocked,
        metaDataList: data.data.metaDataLink,
        tagList:data.data.metaDataList,
        versionHistoryList: data.data.versionHistory
          ? data.data.versionHistory
              .filter((v) => !v.deleted)
              .sort(
                (a, b) => new Date(b.modifiedDate) - new Date(a.modifiedDate)
              )
          : [],
        deletedVersionHistoryList: data.data.versionHistory
          ? data.data.versionHistory
              .filter((v) => v.deleted)
              .sort(
                (a, b) => new Date(b.modifiedDate) - new Date(a.modifiedDate)
              )
          : [],
      },()=>console.log("first====",this.state.documentDetails));
    } catch (error) {
      console.log(error);
      this.setState({ isLoading: false });
    }
  };
  fetchFileLogs = async () => {
    const api = `/logs-history/list-by/${this.props.fileId}?page=0&size=2000`;
    try {
      const data = await getList(api);
      let logData = data.data.content;
      console.log("000000000", logData);
      this.setState({ logList: logData, filterdLogsList: logData });
    } catch (error) {
      console.log(error);
    }
  };

  openRemModal = () => {
    this.setState({ isRemModalOpen: true });
    this.fetchUserGroup();
    this.fetchUserList();
    console.log("Modal opened");
  };
  openShareModal = () => {
    this.fetchUserGroup();
    this.fetchUserList();
    this.setState({ isShareModalOpen: true });
    console.log("Modal opened");
  };

  openRetentionModal = () => {
    this.setState({ isRetentionModalOpen: true });
    console.log("Modal opened");
  };
  openLockModal = () => {
    this.setState({ isLockModalOpen: true });
  };
  closeRemModal = () => {
    this.setState({
      isRemModalOpen: false,
      remEmail: "",
      message: "",
      remUserGroup: "",
      remainderId: null,
    });
  };
  closeShareModal = () => {
    this.setState({
      isShareModalOpen: false,
      shareEmail: "",
      shareUserGroup: "",
      shareId: null,
      selectedCompanyId: null,
      selectedUserId: null,
      userError: "",
      activeTab:"share"
    });
  };

  closeRetentionModal = () => {
    this.setState({
      isRetentionModalOpen: false,
      retentionId: null,
    });
  };
  closeLockModal = () => {
    this.setState({ isLockModalOpen: false });
  };
  handleRetentionRadioChange = (event) => {
    this.setState({ selectedOption: event.target.value });
  };
  handleRetentionDateChange = (event) => {
    this.setState({ retentionEnd: event.target.value });
  };

  handleRetentionActionChange = (event) => {
    this.setState({ action: event.target.value });
  };

  toggleShareExpiration = () => {
    this.setState((prevState) => ({
      isShareExpirationEnabled: !prevState.isShareExpirationEnabled,
      shareExpirationDate: new Date(),
    }));
  };

  handleShareDateChange = (event) => {
    this.setState({ shareExpirationDate: event.target.value });
  };

  handleShareEmailChange = (event) => {
    this.setState({ shareEmail: event.target.value, shareUserGroup: "" });
  };

  handleShareUserGroupChange = (event) => {
    this.setState({ shareUserGroup: event.target.value, shareEmail: "" });
  };
  selectedShareValue = (event) => {
    const selectedOption = event.target.value;
    this.setState({ viewType: selectedOption });
    console.log("Selected value:", selectedOption);
    if (selectedOption === "Custom") {
      this.setState({ isShareCustomSelected: true });
    } else {
      this.setState({ isShareCustomSelected: false });
    }
  };

  handleRemDateChange = (event) => {
    this.setState({ selectedRemDate: event.target.value });
  };

  handleRemEmailChange = (event) => {
    this.setState({ emailError:"", remEmail: event.target.value });
  };

  handleRemGroupChange = (event) => {
    this.setState({ remUserGroup: event.target.value });
  };

  handleRemMessageChange = (event) => {
    this.setState({ message: event.target.value });
  };

  fetchUserGroup = async () => {
    let api = `/team/team_dropdown_list`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        usersGroup: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  fetchReminderJobList = async () => {
    const { itemsPerPage } = this.state;
    const api = `/job-scheduling/list-by/reminder/${this.props.fileId}`;
    try {
      const response = await getList(api);
      const data = response.data;
      console.log("Fetched remainder job data", data);
      this.setState({
        remainderList: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  fetchRetentionJobList = async () => {
    const { itemsPerPage } = this.state;
    const api = `/job-scheduling/list-by/retention/${this.props.fileId}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        retentionList: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  fetchShareJobList = async () => {
    const api = `/job-scheduling/list-by/sharedto/${this.props.fileId}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        shareList: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  handleRemind = async () => {
    const { selectedRemDate, remEmail, remUserGroup, message } = this.state;
    const remindData = {
      jobDate: selectedRemDate,
      documentId: this.props.fileId,
      email: remEmail,
      teamId: remUserGroup,
      messageStatus: message,
      jobType: "reminder",
    };

    if (remEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(remEmail)) {
      this.setState({ emailError: "Invalid email address" });
      return;
    }

    if(this.state.remUserGroup === "" && this.state.remEmail === "" ){
      this.setState({
        notification: {
          message: "User/Team required",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      return;
    }

    try {
      if (this.state.remainderId) {
        await this.editJob(this.state.remainderId, remindData);
      } else {
        await this.createJob(remindData);
      }
      this.closeRemModal();
      await this.fetchFileLogs();
      await this.fetchReminderJobList();
    } catch (error) {
      console.error("Error handling remind action:", error);
    }
  };

  editJob = async (id, jobDetails) => {
    this.setState({ isLoading: true });
    const api = `/job-scheduling/${id}`;
    try {
      const response = await editById(api, jobDetails);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
        isLoading: false,
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      throw error;
    }
  };
  createJob = async (jobDetails) => {
    this.setState({ isLoading: true });
    const api = "/job-scheduling";
    try {
      const response = await addNew(api, jobDetails);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
        isLoading: false,
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      throw error;
    }
  };

  confirmDeleteJob = async (id) => {
    this.setState({ isLoading: true });
    const api = `/job-scheduling/${id}`;
    try {
      const resp = await deleteById(api);

      this.setState({
        notification: {
          message: resp.message,
          type: "success",
          show: true,
        },
        isLoading: false,
      });
      this.fetchReminderJobList();
      this.fetchRetentionJobList();
      this.fetchShareJobList();
    } catch (error) {
      console.error(
        "Error deleting:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    } finally {
    }
  };

  handleRetentionSave = async () => {
    const { selectedOption, retentionEnd, action } = this.state;
    const retentionData = {
      retentionType: selectedOption,
      documentId: this.props.fileId,
      jobDate: retentionEnd,
      messageStatus: action,
      jobType: "retention",
    };

    try {
      if (this.state.retentionId) {
        await this.editJob(this.state.retentionId, retentionData);
      } else {
        await this.createJob(retentionData);
      }
      this.closeRetentionModal();
      await this.fetchFileLogs();
      await this.fetchRetentionJobList();
    } catch (error) {
      console.error("Error handling remind action:", error);
    }
  };

  handleCustomPermissionChange = (event) => {
    const { name, checked } = event.target;
    this.setState((prevState) => ({
      customPermissions: {
        ...prevState.customPermissions,
        [name]: checked,
      },
    }));
  };

  handleShareTo = async () => {
    const {
      shareEmail,
      shareUserGroup,
      viewType,
      isShareCustomSelected,
      shareExpirationDate,
      customPermissions,
      shareList,
      isShareExpirationEnabled
    } = this.state;

    const shareToData = {
      email: shareEmail || null,
      teamId: shareUserGroup || null,
      documentId: this.props.fileId,
      messageStatus: viewType,
      jobDate: isShareExpirationEnabled ? shareExpirationDate : null,
      jobType: "sharedto",
    };

    if (!shareEmail && !shareUserGroup) {
      this.setState({
        notification: {
          message: "User/Team is required",
          type: "error",
          show: true,
        },
      });
      return;
    }

    if (
      shareList.some((item) => shareEmail !== "" && item.email === shareEmail)
    ) {
      this.setState({
        notification: {
          message: "Already shared to this User.",
          type: "error",
          show: true,
        },
      });
      return;
    }
    if (
      shareList.some((item) => 
        shareUserGroup && 
        String(item.teamId) === String(shareUserGroup))
    ) {
      this.setState({
        notification: {
          message: "Already shared to this team.",
          type: "error",
          show: true,
        },
      });
      return;
    }

    try {
      if (this.state.shareId) {
        await this.editJob(this.state.shareId, shareToData);
      } else {
        await this.createJob(shareToData);
      }
      this.closeShareModal();
      await this.fetchFileLogs();
      await this.fetchShareJobList();
    } catch (error) {
      console.error("Error handling remind action:", error);
    }
  };

  onSwitchAction = () => {
    const doesShow = this.state.isSwitchOn;
    this.setState({ isSwitchOn: !doesShow });
  };

  onAckSwitchAction = () => {
    const doesShow = this.state.isAckSwitchOn;
    this.setState({ isAckSwitchOn: !doesShow });
  };

  onESignSwitchAction = () => {
    const doesShow = this.state.isESignSwitchOn;
    this.setState({ isESignSwitchOn: !doesShow });
  };

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=300&search=&sort=";
    try {
      const data = await getList(api);
      const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      this.setState({ userList: filteredList });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  fetchApproveworkflowList = async () => {
    const compId=localStorage.getItem("companyId");
    const api = `/approvalworkflow/approvalworkflow_list/${compId}/?search=&page=0&size=25`;
    try {
      const data = await getList(api);
      const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      this.setState({ approveworkflowList: data.data.content });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  openApprovalModal = () => {
    this.fetchApproveworkflowList();
    this.fetchUserList();
    this.setState({ isApproval: true });
  };

  closeApprovalModal = () => {
    this.setState({ isApproval: false, updateType: "", assignType: "" });
  };

  fetchAcknowledgementWorkflowList = async () => {
    const compId=localStorage.getItem("companyId");
    const api = `/approvalworkflow/acknowledgementworkflow_list/${compId}/?search=&page=0&size=25`;
    try {
      const data = await getList(api);
      const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      this.setState({ ackworkflowList: data.data.content });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  fetchEsignWorkflowList = async () => {
    const compId=localStorage.getItem("companyId");
    const api = `/approvalworkflow/esignworkflow_list/${compId}/?search=&page=0&size=25`;
    try {
      const data = await getList(api);
      const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      this.setState({ eSignworkflowList: data.data.content });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  openAckModal = () => {
    this.fetchAcknowledgementWorkflowList();
    this.fetchUserList();
    this.setState({ isAcknowledge: true });
  };

  closeAckModal = () => {
    this.setState({ isAcknowledge: false, updateType: "", assignType: "" });
  };

  openEsignModal = () => {
    this.fetchEsignWorkflowList();
    this.fetchUserList();
    this.setState({ isEsign: true });
  };

  closeEsignModal = () => {
    this.setState({ isEsign: false, updateType: "", assignType: "" });
  };

  handleInputChange = (index, event) => {
    const newEmailFields = [...this.state.emailFields];
    newEmailFields[index].email = event.target.value;
    this.setState({ emailFields: newEmailFields });
  };

  addEmailField = () => {
    this.setState((prevState) => ({
      emailFields: [...prevState.emailFields, { email: "" }],
    }));
  };

  addAckEmailField = () => {
    this.setState((prevState) => ({
      ackEmailFields: [...prevState.ackEmailFields, { email: "" }],
    }));
  };

  addESignEmailField = () => {
    this.setState((prevState) => ({
      eSignEmailFields: [...prevState.eSignEmailFields, { email: "" }],
    }));
  };

  deleteEmailField = (index) => {
    const newEmailFields = this.state.emailFields.filter((_, i) => i !== index);
    this.setState({ emailFields: newEmailFields });
  };

  deleteAckEmailField = (index) => {
    const newAckEmailFields = this.state.ackEmailFields.filter(
      (_, i) => i !== index
    );
    this.setState({ ackEmailFields: newAckEmailFields });
  };

  deleteESignEmailField = (index) => {
    const newESignEmailFields = this.state.eSignEmailFields.filter(
      (_, i) => i !== index
    );
    this.setState({ eSignEmailFields: newESignEmailFields });
  };

  fileInputRef = React.createRef();

  handleUploadClick = () => {
    this.fileInputRef.current.click();
  };

  handleFileChange = (event) => {
    const file = event.target.files[0];
    console.log(file);
    console.log("Selected file:", file);
    console.log("Document ID:", this.state.documentDetails.documentId);

    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append(
        "documentsAttachmentId",
        this.props.fileId
      );
      console.log(formData);
      for (let pair of formData.entries()) {
        console.log(pair[0] + ": " + pair[1]);
      }
      this.newVersionDocument(formData);
    }
  };

  newVersionDocument = async (docData) => {
    const api = "/documentsattachmentdetail/uploadNewVersionDocument";
    try {
      const response = await uploadDocument(api, docData);
      this.setState(
        {
          notification: {
            message: `New version of ${this.props.fileName} uploaded successfully`,
            type: "success",
            show: true,
          },
        },
        () => {
          this.fetchFileDetails();
          this.fetchFileLogs();
        }
      );
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        //navigate: true,
      });
      //throw error;
    }
  };

  downloadeDocumentAttachment = async (filePath, fileName) => {
    try {
      const data = await downloadeDocument(filePath);
      let blob = new Blob([data], { type: "application/octet-stream" });
      let url = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = fileName;
      anchor.target = "_blank";
      anchor.click();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  closePreviewModal = () => {
    this.setState({ isPreviewModalOpen: false });
  };

  zoomIn = () => {
    this.setState((prevState) => ({
      zoomLevel: prevState.zoomLevel + 0.1,
    }));
  };

  zoomOut = () => {
    this.setState((prevState) => ({
      zoomLevel: Math.max(prevState.zoomLevel - 0.1, 0.1),
    }));
  };

  previewDocument = async (filePath, fileName) => {
    this.setState({ isPreviewModalOpen: true });
    try {
      const data = await downloadeDocument(filePath);
      const blob = new Blob([data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      let fileType = fileName.split(".").pop().toLowerCase();
      if (fileType === "doc") {
        fileType = "docx";
      } else if (fileType === "jpg") {
        fileType = "jpeg";
      } else if (fileType === "xls") {
        fileType = "xlsx";
      } else {
        fileType = fileType;
      }
      this.setState({
        filePath: url,
        fileType: fileType,
        previewing: true,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Could not preview the document",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleError = (e) => {
    console.error("Error rendering file:", e);
  };

  fetchApprovalLevels=async (id) =>{
    const api = `/approvalworkflow/workflow-id/approve/${id}`;
    try {
      const response = await findById(api);
      this.setState({approvalUsers:response.data.approvalLevelDTO})
      console.log(response.data);
    } catch (error) {
    }
  }

  handleApprovalInputChange = (event, index = null) => {
    const { name, value } = event.target;
    this.setState({ errorMessage: "" });
    if (name === "selectedApprovalWorkFlowId") {
      this.fetchApprovalLevels(value);  // Fetch levels when workflow changes
      this.setState({ approvalId: value, selectedApprovalWorkFlowId: value });
    } else {
      this.setState({ [name]: value });
    }
    // if (name.startsWith("userId")) {
    //   this.fetchApprovalLevels(value);
    //   this.setState({approvalId:value});
    //   this.setState((prevState) => {
    //     const emailFields = [...prevState.emailFields];
    //     emailFields[index].id = value;
    //     return { emailFields };
    //   });
    // } else {
    //   this.setState({ [name]: value });
    // }
  };

  handleApprovalSubmit = async (event) => {
    event.preventDefault();

    const { desc, type, folder, isSwitchOn, emailFields } = this.state;

    const isValid = emailFields.every(
      (field) => field.id !== "" && field.id !== null
    );

    // if (!isValid) {
    //   this.setState({ errorMessage: "Please select a user." });
    //   return;
    // } else {
    //   this.setState({ errorMessage: "" });
    // }

    const approvalLevelDTO = emailFields.map((field, index) => ({
      name: `level${index + 1}`,
      usersId: field.id,
    }));

    try {
      if (!this.state.isApprovalStarted) {
        const data = {
          type: type,
          workflow_type: "approve",
          userId: this.props.fileId,
          folder: isSwitchOn ? folder : null,
          description:desc
          //approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ updateType: "Approval" });
        await this.updateWorkFlow(data,this.state.approvalId);
      } 
      //for update
      else {
        const data = {
          id:this.state.approvalWorkFlowId,
          // name: desc,
          type: type,
          workflow_type: "approve",
          userId: this.props.fileId,
          folder: isSwitchOn ? folder : null,
          description:desc,
          // approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ assignType: "Approval" });
        const result = await this.assignWorkflow(data,this.state.approvalId);

        await this.fetchFileLogs();
        //await this.assignWorkflow(data);
        //this.setState({ isApprovalStarted: true });
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.closeApprovalModal();
    }
  };
  
  //doc's workflow update call
  assignWorkflow = async (data,workflowid) => {
    this.setState({ isLoading: true });
    const api = `/approvalworkflow/update-assign/${workflowid}`;
    try {
      const response = await editById(api, data);
      this.setState({
        notification: {
          message: `${this.state.assignType} Workflow ${this.props.fileName} Updated successfully`,
          type: "success",
          show: true,
        },
        isLoading: false,
      });
      await this.getApprovalWorkFlowById();
      await this.getAckWorkFlowById();
      await this.getESignWorkFlowById();
      await this.fetchFileLogs();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  //doc's workflow assign call
  updateWorkFlow = async (data,workflowid) => {
    this.setState({ isLoading: true });
    const api = `/approvalworkflow/create-assign/${workflowid}`;
    try {
      const response = await editById(api, data);
      this.setState({
        notification: {
          message: `${this.state.updateType} Workflow ${this.props.fileName} started successfully`,
          type: "success",
          show: true,
        },
        isLoading: false,
        isApprovalStarted: true,
      });
      await this.getApprovalWorkFlowById();
      await this.fetchFileLogs();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
    this.getApprovalWorkFlowById();
    this.getAckWorkFlowById();
    this.getESignWorkFlowById();
  };

  getApprovalWorkFlowById = async () => {
    const api = `/approvalworkflow/doc1/approve/${this.props.fileId}`;
    try {
      const response = await findById(api);
      console.log(response.data);
      if (response.data.id) {
        this.setState({
          isApprovalStarted: true,
          approvalWorkFlowId:response.data.id,
          selectedApprovalWorkFlowId: response.data.id, 
          selectedApprovalWorkFlowName:response.data.name,
          approvalId: response.data.id,
        });
        await this.fetchApprovalLevels(response.data.id); 
      }
      this.setState({
        workflowData: response.data,
        desc: response.data.description,
        type: response.data.type,
        folder: response.data.folder,
        emailFields: response.data.approvalLevelDTO.map((level) => ({
          id: response.data.id,
        })),
      });
    } catch (error) {
      this.setState({});
    }
  };

  getAckWorkFlowById = async () => {
    const api = `/approvalworkflow/doc1/acknowledgement/${this.props.fileId}`;
    try {
      const response = await findById(api);
      console.log(response.data);
      if (response.data.id) {
        this.setState({
          isAckStarted: true,
          acknowledgementWorkFlowId:response.data.id,
          selectedAcknowledgementWorkFlowId: response.data.id,
          ackId:response.data.id, 
          selectedAcknowledgementWorkFlowName:response.data.name,
        });
        await this.fetchAckLevels(response.data.id);
      }
      this.setState({
        ackWorkflowData: response.data,
        ackDesc: response.data.description,
        ackFolder: response.data.folder,
        ackEmailFields: response.data.approvalLevelDTO.map((level) => ({
          id: response.data.id,
        })),
      });
    } catch (error) {
      this.setState({});
    }
  };

  getESignWorkFlowById = async () => {
    const api = `/approvalworkflow/doc1/eSign/${this.props.fileId}`;
    try {
      const response = await findById(api);
      console.log(response.data);
      if (response.data.id) {
        this.setState({
          isESignStarted: true,
          eSignWorkFlowId:response.data.id,
          selectedESignWorkFlowId: response.data.id, 
          eSignId: response.data.id,
          selectedESignWorkFlowName:response.data.name,
        });
        await this.fetchEsignLevels(response.data.id);
      }
      this.setState({
        eSignWorkflowData: response.data,
        eSignDesc: response.data.description,
        eSignFolder: response.data.folder,
        eSignType: response.data.type,
        eSignEmailFields: response.data.approvalLevelDTO.map((level) => ({
          id: response.data.id,
        })),
      });
    } catch (error) {
      this.setState({});
    }
  };

  handleFilterChange = (e) => {
    const { name, value } = e.target;
    this.setState({ [name]: value }, this.applyFilters);
  };

  applyFilters = () => {
    const { logList, userFilter, actionFilter } = this.state;
    const filtered = logList.filter(
      (log) =>
        log?.createdBy.toLowerCase().includes(userFilter?.toLowerCase()) &&
        log?.logMessage.toLowerCase().includes(actionFilter?.toLowerCase())
    );
    this.setState({ filterdLogsList: filtered });
  };

  exportToPDF = async (event) => {
    event.preventDefault();

    const api = `${GlobalConstants.globalURL}/documentreports/docLogs/exportPDF/${this.props.fileId}`;

    try {
      const response = await axios.get(api, {
        responseType: "blob",
        headers: {
          Accept: "application/pdf",
        },
      });
      console.log(response);
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `documentLog-${this.props.fileId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
    } catch (error) {
      console.error("Error downloading PDF:", error);
    }
  };

  exportToExcel = async (event) => {
    event.preventDefault();

    const api = `${GlobalConstants.globalURL}/documentreports/docLogs/exportExcel/${this.props.fileId}`;
    console.log(api);

    try {
      const response = await axios.get(api, {
        responseType: "blob",
        headers: {
          Accept:
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        },
      });

      if (response.data.size === 0) {
        throw new Error("Received an empty response from the server.");
      }

      const contentType =
        response.headers["content-type"] ||
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      const blob = new Blob([response.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `documentLog-${this.props.fileId}.xls`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
    } catch (error) {
      console.error("Error downloading Excel:", error.message);
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openDeleteModal = () => {
    this.setState({ isDeleteModalOpen: true });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false });
  };

  openMoveModal = () => {
    this.setState({ isMoveModalOpen: true });
    this.fetchDocumentList();
  };
  openDuplicateModal = () => {
    this.setState({ isDuplicateModalOpen: true });
    this.fetchDocumentList();
  };
  closeMoveModal = () => {
    this.setState({ isMoveModalOpen: false });
  };
  closeDuplicateModal = () => {
    this.setState({ isDuplicateModalOpen: false });
  };

  fetchDocumentList = async () => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee/" +
      userId +
      `?page=0&size=500&searchParam=`;

    try {
      const response = await getList(api);
      const data = response.data;
      const filePathList = data.content
        .filter((document) => !document.linkPath)
        .map((document) => document.filePath);
      const fullPaths = filePathList
        .map((filePath) => {
          const empIdPrefixMatch = filePath.match(/Emp_Id_\d+\//);

          if (empIdPrefixMatch) {
            const pathAfterEmpId = filePath.split(empIdPrefixMatch[0])[1];
            if (pathAfterEmpId) {
              const pathParts = pathAfterEmpId.split("/");
              const validParts = pathParts.filter(
                (part) => !part.includes(".")
              );
              return validParts.join("/");
            }
          }
          return "";
        })
        .filter(Boolean);

      const uniquePaths = [...new Set(fullPaths)];

      const folderListWithRoot = ["/", ...uniquePaths];
      
      console.log(folderListWithRoot);
      this.setState({ folderList: folderListWithRoot });

    } catch (error) {}
  };

  handleAckInputChange = (event, index = null) => {
    const { name, value } = event.target;
    this.setState({ errorMessage: "" });

    if (name === "selectedAcknowledgementWorkFlowId") {
      this.fetchAckLevels(value);  // Fetch levels when workflow changes
      this.setState({ ackId: value, selectedAcknowledgementWorkFlowId: value });
    } else {
      this.setState({ [name]: value });
    }

    // if (name.startsWith("userId")) {
    //   this.fetchAckLevels(value);
    //   this.setState({ackId:value});
    //   this.setState((prevAckState) => {
    //     const ackEmailFields = [...prevAckState.ackEmailFields];
    //     ackEmailFields[index].id = value;
    //     return { ackEmailFields };
    //   });
    // } else {
    //   this.setState({ [name]: value });
    // }
  };

  fetchAckLevels=async (id) =>{
    const api = `/approvalworkflow/workflow-id/acknowledgement/${id}`;
    try {
      const response = await findById(api);
      this.setState({ackUsers:response.data.approvalLevelDTO})
      console.log(response.data);
    } catch (error) {
    }
  }

  fetchEsignLevels=async (id) =>{
    const api = `/approvalworkflow/workflow-id/eSign/${id}`;
    try {
      const response = await findById(api);
      this.setState({eSignUsers:response.data.approvalLevelDTO})
      console.log(response.data);
    } catch (error) {
    }
  }

  handleESignInputChange = (event, index = null) => {
    const { name, value } = event.target;
    this.setState({ errorMessage: "" });

    if (name === "selectedESignWorkFlowId") {
      this.fetchEsignLevels(value);  // Fetch levels when workflow changes
      this.setState({ eSignId: value, selectedESignWorkFlowId: value });
    } else {
      this.setState({ [name]: value });
    }

    // if (name.startsWith("userId")) {
    //   this.fetchEsignLevels(value);
    //   this.setState({eSignId:value});
    //   this.setState((prevESignState) => {
    //     const eSignEmailFields = [...prevESignState.eSignEmailFields];
    //     eSignEmailFields[index].id = value;
    //     return { eSignEmailFields };
    //   });
    // } else {
    //   this.setState({ [name]: value });
    // }
  };

  handleAckSubmit = async (event) => {
    event.preventDefault();
    const { ackDesc, ackFolder, isAckSwitchOn, ackEmailFields } = this.state;

    const isValid = ackEmailFields.every(
      (field) => field.id !== "" && field.id !== null
    );

    // if (!isValid) {
    //   this.setState({ errorMessage: "Please select a user." });
    //   return;
    // } else {
    //   this.setState({ errorMessage: "" });
    // }

    const approvalLevelDTO = ackEmailFields.map((field, index) => ({
      name: `level${index + 1}`,
      usersId: field.id,
    }));
    
    try {
      if (!this.state.isAckStarted) {
        const data = {
          // name: ackDesc,
          type: "",
          workflow_type: "acknowledgement",
          userId: this.props.fileId,
          folder: isAckSwitchOn ? ackFolder : null,
          description:ackDesc
          // approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ updateType: "acknowledgement" });
        await this.updateWorkFlow(data,this.state.ackId);
      }
      //for update
       else {
        const data = {
          id:this.state.acknowledgementWorkFlowId,
          // name: ackDesc,
          type: "",
          workflow_type: "acknowledgement",
          userId: this.props.fileId,
          folder: isAckSwitchOn ? ackFolder : null,
          description:ackDesc,
          // approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ assignType: "acknowledgement" });
        const result = await this.assignWorkflow(data,this.state.ackId);
        console.log(result);
        await this.fetchFileLogs();
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.closeAckModal();
    }
  };

  handleESignSubmit = async (event) => {
    event.preventDefault();
    const {
      eSignDesc,
      eSignType,
      eSignFolder,
      isESignSwitchOn,
      eSignEmailFields,
    } = this.state;

    const isValid = eSignEmailFields.every(
      (field) => field.id !== "" && field.id !== null
    );

    // if (!isValid) {
    //   this.setState({ errorMessage: "Please select a user." });
    //   return;
    // } else {
    //   this.setState({ errorMessage: "" });
    // }
    const approvalLevelDTO = eSignEmailFields.map((field, index) => ({
      name: `level${index + 1}`,
      usersId: field.id,
    }));

    try {
      if (!this.state.isESignStarted) {
        const data = {
          type: eSignType,
          workflow_type: "eSign",
          userId: this.props.fileId,
          folder: isESignSwitchOn ? eSignFolder : null,
          description:eSignDesc
          // approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ updateType: "eSign" });
        await this.updateWorkFlow(data,this.state.eSignId);
      } 
      //for update
      else {
        const data = {
          id:this.state.eSignWorkFlowId,
          // name: eSignDesc,
          type: eSignType,
          workflow_type: "eSign",
          userId: this.props.fileId,
          folder: isESignSwitchOn ? eSignFolder : null,
          description:eSignDesc,
          // approvalLevelDTO: approvalLevelDTO,
        };
        this.setState({ assignType: "eSign" });
        const result = await this.assignWorkflow(data,this.state.eSignId);
        await this.fetchFileLogs();
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.closeEsignModal();
    }
  };

  deleteFile = async () => {
    this.setState({ isLoading: true });
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/${this.props.fileId}`;
    try {
      const response = axios.delete(api);
      this.setState(
        {
          notification: {
            message: "File Deleted Successfully",
            type: "success",
            show: true,
          },
          isLoading: false,
        },
        () => {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      );
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
    this.closeDeleteModal();
  };

  permanentDeleteFile = async () => {
    this.setState({ isLoading: true });
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/perminentdelete/${this.props.fileId}`;
    try {
      const response = axios.delete(api);
        this.setState({
          notification: {
            message: "File Deleted Successfully",
            type: "success",
            show:true,
          },
          isLoading:false,
        });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show:true,
        },
        isLoading:false,
      });
    }
    this.closeDeleteModal();
  };

  moveFile = async () => {
    if (!this.state.folderName) {
      this.setState({ folderError: "Select a folder" });
      return;
    }
    let moveData = {
      documentsAttachmentId: this.props.fileId,
      filePath: this.state.folderName,
    };
    const api = "/documentsattachmentdetail/moveDocument";
    try {
      const response = await uploadDocument(api, moveData);
      this.setState({
        notification: {
          message: `File ${this.props.fileName} moved successfully`,
          type: "success",
          show: true,
        },
      });
      this.closeMoveModal();
      await this.fetchFileLogs();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
       // navigate: true,
      });
      this.closeMoveModal();
      //throw error;
    }
  };

  duplicateFile = async () => {
    if (!this.state.folderName) {
      this.setState({ folderError: "Select a folder" });
      return;
    }
    let moveData = {
      documentsAttachmentId: this.props.fileId,
      filePath: this.state.folderName,
    };
    const api = "/documentsattachmentdetail/duplicateDocument";
    try {
      const response = await uploadDocument(api, moveData);
      this.setState({
        notification: {
          message: `File ${this.props.fileName} duplicated successfully`,
          type: "success",
          show: true,
        },
      });
      this.closeDuplicateModal();
      await this.fetchFileLogs();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        //navigate: true,
      });
      this.closeDuplicateModal();
      //throw error;
    }
  };

  handleFolderName = (event) => {
    this.setState({ folderError: "", folderName: event.target.value });
  };

  handleLock = async () => {
    console.log("File lock/unlock initiated for file ID: ", this.props.fileId);
    const { isLocked } = this.state;
    this.setState({ isLoading: true });
    const api = isLocked
      ? `${GlobalConstants.globalURL}/documentsattachmentdetail/unlock/${this.props.fileId}`
      : `${GlobalConstants.globalURL}/documentsattachmentdetail/lock/${this.props.fileId}`;

    try {
      await axios.delete(api);
      this.setState({
        isLocked: !isLocked,
        notification: {
          message: `File ${isLocked ? "Unlocked" : "Locked"} Successfully`,
          type: "success",
          show: true,
        },
        isLoading: false,
      });

      this.fetchFileDetails();
      this.fetchFileLogs();
    } catch (error) {
      console.error("Error locking/unlocking file:", error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    } finally {
      this.closeLockModal();
    }
  };

  fetchJobById = async (id) => {
    const api = `/job-scheduling/${id}`;
    try {
      const response = await findById(api);
      console.log("Id Data : ", response.data);
      return response.data;
    } catch (error) {
      throw error;
    }
  };

  handleRemainderEdit = async (rem) => {
    console.log(rem);
    this.setState({ remainderId: rem.id });
    try {
      const jobData = await this.fetchJobById(rem.id);
      console.log("Job Data in handleRemainderEdit: ", jobData);
      const selectedRemDate = new Date(jobData.jobDate)
        .toISOString()
        .slice(0, 16);
      this.setState({
        selectedRemDate: jobData.jobDate,
        remEmail: jobData.email,
        message: jobData.messageStatus,
        remUserGroup: jobData.teamId,
      });
      this.openRemModal();
    } catch (error) {
      console.error("Error fetching job data:", error);
    }
  };

  handleShareEdit = async (rem) => {
    this.setState({ shareId: rem.id });

    try {
      const jobData = await this.fetchJobById(rem.id);
      console.log("Job Data in handleShareEdit: ", jobData);
      
      //fetching-data-if-shared-to-other-company-user
      if(jobData.otherUserCompanyId){
        this.handleCompanySharingTab();
        this.setState({
          selectedCompanyId:jobData.otherUserCompanyId,
        })
        const users=await this.fetchUserListByCompanyId(jobData.otherUserCompanyId);
        const matchedUser = users.find(user=>user.email === jobData.email);
        this.setState({
          selectedUserId: matchedUser.id,
          shareToCompanyViewType: jobData.messageStatus,
        });

        if (jobData.jobDate) {
          const expirationDate = new Date(jobData.jobDate);
  
          if (!isNaN(expirationDate.getTime())) {
            this.setState({ isShareToCompanyExpirationEnabled: true });
            this.setState({ isShareToCompanyExpirationEnabled: expirationDate });
          } else {
            console.error("Invalid date in jobData:", jobData.jobDate);
          }
        }
      }
      //fetching-data-if-shared-to-own-company-user
      else{
        if (jobData.jobDate) {
          const expirationDate = new Date(jobData.jobDate);
  
          if (!isNaN(expirationDate.getTime())) {
            this.setState({ isShareExpirationEnabled: true });
            this.setState({ shareExpirationDate: expirationDate });
          } else {
            console.error("Invalid date in jobData:", jobData.jobDate);
          }
        }
  
        this.setState({
          shareEmail: jobData.email,
          shareUserGroup: jobData.teamId,
          viewType: jobData.messageStatus,
        });
      }

      this.openShareModal();
    } catch (error) {
      console.error("Error fetching job data:", error);
    }
  };

  handleRetentionEdit = async (rem) => {
    console.log(rem);
    this.setState({ retentionId: rem.id });

    try {
      const jobData = await this.fetchJobById(rem.id);
      console.log("Job Data in handleRetentionEdit: ", jobData);

      if (jobData.jobDate) {
        const retentionEndDate = new Date(jobData.jobDate);

        if (!isNaN(retentionEndDate.getTime())) {
          this.setState({
            selectedOption: "active",
            retentionEnd: retentionEndDate,
            action: jobData.messageStatus,
          });
        } else {
          console.error("Invalid jobDate in jobData:", jobData.jobDate);
        }
      }

      this.openRetentionModal();
    } catch (error) {
      console.error("Error fetching job data:", error);
    }
  };

  handleVersionDownload = (path) => {
    console.log("FilePath:", path);
    const fileName = path.filePath.split(/Emp_Id_\d+\//)[1]
    this.downloadeDocumentAttachment(path.filePath, fileName);
  };

  deleteVersionModal = (path) => {
    this.setState({
      isDeleteVersionModalOpen: true,
      versionPath: path.filePath,
    });
  };

  closeDeleteVersionModal = () => {
    this.setState({ isDeleteVersionModalOpen: false, versionPath: "" });
  };

  deleteVersion = async () => {
    this.setState({ isLoading: true });
    const { versionPath } = this.state;
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/versiondelete/${this.props.fileId}?versionPath=${versionPath}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Version Deleted Successfully",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }

    await this.fetchFileDetails();
    await this.fetchFileLogs();
    this.closeDeleteVersionModal();
  };

  restoreVersionModal = (path) => {
    this.setState({
      isRestoreVersionModalOpen: true,
      versionPath: path.filePath,
    });
  };

  closeRestoreVersionModal = () => {
    this.setState({ isRestoreVersionModalOpen: false, versionPath: "" });
  };

  restoreVersion = async (type) => {
    const { versionPath } = this.state;
    this.setState({isLoading:true});
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/versionrestore/${this.props.fileId}?versionPath=${versionPath}&type=${type}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Version Restored Successfully",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }
    await this.fetchFileDetails();
    await this.fetchFileLogs();
    this.closeRestoreVersionModal();
  };

  handleSort = (column) => {
    const { sortColumn, sortDirection, filteredList } = this.state;

    // Map columns to their actual object keys
    const columnKeyMap = {
      Time: "createdDate",
      User: "createdBy",
      Action: "logMessage",
    };

    const key = columnKeyMap[column];
    let newDirection = "asc";

    if (sortColumn === column && sortDirection === "asc") {
      newDirection = "desc";
    }

    const sortedList = [...filteredList].sort((a, b) => {
      let aValue = column === "Time" ? new Date(a[key]) : a[key];
      let bValue = column === "Time" ? new Date(b[key]) : b[key];

      if (aValue < bValue) return newDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return newDirection === "asc" ? 1 : -1;
      return 0;
    });

    this.setState({
      logList: sortedList,
      sortColumn: column,
      sortDirection: newDirection,
    });
  };

  permanentDeleteVersionModal = (path) => {
    this.setState({
      isPermDeleteVersionModalOpen: true,
      versionPath: path.filePath,
    });
  };

  closePermanentDeleteVersionModal = () => {
    this.setState({ isPermDeleteVersionModalOpen: false, versionPath: "" });
  };

  permanentDeleteVersion = async () => {
    this.setState({isLoading:true});
    const { versionPath } = this.state;
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/version-delete-permanent/${this.props.fileId}?versionPath=${versionPath}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Version Deleted Permanently",
          type: "success",
          show: true,
        },
        isLoading:false
      });
      await this.fetchFileDetails();
      await this.fetchFileLogs();
      this.closePermanentDeleteVersionModal();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }
  };

  deletedRestoreVersionModal = (path) => {
    this.setState({
      isDeletedRestoreModalOpen: true,
      versionPath: path.filePath,
    });
  };

  closeDeletedRestoreVersionModal = () => {
    this.setState({ isDeletedRestoreModalOpen: false, versionPath: "" });
  };

  deletedRestoreVersion = async () => {
    const { versionPath } = this.state;
    this.setState({isLoading:true});
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/deleted-version-restore/${this.props.fileId}?versionPath=${versionPath}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Version Restored Successfully",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
      await this.fetchFileDetails();
      await this.fetchFileLogs();
      this.closeDeletedRestoreVersionModal();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }
  };

  getTeamName = (id) => {
    const matchedTeam = this.state.usersGroup.find((team) => team.id === id);
    return matchedTeam ? matchedTeam.name : null;
  };

  handleCompanySharingTab = () => {
    this.setState({ activeTab: "company" });
    this.fetchCompanyList();
  };

  fetchCompanyList = async () => {
    const api = `/company/list?page=0&size=1000&searchParam=`;
    const companyId = localStorage.getItem("companyId");
    try {
      const response = await getList(api);
      const data = response.data;
      const filteredComList = data.content.filter(
        (com) => com.companyId != companyId
      );
      console.log(filteredComList);
      this.setState({
        companyList: filteredComList,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleSelectedCompany = (e) => {
    const selectedCompanyId = e.target.value;
    this.setState({ selectedCompanyId: selectedCompanyId });
    this.fetchUserListByCompanyId(e.target.value);
  };

  fetchUserListByCompanyId = async (id) => {
    const api = `/user/get-user-list-by-company-id/${id}`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        shareUserList: data,
        isLoading: false,
      });
      return data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handleSelectedUser = (e) => {
    const selectedUserId = e.target.value;
    this.setState({ selectedUserId: selectedUserId, userError: "" });
  };

  toggleShareToCompanyExpiration = () => {
    this.setState((prevState) => ({
      isShareToCompanyExpirationEnabled:
        !prevState.isShareToCompanyExpirationEnabled,
      shareToCompanyExpirationDate: new Date(),
    }));
  };

  selectedShareToCompanyViewType = (event) => {
    const selectedOption = event.target.value;
    this.setState({ shareToCompanyViewType: selectedOption });
  };

  handleShareFileToOtherCompanyUser = async () => {
    const { isShareToCompanyExpirationEnabled }=this.state;
    const data = {
      userId: +localStorage.getItem("id"),
      receivingUserId: +this.state.selectedUserId,
      companyId: +this.state.selectedCompanyId,
      documentId: this.props.fileId,
      messageStatus: this.state.shareToCompanyViewType,
      jobDate: isShareToCompanyExpirationEnabled ? this.state.shareToCompanyExpirationDate : null,
      jobType: "sharedto",
    };
    console.log(data);

    if (!this.state.selectedUserId && !this.state.selectedCompanyId) {
      this.setState({
        notification: {
          message: "User is required",
          type: "success",
          show: true,
        },
      }); 
      return;
    }

    this.setState({ isLoading: true });
    const api = "/job-scheduling/share-file-other-company-user";
    try {
      const response = await addNew(api, data);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
        isLoading: false,
      });
      await this.fetchFileLogs();
      await this.fetchShareJobList();
      this.closeShareModal();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      throw error;
    }
  };


      
      
      
      
  showEditIcon=()=>{
    const fileName=this.props.fileName;
    const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
    if (this.editableFileTypes.includes(fileExt)) {
      this.setState({showEditIcon:true});
    }
  }

  handleEditFile = async (filePath, fileName) => {
    const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
    
    if (this.editableFileTypes.includes(fileExt)) {
      try {
        // Download the file content
        const data = await downloadeDocument(filePath);
        
        if (['.xlsx', '.xls'].includes(fileExt)) {
          const arrayBuffer = await data.arrayBuffer();
          const workbook = XLSX.read(arrayBuffer);
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          let jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
          
          // Ensure we have at least one row with one cell
          if (jsonData.length === 0) jsonData = [['']];
          if (jsonData[0].length === 0) jsonData[0] = [''];
          
          this.setState({
            showEditModal: true,
            editContent: jsonData,
            editFileName: fileName,
            editFileType: fileExt,
            editFileUrl: filePath,
            workbook: workbook,
            sheetName: workbook.SheetNames[0]
          });
        } else if (fileExt === '.txt') {
          // Handle text files (existing code)
          const content = await new Response(data).text();
          this.setState({
            showEditModal: true,
            editContent: content,
            editFileName: fileName,
            editFileType: fileExt,
            editFileUrl: filePath
          });
        } else {
          // Other file types
          this.setState({
            showEditModal: true,
            editContent: 'Binary file content - editing might require special handling',
            editFileName: fileName,
            editFileType: fileExt,
            editFileUrl: filePath
          });
        }
      } catch (error) {
        this.setState({
          notification: {
            message: "Failed to load file for editing",
            type: "error",
            show: true,
          },
        });
      }
    } else {
      this.setState({
        notification: {
          message: "File type not supported for editing",
          type: "warning",
          show: true,
        },
      });
    }
  };
  
  handleSaveEdit = async () => {
    try {
      let file;
      const { editFileType, editFileName, editContent, workbook } = this.state;
  
      if (['.xlsx', '.xls'].includes(editFileType)) {
        // Handle Excel file saving
        const wb = this.state.workbook || XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(editContent);
        
        // Clear existing sheet if it exists
        if (wb.SheetNames.includes(this.state.sheetName)) {
          wb.Sheets[this.state.sheetName] = ws;
        } else {
          XLSX.utils.book_append_sheet(wb, ws, this.state.sheetName || 'Sheet1');
        }
        
        // Generate file
        const excelBuffer = XLSX.write(wb, { 
          bookType: editFileType.replace('.', ''), 
          type: 'array' 
        });
        file = new File([excelBuffer], editFileName, {
          type: editFileType === '.xlsx' 
            ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            : 'application/vnd.ms-excel'
        });
      } else {
        // Handle text files
        const blob = new Blob([editContent], { type: "text/plain" });
        file = new File([blob], editFileName, {
          type: "text/plain",
          lastModified: Date.now()
        });
      }
  
      // Debugging: Verify file before upload
      console.log('File to upload:', file);
      
      // Create FormData
      const formData = new FormData();
      formData.append("file", file);
      formData.append(
        "documentsAttachmentId",
        this.props.fileId
      );
  
      // Debugging: Verify FormData contents
      for (let pair of formData.entries()) {
        console.log(pair[0] + ': ', pair[1]);
      }
  
      await this.saveEditedDocument(formData);
      this.setState({ showEditModal: false });
      
    } catch (error) {
      console.error('Save error:', error);
      this.setState({
        notification: {
          message: `Failed to save file: ${error.message}`,
          type: "error",
          show: true,
        },
      });
    }
  };

saveEditedDocument = async (docData) => {
  const api = "/documentsattachmentdetail/editDocument";
  try {
    const response = await uploadDocument(api, docData);
    this.setState(
      {
        notification: {
          message: `File ${this.props.fileName} edited successfully`,
          type: "success",
          show: true,
        },
      },
      () => {
        this.fetchFileDetails();
        this.fetchFileLogs();
      }
    );
    await this.fetchFileDetails();
    return response.data;
  } catch (error) {
    this.setState({
      notification: {
        message: "Failed to save file",
        type: "error",
        show: true,
      },
      //navigate: true,
    });
    //throw error;
  }
};

updateWorkbook = (newContent) => {
  const { workbook, sheetName } = this.state;
  const wb = workbook || XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(newContent);
  
  if (wb.SheetNames.includes(sheetName)) {
    wb.Sheets[sheetName] = ws;
  } else {
    XLSX.utils.book_append_sheet(wb, ws, sheetName || 'Sheet1');
  }
  
  return wb;
};

renderEditModal = () => {
  if (!this.state.showEditModal) return null;
  
  // Calculate dynamic sizing based on content
  const isExcel = ['.xlsx', '.xls'].includes(this.state.editFileType);
  const { workbook, sheetName, editContent } = this.state;
  
  // For Excel files, we'll use the workbook sheets, for others we'll use editContent directly
  const sheets = workbook ? workbook.SheetNames.map(name => ({
    name,
    data: XLSX.utils.sheet_to_json(workbook.Sheets[name], { header: 1 })
  })) : [{ name: sheetName || 'Sheet1', data: editContent }];
  
  const activeSheetIndex = Math.max(0, sheets.findIndex(sheet => sheet.name === sheetName));
  const currentSheet = sheets[activeSheetIndex] || { name: '', data: [] };
  const rowCount = isExcel ? currentSheet.data.length : 0;
  const colCount = isExcel && currentSheet.data[0] ? currentSheet.data[0].length : 0;
  
  // Dynamic dimensions
  const modalWidth = Math.min(
    window.innerWidth - 40, 
    Math.max(
      600, 
      isExcel ? Math.min(1200, 100 + (colCount * 120)) : 800
    )
  );
  
  const modalHeight = Math.min(
    window.innerHeight - 40,
    isExcel ? Math.min(800, 200 + (rowCount * 40)) : 600
  );

  // Handle sheet switching
  const handleSheetChange = (index) => {
    const sheet = sheets[index];
    this.setState({ 
      sheetName: sheet.name,
      editContent: sheet.data
    });
  };

  // Handle cell changes
  const handleCellChange = (rowIndex, cellIndex, value) => {
    const newData = currentSheet.data.map((row, rIdx) => {
      if (rIdx === rowIndex) {
        const newRow = [...row];
        newRow[cellIndex] = value;
        return newRow;
      }
      return row;
    });
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  // Handle adding new row
  const handleAddRow = () => {
    const colCount = currentSheet.data[0]?.length || 1;
    const newRow = Array(colCount).fill('');
    const newData = [...currentSheet.data, newRow];
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  // Handle removing row
  const handleRemoveRow = (rowIndex) => {
    if (rowCount <= 1) {
      this.setState({
        notification: {
          message: "Cannot remove the last row",
          type: "warning",
          show: true,
        },
      });
      return;
    }
    
    const newData = currentSheet.data.filter((_, idx) => idx !== rowIndex);
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  // Handle removing cell (set to empty string)
  const handleClearCell = (rowIndex, cellIndex) => {
    const newData = currentSheet.data.map((row, rIdx) => {
      if (rIdx === rowIndex) {
        const newRow = [...row];
        newRow[cellIndex] = '';
        return newRow;
      }
      return row;
    });
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  // Handle adding new column
  const handleAddColumn = () => {
    const newData = currentSheet.data.map(row => [...row, '']);
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  // Handle removing column
  const handleRemoveColumn = (colIndex) => {
    if (colCount <= 1) return; // Don't remove last column
    
    const newData = currentSheet.data.map(row => 
      row.filter((_, idx) => idx !== colIndex)
    );
    
    // Update the current sheet's data
    const updatedSheets = [...sheets];
    updatedSheets[activeSheetIndex].data = newData;
    
    // For Excel files, update the workbook
    if (workbook) {
      const ws = XLSX.utils.aoa_to_sheet(newData);
      workbook.Sheets[currentSheet.name] = ws;
    }
    
    this.setState({ 
      editContent: newData,
      workbook: workbook || this.state.workbook
    });
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
      padding: '20px',
      boxSizing: 'border-box'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '5px',
        width: `${modalWidth}px`,
        height: `${modalHeight}px`,
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '15px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <div>
            <h2 style={{ margin: 0 }}>Editing: {this.state.editFileName}</h2>
            {isExcel && workbook && (
              <div style={{ marginTop: '5px' }}>
                {sheets.map((sheet, index) => (
                  <button
                    key={index}
                    onClick={() => handleSheetChange(index)}
                    style={{
                      marginRight: '5px',
                      padding: '5px 10px',
                      background: activeSheetIndex === index ? '#007bff' : '#f0f0f0',
                      color: activeSheetIndex === index ? 'white' : '#333',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer'
                    }}
                  >
                    {sheet.name}
                  </button>
                ))}
              </div>
            )}
          </div>
          <button 
            onClick={() => this.setState({ showEditModal: false })}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        
        {/* Content Area */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          marginBottom: '15px',
          border: '1px solid #ddd',
          borderRadius: '4px'
        }}>
          {this.state.editFileType === '.txt' ? (
            <textarea
              value={this.state.editContent}
              onChange={(e) => this.setState({ editContent: e.target.value })}
              style={{
                width: '100%',
                height: '100%',
                fontFamily: 'monospace',
                padding: '10px',
                border: 'none',
                resize: 'none',
                boxSizing: 'border-box'
              }}
            />
          ) : isExcel ? (
            <div style={{ 
              position: 'relative',
              minWidth: `${colCount * 120}px`,
              minHeight: `${rowCount * 40}px`
            }}>
              <div style={{ 
                overflow: 'auto',
                height: '100%',
                width: '100%',
                position: 'absolute',
                top: 0,
                left: 0
              }}>
                <table style={{ 
                  borderCollapse: 'collapse',
                  width: '100%',
                  tableLayout: 'fixed'
                }}>
                  {/* Column headers with remove buttons */}
                  <thead>
                    <tr>
                      <th style={{ width: '40px' }}></th>
                      {currentSheet.data[0]?.map((_, colIndex) => (
                        <th key={`col-${colIndex}`} style={{ 
                          border: '1px solid #ddd',
                          padding: '5px',
                          position: 'relative'
                        }}>
                          <span>{String.fromCharCode(65 + colIndex)}</span>
                          {colCount > 1 && (
                            <button
                              onClick={() => handleRemoveColumn(colIndex)}
                              style={{
                                position: 'absolute',
                                right: '2px',
                                top: '2px',
                                background: 'transparent',
                                border: 'none',
                                color: 'red',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              ×
                            </button>
                          )}
                        </th>
                      ))}
                      <th style={{ width: '40px' }}>
                        <button
                          onClick={handleAddColumn}
                          style={{
                            background: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            padding: '2px 5px',
                            cursor: 'pointer'
                          }}
                        >
                          +
                        </button>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
  {currentSheet.data.map((row, rowIndex) => (
    <tr key={`row-${rowIndex}`}>
      <td style={{ 
        border: '1px solid #ddd',
        padding: '5px',
        textAlign: 'center'
      }}>
        {rowCount > 1 && (
          <button
            onClick={() => handleRemoveRow(rowIndex)}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'red',
              cursor: 'pointer'
            }}
          >
            ×
          </button>
        )}
      </td>
      {/* Ensure row is an array and has the correct number of columns */}
      {(Array.isArray(row) ? row : []).map((cell, cellIndex) => (
        <td key={`cell-${rowIndex}-${cellIndex}`} style={{ 
          border: '1px solid #ddd', 
          padding: '8px',
          minWidth: '120px',
          height: '40px',
          boxSizing: 'border-box',
          position: 'relative'
        }}>
          <input
            type="text"
            value={cell || ''}
            onChange={(e) => handleCellChange(rowIndex, cellIndex, e.target.value)}
            style={{ 
              width: '100%',
              height: '100%',
              border: 'none',
              padding: '5px',
              boxSizing: 'border-box'
            }}
          />
          <button
            onClick={() => handleClearCell(rowIndex, cellIndex)}
            style={{
              position: 'absolute',
              right: '2px',
              top: '2px',
              background: 'transparent',
              border: 'none',
              color: '#999',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            ×
          </button>
        </td>
      ))}
      {/* If row has fewer columns than expected, add empty cells */}
      {Array.isArray(row) && row.length < colCount && 
        Array.from({ length: colCount - row.length }).map((_, cellIndex) => (
          <td key={`empty-${rowIndex}-${cellIndex}`} style={{ 
            border: '1px solid #ddd', 
            padding: '8px',
            minWidth: '120px',
            height: '40px',
            boxSizing: 'border-box',
            position: 'relative'
          }}>
            <input
              type="text"
              value=""
              onChange={(e) => handleCellChange(rowIndex, row.length + cellIndex, e.target.value)}
              style={{ 
                width: '100%',
                height: '100%',
                border: 'none',
                padding: '5px',
                boxSizing: 'border-box'
              }}
            />
            <button
              onClick={() => handleClearCell(rowIndex, row.length + cellIndex)}
              style={{
                position: 'absolute',
                right: '2px',
                top: '2px',
                background: 'transparent',
                border: 'none',
                color: '#999',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              ×
            </button>
          </td>
        ))
      }
    </tr>
  ))}
</tbody>
                </table>
              </div>
            </div>
          ) : (
            <div style={{ padding: '15px' }}>
              <p>This file type requires special editing tools.</p>
              <p>Content preview:</p>
              <pre style={{
                backgroundColor: '#f5f5f5',
                padding: '15px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '400px'
              }}>
                {this.state.editContent}
              </pre>
            </div>
          )}
        </div>
        
        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: '24px',
          gap: '20px'
        }}>
          <button 
            className="btn btn-secondary"
            onClick={() => this.setState({ showEditModal: false })}
            style={{
              padding: '8px 24px',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            Close
          </button>
          <button 
            disabled={this.state.editContent === "Binary file content - editing might require special handling" }
            className="btn btn-success"
            onClick={this.handleSaveEdit}
            style={{
              padding: '8px 24px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '1rem'
            }}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

fetchDocumentMappingListByFileId= async ()=>{
  const api = "/documentsattachmentdetail/mapped_doc_list_by_document_id/" + this.props.fileId;
  try {
    const response = await getList(api);
    const data = response.data;
    const selectedOptions = data[0].docMappings.map(doc => ({
      value: doc.documentId,
      label: doc.filePath?.split(/Emp_Id_\d+\//)[1] || `Document ${doc.documentId}`
    }));
    console.log("selectedOptions",selectedOptions)
    if(selectedOptions){
      this.setState({
        selectedDocuments: selectedOptions,
        mappedDocsExists:true,
        mappedDocList:data[0].docMappings,
      });
    }
  
  } catch (error) { }

}

openDocMappingModal = () => {
  this.setState({ isDocMappingModalOpen: true });
  this.fetchDocumentsForMapping();
};

fetchDocumentsForMapping = async () => {
    this.setState({ isLoading: true });
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee/" +
      userId +
      `?page=0&size=3000&searchParam=`;
    try {
      const response = await getList(api);
      const data = response.data;
      const filteredList=data.content.filter(doc=> doc.documentsAttachmentId!=this.props.fileId);
      const options = filteredList.map(doc => ({
        value: doc.documentsAttachmentId,
        label: doc.filePath.split(/Emp_Id_\d+\//)[1] || `Document ${doc.documentsAttachmentId}` 
      }));
      this.setState({
        docListToMap: filteredList,
        docSelectOptions: options,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  closeDocMappingModal=()=>{
    this.setState({ isDocMappingModalOpen: false,docListToMap:[] });
  }

  handleDocSelection = (selectedOptions) => {
    const selectedIds = selectedOptions ? selectedOptions.map(opt => opt.value) : [];

    this.setState({
        selectedDocuments: selectedOptions
    },()=>console.log(this.state.selectedDocuments));
  };

handleReorder = (event) => {
  const { active, over } = event;
  
  if (active.id !== over.id) {
    const items = [...this.state.selectedDocuments];
    const oldIndex = items.findIndex(item => String(item.value) === active.id);
    const newIndex = items.findIndex(item => String(item.value) === over.id);
    const newItems = arrayMove(items, oldIndex, newIndex);
    
    this.setState({ selectedDocuments: newItems });
  }
};

  handleDocMapping=()=>{
    const selectedIds = this.state.selectedDocuments 
        ? this.state.selectedDocuments.map(doc => doc.value) 
        : [];
    if(selectedIds){
       this.updateDocMaps(selectedIds);
      console.log("Saving order:", selectedIds);
    }
  }

  updateDocMaps = async (ids) => {
    const docMappings = ids.map(id => ({
      documentId: id, 
    }));
    const data={
      documentsAttachmentId:this.props.fileId,
      docMappings: docMappings,
    }
    const api=`/documentsattachmentdetail/doc_mappings`
    try {
      const response = await addNew(api, data);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.closeDocMappingModal();
      await this.fetchFileLogs();
      await this.fetchDocumentMappingListByFileId();
      return response.data;
    } catch (error) { }
    
  }

  openMappedDocument = (d) =>{
    console.log(d);
    this.setState({mappedDoc:d});
  }

  getFullNameByMail = (email,otherUserCompanyId) => {
    // if(otherUserCompanyId!=null){
    //   const users=fetchUserListByCompanyId(otherUserCompanyId);
    //   const matchedUser = users.find((user) => user.email === email);
    //   return matchedUser ? (matchedUser.firstName + matchedUser.lastName ) : null;
    // }
    // else{
      const matchedUser = this.state.userList.find((user) => user.email === email);
      return matchedUser ? (matchedUser.firstName +" "+ matchedUser.lastName ) : null;  
    // }
  }

  render() {
    if (this.state.navigate) {
      return <Navigate to="/newDS/home" />;
    }

    const {
      previewing,
      filePath,
      fileType,
      deployedURL,
      metaDataList,
      tagList,
      versionHistoryList,
      deletedVersionHistoryList,
    } = this.state;
    const { fileName, fileUrl, onClose, fileId } = this.props;
    const { isRetentionModalOpen, closeRetentionModal } = this.props;
    const { retentionEnd, action } = this.state;
    const { selectedRetentionOption } = this.state;
    const { selectedOption } = this.state;
    const { closeRemModal } = this.props;
    const { isLockModalOpen } = this.state;
    const { selectedRemDate, remEmail, remUserGroup, message } = this.state;
    const { documentDetails } = this.state;
    const fileExtension = fileName
      ? fileName.split(".").pop().toLowerCase()
      : "";

    const getFileSize = () => {
      return "1.2 MB";
    };

    const getFormattedDate = () => {
      return "09-04-2025";
    };

    const truncateText = (text, maxLength) => {
      if (!text) return "";
      const displayText =
        text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
      const needsTooltip = text.length > 8;
      return (
        <div>
          <div
            style={{ cursor: needsTooltip ? "help" : "default" }}
            onMouseEnter={() =>
              needsTooltip && this.setState({ setShowTooltip: true })
            }
            onMouseLeave={() => this.setState({ setShowTooltip: false })}
            title={needsTooltip ? text : undefined}
          >
            {displayText}
          </div>
        </div>
      );
    };

    const {
      isShareModalOpen,
      isRemModalOpen,
      isShareExpirationEnabled,
      isShareToCompanyExpirationEnabled,
      shareExpirationDate,
      shareEmail,
      shareUserGroup, mappedDoc
    } = this.state;
    const isShareEmailDisabled = !!shareUserGroup;
    const isShareUserGroupDisabled = !!shareEmail;

    if (mappedDoc) {
      return (
        <div>
          <FileDetail
            from={"inbox"}
            fileName={mappedDoc.documentName}
            fileUrl={mappedDoc.filePath}
            fileId={mappedDoc.documentId}
            onClose={() => this.setState({ mappedDoc: null })}
          />
        </div>
      );
    }

    return (
      <>
        <Container fluid className="p-0">
          <Row>
            <Col className="col-12">
              <div className={`${classes.actionBtns} d-flex flex-wrap align-items-center mb-2 gap-1 w-100`}>
                <Button
                  variant="primary"
                  onClick={() => {
                    if (!this.state.isViewer) {
                      this.openLockModal();
                    }
                  }}
                  className="lockFileBtn"
                  size="sm"
                  title={`${this.state.isLocked ? "Unlock" : "Lock"} File`}
                >
                  <BsFileEarmarkLock2Fill />
                  <span className="d-none d-md-inline ms-1">{this.state.isLocked ? "Unlock" : "Lock"}</span>
                </Button>
                <Button
                  variant="primary"
                  onClick={this.handleUploadClick}
                  style={{ background: "#1B2B65" }}
                  size="sm"
                  title="Upload New Version"
                >
                  <input
                    type="file"
                    ref={this.fileInputRef}
                    onChange={this.handleFileChange}
                    style={{ display: "none" }}
                    disabled={this.state.isLocked || this.state.isViewer}
                  />
                  <BsFileEarmarkArrowUpFill />
                  <span className="d-none d-md-inline ms-1">New Version</span>
                </Button>
                <Button
                  variant="primary"
                  onClick={(this.state.isLocked || this.state.isViewer) ? null : this.openApprovalModal}
                  style={{ background: "#29CE7F" }}
                  size="sm"
                  title="Approval Workflow"
                >
                  <BsUiRadiosGrid />
                  <span className="d-none d-md-inline ms-1">Approval</span>
                </Button>
                <Button
                  variant="primary"
                  onClick={(this.state.isLocked || this.state.isViewer) ? null : this.openAckModal}
                  style={{ background: "#FD1F9B" }}
                  size="sm"
                  title="Acknowledgment Workflow"
                >
                  <BsFileEarmarkTextFill />
                  <span className="d-none d-md-inline ms-1">Acknowledge</span>
                </Button>
                <Button 
                  variant="primary" 
                  size="sm"
                  title="DocuSign Workflow"
                >
                  <MdEditDocument />
                  <span className="d-none d-lg-inline ms-1">DocuSign</span>
                </Button>
                <Button
                  className="bg-navy"
                  variant="primary"
                  style={{ background: "#1B2B65" }}
                  onClick={() => {
                    if (!this.state.isLocked && !this.state.isViewer) {
                      this.openEsignModal();
                    }
                  }}
                  size="sm"
                  title="DMS eSign Workflow"
                >
                  <MdEditSquare />
                  <span className="d-none d-lg-inline ms-1">eSign</span>
                </Button>
                <CustomBreadcrumb />
              </div>
            </Col>
            <Col className="col-sm-8">
              <Card
                className="mb-2 shadow-sm border-0"
                style={{ borderRadius: "12px", background: "#f8fafc" }}
              >
                <Card.Header
                  className="d-flex align-items-center justify-content-between py-2 px-3"
                  style={{
                    background: "#e9ecef",
                    borderTopLeftRadius: "12px",
                    borderTopRightRadius: "12px",
                    borderBottom: "none",
                  }}
                >
                  <div className="d-flex align-items-center gap-2">
                    <i
                      className="fa fa-file-text-o"
                      style={{ fontSize: "1.2rem", color: "#1B2B65" }}
                    ></i>
                    <span
                      className="fw-bold"
                      style={{ fontSize: "1rem" }}
                    >
                      Document Details
                    </span>
                  </div>
                  <div className="d-flex gap-2 align-items-center">
                    <a
                      href={
                        !this.state.isLocked &&
                        `${deployedURL}/#/newDS/fileUpdate?fileId=${this.props.fileId}`
                      }
                      className={classes.backButtonLink}
                    >
                      <BsGear
                        color="black"
                        title={
                          this.state.isLocked ? "File is locked" :
                          this.state.isViewer ? "Viewers cannot edit files" :
                          "Modify"
                        }               
                        style={{
                          cursor: this.state.isLocked || this.state.isViewer ? "not-allowed" : "pointer",
                          opacity: this.state.isLocked || this.state.isViewer ? 0.5 : 1
                        }}  
                        onClick={
                          (this.state.isLocked || this.state.isViewer) ? (e) => e.preventDefault() : null
                        }
                        size={16}
                      />
                    </a>
                    <div
                      className={classes.backButtonLink}
                      title={
                        this.state.isLocked ? "File is locked" :
                        this.state.isViewer ? "Viewers cannot map" :
                        "Document Mappings"
                      }  
                      >
                      <BsMap
                        color="blue"
                        style={{
                          cursor: this.state.isLocked || this.state.isViewer ? "not-allowed" : "pointer",
                          opacity: this.state.isLocked || this.state.isViewer ? 0.5 : 1
                        }} 
                        onClick={
                          (this.state.isLocked || this.state.isViewer) ? null : this.openDocMappingModal
                        }
                        size={16}
                      />
                    </div>
                    {this.state.showEditIcon &&
                      <div
                        className={classes.backButtonLink}
                        title={
                          this.state.isLocked ? "File is locked" :
                          this.state.isViewer ? "Viewers cannot edit file" :
                          "Edit file"
                        }                        
                      >
                        <BsPencil
                          color="brown"
                          style={{
                            cursor: this.state.isLocked || this.state.isViewer ? "not-allowed" : "pointer",
                            opacity: this.state.isLocked || this.state.isViewer ? 0.5 : 1
                          }}                 
                          onClick={() =>
                            (this.state.isLocked || this.state.isViewer) ? null : this.handleEditFile(fileUrl, fileName)
                          }
                        />
                      </div>
                    }
                    <div
                      className={classes.backButtonLink}
                      title={
                        this.state.isLocked ? "File is locked" :
                        this.state.isViewer ? "Viewers cannot duplicate files" :
                        "Duplicate file"
                      }
                    >
                      <BsCopy 
                        color="blue"
                        style={{
                          cursor: this.state.isLocked || this.state.isViewer ? "not-allowed" : "pointer",
                          opacity: this.state.isLocked || this.state.isViewer ? 0.5 : 1
                        }} 
                        onClick={
                          (this.state.isLocked || this.state.isViewer) ? null : this.openDuplicateModal
                        }
                        size={16}
                      />
                    </div>
                    <div className={classes.backButtonLink} title="Move file">
                      <BsSend
                        title={
                          this.state.isLocked ? "File is locked" :
                          this.state.isEditor ? "Editors cannot move files" :
                          this.state.isViewer ? "Viewers cannot move files" :
                          "Move file"
                        }
                        color="green"
                        style={{
                          cursor: (this.state.isLocked || this.state.isEditor || this.state.isViewer) 
                            ? "not-allowed" 
                            : "pointer",
                          opacity: (this.state.isLocked || this.state.isEditor || this.state.isViewer) 
                            ? 0.5 
                            : 1
                        }}
                        onClick={
                          (this.state.isLocked || this.state.isEditor || this.state.isViewer) ? null : this.openMoveModal
                        }
                        size={16}
                      />
                    </div>
                    <div
                      className={classes.backButtonLink}
                      onClick={
                       ( this.state.isLocked || this.state.isViewer )
                          ? null 
                          : this.openDeleteModal
                      }
                      title={
                        this.state.isLocked ? "File is locked" :
                        this.state.isViewer ? "Viewers cannot delete files" :
                        "Delete file"
                      }
                      style={{
                        cursor: this.state.isLocked || this.state.isViewer ? "not-allowed" : "pointer",
                        opacity: this.state.isLocked || this.state.isViewer ? 0.5 : 1
                      }}                    >
                      <BsTrash color="red" size={16} />
                    </div>
                  </div>
                </Card.Header>
                <Card.Body style={{ padding: "1rem" }}>
                  <Row>
                    <Col className="col-12 col-md-4 mb-3 mb-md-0 d-flex flex-column align-items-center justify-content-center">
                      <div className="d-flex flex-column align-items-center gap-1 w-100">
                        <small className="text-muted">Path: {convertFilePathToInboxFormat(fileUrl)}</small>
                        <div
                          style={{
                            fontSize: "2.5rem",
                            color: "#4285f4",
                            cursor: "pointer",
                            background: "#fff",
                            borderRadius: "50%",
                            width: "60px",
                            height: "60px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            boxShadow: "0 2px 8px rgba(66,133,244,0.08)",
                          }}
                          onClick={(e) => {
                            e.preventDefault();
                            if (this.state.linkPath) {
                              let url = this.state.linkPath;
                              if (!url.match(/^https?:\/\//i)) {
                                url = "http://" + url;
                              }
                              if(!this.state.isLocked){
                                window.open(url, "_blank");
                              }
                            } else {
                              if(!this.state.isLocked){
                                this.previewDocument(fileUrl, fileName);
                              }
                            }
                          }}
                        >
                          <i
                            className={
                              fileExtension === "png" ||
                              fileExtension === "jpg" ||
                              fileExtension === "jpeg"
                                ? "fa fa-file-image-o"
                                : fileExtension === "pdf"
                                ? "fa fa-file-pdf-o"
                                : fileExtension === "doc" ||
                                  fileExtension === "docx"
                                ? "fa fa-file-word-o"
                                : fileExtension === "xls" ||
                                  fileExtension === "xlsx"
                                ? "fa fa-file-excel-o"
                                : "fa fa-file-o"
                            }
                          ></i>
                        </div>
                        <a
                          onClick={(e) => {
                            e.preventDefault();
                            if (this.state.linkPath) {
                              let url = this.state.linkPath;
                              if (!url.match(/^https?:\/\//i)) {
                                url = "http://" + url;
                              }
                              if(!this.state.isLocked){
                                window.open(url, "_blank");
                              }
                            } else {
                              if(!this.state.isViewer && !this.state.isLocked){
                                this.downloadeDocumentAttachment(
                                  fileUrl,
                                  fileName
                                );
                              }
                             
                            }
                          }}
                          style={{
                            color: "#1B2B65",
                            textDecoration: "underline",
                            fontWeight: 600,
                            fontSize: "0.9rem",
                            cursor: "pointer",
                            marginTop: "0.25rem",
                            textAlign: "center",
                            wordBreak: "break-all",
                            maxWidth: "180px",
                          }}
                        >
                          {fileName}
                        </a>
                        {this.state.isLocked && (
                          <span
                            className="badge bg-danger"
                            style={{ fontSize: "0.75rem" }}
                          >
                            <i className="fa fa-lock me-1"></i> Locked
                          </span>
                        )}
                        {this.state.isViewer && (
                          <span
                            className="badge bg-success"
                            style={{ fontSize: "0.75rem" }}
                          >
                            <i className="fa fa-eye me-1"></i> Viewer
                          </span>
                        )}
                        {this.state.isEditor && (
                          <span
                            className="badge bg-primary"
                            style={{ fontSize: "0.75rem" }}
                          >
                            <i className="fa fa-edit me-1"></i> Editor
                          </span>
                        )}
                      </div>
                      <div className="mt-2 w-100 text-center">
                        <small className="text-secondary">Owner:</small>
                        <small className="fw-semibold ms-1" style={{ color: "#1B2B65" }}>
                          {documentDetails.ownerName || "<EMAIL>"}
                        </small>
                      </div>
                      {documentDetails.remarks && (
                        <div className="mt-1 w-100 text-center">
                          <small className="text-secondary">Notes:</small>
                          <div
                            className="text-muted"
                            style={{ fontSize: "0.8rem", wordBreak: "break-word" }}
                          >
                            {documentDetails.remarks}
                          </div>
                        </div>
                      )}
                    </Col>
                    <Col className="col-12 col-md-8">
                      <Row className="gy-2">
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-hashtag text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">Document ID</small>
                          </div>
                          <small>{documentDetails.documentId || "6412"}</small>
                        </Col>
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-calendar text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">Created Date</small>
                          </div>
                          <small>
                            {documentDetails.createdDate || "09-04-2025"}
                          </small>
                        </Col>
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-language text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">OCR Language</small>
                          </div>
                          <small>English</small>
                        </Col>
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-calendar-check-o text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">Due Date</small>
                          </div>
                          <small>
                            {this.state.documentDetails.dueDate &&
                              this.state.documentDetails.dueDate}
                          </small>
                        </Col>
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-barcode text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">Document Number</small>
                          </div>
                          <small>
                            {documentDetails.documentNumber &&
                              truncateText(documentDetails.documentNumber, 12)}
                          </small>
                        </Col>
                        <Col xs={12} md={6}>
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-pencil-square-o text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">Modified Date</small>
                          </div>
                          <small>{this.state.documentDetails.modifiedDate}</small>
                        </Col>
                        {/* {metaDataList &&
                          metaDataList.map((item) => (
                            <Col xs={12} md={6} key={item.key}>
                              <div className="d-flex align-items-center gap-1 mb-1">
                                <i className="fa fa-info-circle text-info" style={{ fontSize: "0.9rem" }}></i>
                                <small className="fw-bold">{item.key}</small>
                              </div>
                              <small>{item.value}</small>
                            </Col>
                          ))} */}
                      </Row>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {versionHistoryList && versionHistoryList.length > 0 && (
                <Card className="border-0 w-100">
                  <Card.Header className="cardHeader py-2">
                    Version History
                  </Card.Header>
                  <Card.Body className="p-0">
                    <div className="table-responsive w-100">
                      <DataTable
                        data={versionHistoryList}
                        columns={[
                          {
                            key: "filePath",
                            header: "VERSION",
                            width: "40%",
                            render: (value, row) => (
                              <span
                                className="text-primary small"
                                style={{ cursor: "pointer" }}
                                onClick={() => this.state.isViewer ? null :
                                  this.previewDocument(
                                    row.filePath,
                                    row.filePath
                                  )
                                }
                              >
                                {row.filePath &&
                                  row.filePath.split(/Emp_Id_\d+\//)[1]}
                              </span>
                            ),
                            sortable: false,
                          },
                          {
                            key: "modifiedDate",
                            header: "DATE & TIME",
                            width: "30%",
                            render: (value, row) => (
                              <small>{formatDate(row.modifiedDate)}</small>
                            ),
                            sortable: true,
                          },
                          {
                            key: "actions",
                            header: "ACTION",
                            width: "30%",
                            render: (_value, row) => (
                              <div className="d-flex align-items-center justify-content-start gap-1">
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="text-decoration-none p-0 text-success"
                                  onClick={() => this.state.isViewer ? null : this.restoreVersionModal(row)}
                                  title="Restore"
                                >
                                  <i className="fa fa-undo" style={{ fontSize: "0.9rem" }}></i>
                                </Button>
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="text-decoration-none p-0"
                                  onClick={() =>
                                    this.state.isViewer ? null :
                                    this.handleVersionDownload(row)
                                  }
                                  title="Download"
                                >
                                  <i className="fa fa-download" style={{ fontSize: "0.9rem" }}></i>
                                </Button>
                                <Button
                                  variant="link"
                                  size="sm"
                                  className="text-decoration-none p-0 text-danger"
                                  onClick={() => this.state.isViewer ? null : this.deleteVersionModal(row)}
                                  title="Delete"
                                >
                                  <i className="fa fa-trash" style={{ fontSize: "0.9rem" }}></i>
                                </Button>
                              </div>
                            ),
                            sortable: false,
                          },
                        ]}
                        searchable={true}
                        itemsPerPage={5}
                        className="table-sm table-striped table-hover"
                        style={{ tableLayout: "fixed", width: "100%" }}
                      />
                    </div>
                  </Card.Body>
                </Card>
              )}

              {deletedVersionHistoryList &&
                deletedVersionHistoryList.length > 0 && (
                  <Card className="border-0 w-100">
                    <Card.Header className="cardHeader py-2">
                      Deleted Versions
                    </Card.Header>
                    <Card.Body className="p-0">
                      <div className="table-responsive w-100">
                        <DataTable
                          data={deletedVersionHistoryList}
                          columns={[
                            {
                              key: "filePath",
                              header: "VERSION",
                              width: "40%",
                              render: (value, row) => (
                                <span
                                  className="text-primary small"
                                  style={{ cursor: "pointer" }}
                                  onClick={() =>
                                    this.previewDocument(
                                      row.filePath,
                                      row.filePath
                                    )
                                  }
                                >
                                  {row.filePath &&
                                    row.filePath.split(/Emp_Id_\d+\//)[1]}
                                </span>
                              ),
                              sortable: false,
                            },
                            {
                              key: "modifiedDate",
                              header: "DATE & TIME",
                              width: "30%",
                              render: (value, row) => (
                                <small>{formatDate(row.modifiedDate)}</small>
                              ),
                              sortable: true,
                            },
                            {
                              key: "actions",
                              header: "ACTION",
                              width: "30%",
                              render: (_value, row) => (
                                <div className="d-flex align-items-center justify-content-start gap-1">
                                  <Button
                                    variant="link"
                                    size="sm"
                                    className="text-decoration-none p-0 text-success"
                                    onClick={() => this.state.isViewer ? null :
                                      this.deletedRestoreVersionModal(row)
                                    }
                                    title="Restore"
                                  >
                                    <i className="fa fa-undo" style={{ fontSize: "0.9rem" }}></i>
                                  </Button>
                                  <Button
                                    variant="link"
                                    size="sm"
                                    className="text-decoration-none p-0 text-danger"
                                    onClick={() => this.state.isViewer ? null :
                                      this.permanentDeleteVersionModal(row)
                                    }
                                    title="Delete"
                                  >
                                    <i className="fa fa-trash" style={{ fontSize: "0.9rem" }}></i>
                                  </Button>
                                </div>
                              ),
                              sortable: false,
                            },
                          ]}
                          searchable={true}
                          itemsPerPage={5}
                          className="table-sm table-striped table-hover"
                          style={{ tableLayout: "fixed", width: "100%" }}
                        />
                      </div>
                    </Card.Body>
                  </Card>
                )}

              {this.state.mappedDocList &&
                this.state.mappedDocList.length > 0 && (
                  <Card className="border-0 w-100">
                    <Card.Header className="cardHeader py-2">
                      Mapped Documents
                    </Card.Header>
                    <Card.Body className="p-0">
                      <div className="table-responsive w-100">
                        <DataTable
                          data={this.state.mappedDocList}
                          columns={[
                            {
                              key: "documentName",
                              header: "DOCUMENT NAME",
                              width: "40%",
                              render: (value, row) => (
                                <span
                                  className="small"
                                  style={{ cursor: "pointer",color:"blue",textDecoration:"underline" }}
                                  onClick={()=> this.state.isViewer ? null : this.openMappedDocument(row)}
                                >
                                  {row.documentName}
                                </span>
                              ),
                              sortable: true,
                            },
                            {
                              key: "filePath",
                              header: "LOCATION",
                              width: "40%",
                              render: (value, row) => (
                                <span>
                                  {row.filePath && getDisplayPath(row.filePath)}
                                </span>
                              ),
                              sortable: true,
                            },
                            {
                              key: "modifiedDate",
                              header: "CREATED DATE",
                              width: "30%",
                              render: (value, row) => (
                                <small>{formatDate(row.modifiedDate)}</small>
                              ),
                              sortable: true,
                            },
                            
                          ]}
                          searchable={true}
                          itemsPerPage={5}
                          className="table-sm table-striped table-hover"
                          style={{ tableLayout: "fixed", width: "100%" }}
                        />
                      </div>
                    </Card.Body>
                  </Card>
                )}

              {!(this.state.isViewer || this.state.isEditor) && <> 
                <Card className="border-0">
                <Card.Header className="cardHeader py-2">
                  <div className={`${classes.auditlogHeader} d-flex justify-content-between align-items-center`}>
                    <span>Audit Log</span>
                    {!this.state.isViewer && 
                    <div className="d-flex justify-content-end align-items-center gap-2">
                      <DownloadReport
                        excelEndpoint={`${GlobalConstants.globalURL}/documentreports/docLogs/exportExcel/${this.props.fileId}`}
                        pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/docLogs/exportPDF/${this.props.fileId}`}
                        reportName="DuplicateFilesReport"
                        onError={this.handleDownloadError}
                      />
                    </div>
                    }
                  </div>
                </Card.Header>
                <Card.Body className="p-0">
                  <div className="table-responsive w-100">
                    <DataTable
                      data={this.state.filterdLogsList}
                      columns={[
                        {
                          key: "createdDate",
                          header: "Date & Time",
                          sortable: true,
                          render: (value, row) => (
                            <small>{formatDate(row.createdDate)}</small>
                          ),
                        },
                        {
                          key: "createdBy",
                          header: "User",
                          sortable: true,
                          render: (value) => <small>{value}</small>,
                        },
                        {
                          key: "logMessage",
                          header: "Action",
                          sortable: true,
                          render: (value) => <small>{value}</small>,
                        },
                      ]}
                      searchable={true}
                      itemsPerPage={5}
                      className="table-sm table-striped table-hover"
                      style={{ tableLayout: "fixed", width: "100%" }}
                    />
                  </div>
                </Card.Body>
              </Card>
              </>}
            </Col>
            <Col className="col-sm-4">
              <div className="d-flex flex-column">
                <Card className="mb-2 boxShadow">
                  <Card.Header className="cardHeader d-flex justify-content-between align-items-center py-2">
                    <span style={{ fontSize: "0.9rem" }}>Reminders</span>
                    <Button
                      variant="link"
                      className="p-0"
                      disabled={this.state.isLocked || this.state.isViewer}
                      onClick={() => {
                        if (!this.state.isLocked && !this.state.isViewer) {
                          this.openRemModal();
                        }
                      }}
                      title="Add Reminder"
                    >
                      <i className="fa fa-plus-circle" style={{ fontSize: "1.1rem" }}></i>
                    </Button>
                  </Card.Header>
                  {this.state.remainderList.length > 0 && (
                    <Card.Body className="py-2 px-3 d-flex justify-content-between align-items-stretch">
                      {this.state.remainderList.map((rem, idx) => (
                        <div key={idx} className="d-flex justify-content-between align-items-center mb-2">
                          <div className="text-end">
                            <small className="text-muted d-block">
                              {formatDate(rem.jobDate)}
                              {rem.email && ( <>
                                To: {rem.email}
                              </>
                            )}
                            </small>
                          </div>
                          <div className="d-flex align-items-center gap-1">
                            <MdEdit
                              color="blue"
                              size={14}
                              style={{ cursor: "pointer" }}
                              onClick={() => {
                                if (!this.state.isLocked && !this.state.isViewer) {
                                  this.handleRemainderEdit(rem);
                                }
                              }}
                              title="Edit reminder"
                            />
                            <BsTrash
                              color="red"
                              size={14}
                              style={{ cursor: "pointer" }}
                              onClick={() => {
                                if (!this.state.isLocked && !this.state.isViewer) {
                                  this.confirmDeleteJob(rem.id);
                                }
                              }}
                              title="Delete reminder"
                            />
                          </div>
                        </div>
                      ))}
                    </Card.Body>
                  )}
                </Card>
                <Card className="mb-2 boxShadow">
                  <Card.Header className="cardHeader d-flex justify-content-between align-items-center py-2">
                    <span style={{ fontSize: "0.9rem" }}>Share File</span>
                    <Button
                      variant="link"
                      className="p-0"
                      disabled={this.state.isLocked || this.state.isViewer}
                      onClick={() => {
                        if (!this.state.isLocked && !this.state.isViewer && !this.state.isEditor) {
                          this.openShareModal();
                        }
                      }}
                    >
                      <i className="fa fa-share-alt" style={{ fontSize: "1.1rem" }}></i>
                    </Button>
                  </Card.Header>
                  {!(this.state.isViewer || this.state.isEditor) && <> 
                    {this.state.shareList.length > 0 && (
                    <Card className="boxShadow mt-2">
                      <Card.Body className="py-2 px-3 align-items-stretch" 
                        style={{
                          maxHeight: this.state.shareList.length > 3 ? "200px" : "none",
                          overflowY: this.state.shareList.length > 3 ? "scroll" : "visible",
                          overflowAnchor :"none"
                        }}
                      >
                            <div className="py-2 px-3" style={{minHeight:"0.01%"}}>  
                        {this.state.shareList.map((share, idx) => (
                          <div
                            className={`d-flex justify-content-between align-items-strech gap-1 mb-2 ${classes.auditlogHeader}`}
                            key={idx}
                          >
                            <div className="text-start">
                              <small className="d-block">
                                {share.email
                                  ? (this.getFullNameByMail(share.email,share.otherUserCompanyId) ? this.getFullNameByMail(share.email,share.otherUserCompanyId) : share.email)
                                  : this.getTeamName(share.teamId)}
                              </small>
                              {share.messageStatus && (
                                <small className="text-secondary d-block" style={{ fontSize: "0.75rem" }}>
                                  Permission: {share.messageStatus}
                                </small>
                              )}
                              {share.jobDate && (
                                <small className="text-muted d-block" style={{ fontSize: "0.75rem" }}>
                                  Expires: {formatDate(share.jobDate)}
                                </small>
                              )}
                            </div>
                            <div className={`d-flex align-items-center gap-1 ${classes.auditlogHeader}`}>
                              <MdEdit
                                color="blue"
                                size={14}
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                  if (!this.state.isLocked && !this.state.isViewer && !this.state.isEditor)
                                    this.handleShareEdit(share);
                                }}
                                title="Edit share"
                              />
                              <BsTrash
                                color="red"
                                size={14}
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                  if (!this.state.isLocked && !this.state.isViewer && !this.state.isEditor)
                                    this.confirmDeleteJob(share.id);
                                }}
                                title="Remove share"
                              />
                            </div>
                          </div>
                        ))}
                        </div>
                      </Card.Body>
                    </Card>
                  )}
                  </>}
                </Card>
                <Card className="mb-2 mt-2 boxShadow">
                  <Card.Header className="cardHeader d-flex justify-content-between align-items-center py-2">
                    <span style={{ fontSize: "0.9rem" }}>Retention</span>
                  </Card.Header>
                  <Card.Body className="p-0 align-items-stretch">
                    {this.state.retentionList.length ? (
                      <div className="py-2 px-3">
                        {this.state.retentionList.map((rem) => (
                          <div key={rem.id} className="d-flex justify-content-between align-items-center mb-2">
                            <div 
                              className="text-start flex-grow-1"
                              onClick={() => {
                                if (!this.state.isLocked && !this.state.isViewer)
                                  this.handleRetentionEdit(rem);
                              }}
                              style={{ cursor: "pointer" }}
                            >
                              <small className="d-block">
                                {rem.messageStatus === "recycle-bin" && (
                                  <span>
                                    <i className="fa fa-trash-o me-1"></i>
                                    Move to recycle bin
                                  </span>
                                )}
                                {rem.messageStatus === "delete" && (
                                  <span>
                                    <i className="fa fa-times-circle me-1"></i>
                                    Delete permanently
                                  </span>
                                )}
                                {rem.messageStatus === "archive" && (
                                  <span>
                                    <i className="fa fa-archive me-1"></i>
                                    Archive
                                  </span>
                                )}
                              </small>
                              <small className="text-muted d-block" style={{ fontSize: "0.75rem" }}>
                                Date: {formatDate(rem.jobDate)}
                              </small>
                            </div>
                            <div>
                              <BsTrash 
                                color="red" 
                                size={14}
                                style={{ cursor: "pointer" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (!this.state.isLocked && !this.state.isViewer)
                                    this.confirmDeleteJob(rem.id);
                                }}
                                title="Remove retention"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="py-2 px-3">
                        <span
                          className={`${classes.rightTopSpan} d-block text-center`}
                          style={{ fontSize: "0.85rem", cursor: "pointer" }}
                          onClick={() => {
                            if (!this.state.isLocked && !this.state.isViewer){
                              this.openRetentionModal();
                            }
                          }}
                        >
                          <i className="fa fa-hourglass-start me-1"></i>
                          Infinite retention
                        </span>
                      </div>
                    )}
                  </Card.Body>
                </Card>
                {metaDataList && 
                <Card className="mb-2 boxShadow">
                  <Card.Header className="cardHeader d-flex justify-content-between align-items-center py-2">
                    <span style={{ fontSize: "0.9rem" }}>Meta Data</span>
                  </Card.Header>
                  {metaDataList.length > 0 && (
                    <Card.Body className="py-2 px-3 d-flex justify-content-between align-items-stretch">
                      {this.state.metaDataList.map((rem, idx) => (
                        <div key={idx} className="d-flex justify-content-between align-items-center mb-2">
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-info-circle text-info" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">{rem.key}{rem.value && <> : {rem.value}</>}</small>
                          </div>
                        </div>
                      ))}
                    </Card.Body>
                  )}
                </Card>
                }
                 {tagList.length>0 && 
                <Card className="mb-2 boxShadow">
                  <Card.Header className="cardHeader d-flex justify-content-between align-items-center py-2">
                    <span style={{ fontSize: "0.9rem" }}>Tags</span>
                  </Card.Header>
                  {tagList.length > 0 && (
                    <Card.Body className="py-2 px-3 d-flex justify-content-between align-items-stretch">
                      {this.state.tagList.map((rem, idx) => (
                        <div key={idx} className="d-flex justify-content-between align-items-center mb-2">
                          <div className="d-flex align-items-center gap-1 mb-1">
                            <i className="fa fa-tags text-danger" style={{ fontSize: "0.9rem" }}></i>
                            <small className="fw-bold">{rem.fieldName}</small>
                          </div>
                        </div>
                      ))}
                    </Card.Body>
                  )}
                </Card>
                }
              </div>
            </Col>
          </Row>
          <div>
            <Modal
              show={this.state.isPreviewModalOpen}
              size="xl"
              onHide={this.closePreviewModal}
            >
              <Modal.Header className="modal-header-modern py-2" closeButton>
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Previewing <strong>"{fileName}"</strong>
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className={`${classes.previewModalBody} p-3`}>
                <div className={`${classes.zoomButtons} mb-2`}>
                  <i
                    className="fa fa-search-plus"
                    aria-hidden="true"
                    style={{ cursor: "pointer", marginRight: "10px", fontSize: "0.9rem" }}
                    onClick={this.zoomIn}
                  >
                    {" "}Zoom In
                  </i>
                  <i
                    className="fa fa-search-minus"
                    aria-hidden="true"
                    style={{ cursor: "pointer", fontSize: "0.9rem" }}
                    onClick={this.zoomOut}
                  >
                    {" "}Zoom Out
                  </i>
                </div>
                {previewing && (
                  <div
                    style={{
                      transform: `scale(${this.state.zoomLevel})`,
                      transformOrigin: "top left",
                    }}
                  >
                    {/* <FileViewer
                      fileType={fileType}
                      filePath={filePath}
                      onError={this.handleError}
                    /> */}
                    <DocumentPreview
                      filePath={filePath}
                      fileName={fileName}
                      closePreview={this.closePreviewModal}
                    />
                  </div>
                )}
              </Modal.Body>
              <Modal.Footer className="py-2">
                <Button size="sm" onClick={this.closePreviewModal}>Close</Button>
              </Modal.Footer>
            </Modal>
          </div>
          <div>
            <Modal
              show={isShareModalOpen}
              onHide={this.closeShareModal}
              size="md"
              dialogClassName={classes.centeredModal}
            >
              <Modal.Header closeButton className="modal-header-modern py-2">
                <Modal.Title style={{ fontSize: "1rem" }}>Share File</Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <ul className="nav nav-tabs nav-tabs-sm" id="shareModalTabs" role="tablist">
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link ${
                        this.state.activeTab === "share" ? "active" : ""
                      }`}
                      id="share-tab"
                      data-bs-toggle="tab"
                      data-bs-target="#share-content"
                      type="button"
                      role="tab"
                      aria-controls="share-content"
                      aria-selected={this.state.activeTab === "share"}
                      onClick={() => this.setState({ activeTab: "share" })}
                      style={{ fontSize: "0.9rem", padding: "0.5rem 1rem" }}
                    >
                      Share File
                    </button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button
                      className={`nav-link ${
                        this.state.activeTab === "company" ? "active" : ""
                      }`}
                      id="company-tab"
                      data-bs-toggle="tab"
                      data-bs-target="#company-content"
                      type="button"
                      role="tab"
                      aria-controls="company-content"
                      aria-selected={this.state.activeTab === "company"}
                      onClick={this.handleCompanySharingTab}
                      style={{ fontSize: "0.9rem", padding: "0.5rem 1rem" }}
                    >
                      Company Sharing
                    </button>
                  </li>
                </ul>

                <div className="tab-content" id="shareModalTabContent">
                  <div
                    className={`tab-pane fade ${
                      this.state.activeTab === "share" ? "show active" : ""
                    }`}
                    id="share-content"
                    role="tabpanel"
                    aria-labelledby="share-tab"
                  >
                    <div className="pt-3">
                      <small className="text-muted">Share file "{fileName}"</small>
                      <div className="d-flex align-items-center mt-2 gap-2">
                        <select
                          className="form-control form-control-sm bg-info text-white"
                          value={shareEmail}
                          onChange={this.handleShareEmailChange}
                          disabled={isShareEmailDisabled}
                          onSelect={() => {
                            this.setState({ isShareInputGiven: true });
                          }}
                        >
                          <option value="">-- Select User --</option>
                          {this.state.userList &&
                          this.state.userList.length > 0 ? (
                            this.state.userList.map((user, index) => (
                              <option key={index} value={user.email}>
                                {user.firstName} {user.lastName}
                              </option>
                            ))
                          ) : (
                            <option value="">No Users available</option>
                          )}
                        </select>
                        <span className="text-muted">or</span>
                        <select
                          className="form-control form-control-sm bg-info text-white"
                          value={shareUserGroup}
                          onChange={this.handleShareUserGroupChange}
                          onSelect={() => {
                            this.setState({ isShareInputGiven: true });
                          }}
                        >
                          <option value="">-- Select User Group --</option>
                          {this.state.usersGroup &&
                          this.state.usersGroup.length > 0 ? (
                            this.state.usersGroup.map((group, index) => (
                              <option key={index} value={group.id}>
                                {group.name}
                              </option>
                            ))
                          ) : (
                            <option value="">No User Groups Available</option>
                          )}
                        </select>
                      </div>
                      <div className="mt-2">
                        <select
                          className="form-control form-control-sm"
                          style={{ backgroundColor: "#e3f2fd" }}
                          name="viewType"
                          value={this.state.viewType}
                          onChange={this.selectedShareValue}
                        >
                          {/* <option value="Preview">Preview</option> */}
                          <option value="Viewer">Viewer</option>
                          <option value="Editor">Editor</option>
                        </select>
                      </div>
                      {this.state.isShareCustomSelected && (
                        <div className="mt-2">
                          <strong className="small">File Permissions</strong>
                          <div className="row mt-1">
                            <div className="col-6">
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="previewFiles"
                                  checked={
                                    this.state.customPermissions.previewFiles
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Preview Files
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="downloadFiles"
                                  checked={
                                    this.state.customPermissions.downloadFiles
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Download Files
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="editAttributes"
                                  checked={
                                    this.state.customPermissions.editAttributes
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Edit Attributes
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="uploadFileVersions"
                                  checked={
                                    this.state.customPermissions.uploadFileVersions
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Upload Versions
                              </label>
                            </div>
                            <div className="col-6">
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="editFilesOnline"
                                  checked={
                                    this.state.customPermissions.editFilesOnline
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Edit Online
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="duplicateFiles"
                                  checked={
                                    this.state.customPermissions.duplicateFiles
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Duplicate Files
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="shareFiles"
                                  checked={this.state.customPermissions.shareFiles}
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Share Files
                              </label>
                              <label className="d-flex align-items-center small mb-1">
                                <input
                                  type="checkbox"
                                  name="seeAuditTrails"
                                  checked={
                                    this.state.customPermissions.seeAuditTrails
                                  }
                                  onChange={this.handleCustomPermissionChange}
                                  className="me-1"
                                />
                                Audit Trails
                              </label>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="mt-2 d-flex align-items-center">
                        <label className={`${classes.slider} me-2`}>
                          <input
                            type="checkbox"
                            checked={isShareExpirationEnabled}
                            onChange={this.toggleShareExpiration}
                          />
                          <div className={classes.sliderTrack}>
                            <div className={classes.sliderThumb}></div>
                          </div>
                        </label>
                        <span className="small">Expiration</span>
                        {isShareExpirationEnabled && (
                          <div className="ms-auto">
                            <Form.Group controlId="shareExpirationDate">
                              <ReactDatePicker
                                selected={this.state.shareExpirationDate}
                                onChange={(date) =>
                                  this.setState({ shareExpirationDate: date })
                                }
                                showTimeSelect
                                minDate={new Date()}
                                dateFormat="dd/MM/yyyy HH:mm"
                                locale="enIN"
                                timeIntervals={15}
                                minTime={
                                  this.state.shareExpirationDate &&
                                  new Date().getDate() ===
                                    this.state.shareExpirationDate.getDate()
                                    ? new Date()
                                    : new Date(new Date().setHours(0, 0, 0, 0))
                                }
                                maxTime={
                                  new Date(new Date().setHours(23, 59, 59, 999))
                                }
                                className="form-control form-control-sm"
                              />
                            </Form.Group>
                          </div>
                        )}
                      </div>
                      <div className="mt-3">
                        <Button variant="info" size="sm" onClick={this.handleShareTo}>
                          Share
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`tab-pane fade ${
                      this.state.activeTab === "company" ? "show active" : ""
                    }`}
                    id="company-content"
                    role="tabpanel"
                    aria-labelledby="company-tab"
                  >
                    <div>
                      <div className="p-3">
                        <div className="mb-3">
                          <h5>Share file "{fileName}"</h5>
                        </div>

                        <div className="mb-3">
                          <label
                            htmlFor="company-select"
                            className="form-label"
                          >
                            Select Company
                          </label>
                          <select
                            id="company-select"
                            className="form-control bg-info text-white"
                            onChange={(e) => this.handleSelectedCompany(e)}
                            value={this.state.selectedCompanyId || ""}
                          >
                            <option value="">-- Select Company --</option>
                            {this.state.companyList &&
                            this.state.companyList.length > 0 ? (
                              this.state.companyList.map((company, index) => (
                                <option key={index} value={company.companyId}>
                                  {company.companyName}
                                </option>
                              ))
                            ) : (
                              <option value="">No Companies available</option>
                            )}
                          </select>
                        </div>

                        {this.state.selectedCompanyId && (
                          <div className="mb-3">
                            <label htmlFor="user-select" className="form-label">
                              Select User
                            </label>
                            <select
                              id="user-select"
                              className="form-control bg-info text-white"
                              onChange={(e) => this.handleSelectedUser(e)}
                              value={this.state.selectedUserId || ""}
                            >
                              <option value="">-- Select User --</option>
                              {this.state.shareUserList &&
                              this.state.shareUserList.length > 0 ? (
                                this.state.shareUserList.map((user, index) => (
                                  <option key={index} value={user.id}>
                                    {user.firstName} {user.lastName}
                                  </option>
                                ))
                              ) : (
                                <option value="">No Users available</option>
                              )}
                            </select>
                          </div>
                        )}
                        {this.state.userError && (
                          <p className="text-danger">{this.state.userError}</p>
                        )}
                        <div className="mb-3">
                          <label
                            htmlFor="view-type-select"
                            className="form-label"
                          >
                            Permission Level
                          </label>
                          <select
                            id="view-type-select"
                            className="form-control"
                            style={{ backgroundColor: "#e3f2fd" }}
                            name="viewType"
                            value={this.state.shareToCompanyViewType}
                            onChange={this.selectedShareToCompanyViewType}
                          >
                            {/* <option value="Preview">Preview</option> */}
                            <option value="Viewer">Viewer</option>
                            <option value="Editor">Editor</option>
                          </select>
                        </div>

                        <div className="mb-3">
                          <div className="d-flex align-items-center">
                            <label className={classes.slider}>
                              <input
                                type="checkbox"
                                checked={isShareToCompanyExpirationEnabled}
                                onChange={this.toggleShareToCompanyExpiration}
                              />
                              <div className={classes.sliderTrack}>
                                <div className={classes.sliderThumb}></div>
                              </div>
                            </label>
                            &nbsp;&nbsp;
                            <span className="ml-2">Expiration</span>
                            {isShareToCompanyExpirationEnabled && (
                              <div className="ms-auto">
                                <Form.Group controlId="shareToCompanyExpirationDate">
                                  <ReactDatePicker
                                    selected={
                                      this.state.shareToCompanyExpirationDate
                                    }
                                    onChange={(date) =>
                                      this.setState({
                                        shareToCompanyExpirationDate: date,
                                      })
                                    }
                                    showTimeSelect
                                    minDate={new Date()}
                                    dateFormat="dd/MM/yyyy HH:mm"
                                    locale="enIN"
                                    timeIntervals={15}
                                    minTime={
                                      this.state.shareToCompanyExpirationDate &&
                                      new Date().getDate() ===
                                        this.state.shareToCompanyExpirationDate.getDate()
                                        ? new Date()
                                        : new Date(
                                            new Date().setHours(0, 0, 0, 0)
                                          )
                                    }
                                    maxTime={
                                      new Date(
                                        new Date().setHours(23, 59, 59, 999)
                                      )
                                    }
                                  />
                                </Form.Group>
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="mt-3">
                          <Button
                            variant="info"
                            onClick={this.handleShareFileToOtherCompanyUser}
                            disabled={this.state.selectedUserId == null}
                          >
                            Share
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={this.state.isRetentionModalOpen}
              onHide={this.closeRetentionModal}
              size="md"
              dialogClassName={classes.centeredModal}
            >
              
              <Modal.Header closeButton className="modal-header-modern py-2">
                <Modal.Title style={{ fontSize: "1rem" }}>Edit Retention</Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <small className="text-muted d-block mb-2">Edit Retention</small>
                <div className="radio-group d-flex gap-3">
                  <label className="small">
                    <input
                      type="radio"
                      value="auto"
                      checked={this.state.selectedOption === "auto"}
                      onChange={this.handleRetentionRadioChange}
                      className="me-1"
                    />
                    Auto (inherit)
                  </label>
                  <label className="small">
                    <input
                      type="radio"
                      value="active"
                      checked={this.state.selectedOption === "active"}
                      onChange={this.handleRetentionRadioChange}
                      className="me-1"
                    />
                    Active
                  </label>
                  {/* <label className="small">
                    <input
                      type="radio"
                      value="inactive"
                      checked={this.state.selectedOption === "inactive"}
                      onChange={this.handleRetentionRadioChange}
                      className="me-1"
                    />
                    Inactive
                  </label> */}
                </div>
                {this.state.selectedOption === "auto" && <div className="mt-2 small">Infinite</div>}
                {this.state.selectedOption === "inactive" && <div></div>}
                {this.state.selectedOption === "active" && (
                  <div className="mt-2">
                    <Form.Group controlId="retentionEnd">
                      <Form.Label className="small">Retention end:</Form.Label>
                      <ReactDatePicker
                        selected={this.state.retentionEnd}
                        onChange={(date) =>
                          this.setState({ retentionEnd: date })
                        }
                        showTimeSelect
                        dateFormat="dd/MM/yyyy HH:mm"
                        locale="enIN"
                        timeIntervals={15}
                        minDate={new Date()}
                        minTime={
                          this.state.retentionEnd &&
                          new Date().getDate() ===
                            this.state.retentionEnd.getDate()
                            ? new Date()
                            : new Date(new Date().setHours(0, 0, 0, 0))
                        }
                        maxTime={new Date(new Date().setHours(23, 59, 59, 999))}
                        className="form-control form-control-sm"
                      />
                    </Form.Group>

                    <Form.Group controlId="actionSelect" className="mt-2">
                      <Form.Label className="small">Take the following action:</Form.Label>
                      <Form.Control
                        as="select"
                        value={this.state.action}
                        onChange={this.handleRetentionActionChange}
                        size="sm"
                      >
                        <option value="">Select an action</option>
                        <option value="recycle-bin">Move to Recycle Bin</option>
                        <option value="delete">Delete permanently</option>
                        <option value="archive">Move to archival</option>
                      </Form.Control>
                    </Form.Group>
                  </div>
                )}
                <div className="mt-3">
                  <Button
                    className="modal-header-modern"
                    onClick={this.handleRetentionSave}
                    disabled={!this.state.action}
                    size="sm"
                  >
                    Save
                  </Button>
                </div>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={isRemModalOpen}
              onHide={this.closeRemModal}
              size="md"
              dialogClassName={classes.centeredModal}
            >
              
              <Modal.Header closeButton className="modal-header-modern py-2">
                <Modal.Title style={{ fontSize: "1rem" }}>Set Reminder</Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <small className="text-muted d-block mb-2">Set Reminder to: "{fileName}"</small>
                <div className="mt-2">
                  <Form.Group controlId="selectedRemDate">
                    <ReactDatePicker
                      selected={new Date(this.state.selectedRemDate)}
                      onChange={(date) =>
                        this.setState({ selectedRemDate: date })
                      }
                      minDate={new Date()}
                      showTimeSelect
                      dateFormat="dd/MM/yyyy HH:mm"
                      locale="enIN"
                      timeIntervals={15}
                      minTime={
                        this.state.shareExpirationDate &&
                        new Date().getDate() ===
                          this.state.shareExpirationDate.getDate()
                          ? new Date()
                          : new Date(new Date().setHours(0, 0, 0, 0))
                      }
                      maxTime={new Date(new Date().setHours(23, 59, 59, 999))}
                      className="form-control form-control-sm"
                    />
                  </Form.Group>
                </div>

                <div className="d-flex align-items-center mt-2 gap-2">
                  <input
                    type="email"
                    placeholder="Remind to (e-mail)"
                    className="form-control form-control-sm"
                    value={remEmail}
                    onChange={this.handleRemEmailChange}
                  />
                  <span className="small text-muted">or</span>
                  <select
                    className="form-control form-control-sm bg-info text-white"
                    value={remUserGroup}
                    onChange={this.handleRemGroupChange}
                  >
                    <option value="">-- Select User Group --</option>
                    {this.state.usersGroup &&
                    this.state.usersGroup.length > 0 ? (
                      this.state.usersGroup.map((group, index) => (
                        <option key={index} value={group.id}>
                          {group.name}
                        </option>
                      ))
                    ) : (
                      <option value="">No User Groups Available</option>
                    )}
                  </select>
                </div>
                <div className="text-danger">{this.state.emailError}</div>

                <div className="mt-2">
                  <textarea
                    placeholder="message..."
                    className="form-control form-control-sm"
                    rows="2"
                    value={message}
                    onChange={this.handleRemMessageChange}
                  ></textarea>
                </div>

                <div className="mt-3">
                  <Button
                    variant="info"
                    onClick={this.handleRemind}
                    disabled={
                      !(remEmail || remUserGroup || this.state.selectedRemDate)
                    }
                    size="sm"
                  >
                    Remind
                  </Button>
                </div>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={isLockModalOpen}
              onHide={this.closeLockModal}
              size="sm"
              dialogClassName={classes.centeredModal}
            >
              
              <Modal.Header closeButton className="modal-header-modern py-2">
                <Modal.Title style={{ fontSize: "1rem" }}>
                  {this.state.isLocked ? "Unlock File" : "Lock File"}
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <p className="small mb-2">
                  Do you want to {this.state.isLocked ? "Unlock" : "Lock"} the
                  file?
                </p>
                <p className="fw-bold small mb-3">"{fileName}"</p>
                <Button variant="info" size="sm" onClick={this.handleLock}>
                  {this.state.isLocked ? "Unlock" : "Lock"}
                </Button>
              </Modal.Body>
            </Modal>
          </div>
          <datalist id="UsersList">
            <option value="<EMAIL>" title="ranjit"></option>
            <option value="<EMAIL>" title="karthik"></option>
            <option value="<EMAIL>" title="praveen"></option>
          </datalist>
          <div>
            <Modal
              show={this.state.isApproval}
              onHide={this.closeApprovalModal}
              size="md"
              centered
            >
              
              <Modal.Header closeButton className="modal-header-modern py-2">
                <Modal.Title style={{ fontSize: "1rem" }}>Approval WorkFlow</Modal.Title>
              </Modal.Header>
              <Modal.Body style={{ backgroundColor: "#f1f1f1", padding: "1rem" }}>
                <h6 className="mb-3">Document Name: {fileName}</h6>
                <form
                  name="approvalWorkFlow"
                  onSubmit={this.handleApprovalSubmit}
                >
                  <div className="row mb-2">
                    <label
                      className="col-sm-3 col-form-label small"
                      htmlFor="desc"
                    >
                      Description
                    </label>
                    <div className="col-sm-9">
                      <textarea
                        name="desc"
                        id="desc"
                        className="form-control form-control-sm"
                        rows="3"
                        required
                        value={this.state.desc}
                        onChange={this.handleApprovalInputChange}
                      ></textarea>
                    </div>
                  </div>
                  <div className="row mb-2">
                    <label className="col-sm-4 col-form-label small">
                      {this.state.selectedApprovalWorkFlowName ? `Selected "${this.state.selectedApprovalWorkFlowName}"` : "Select Workflow"}
                    </label>
                     {/* {this.state.emailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId-${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleApprovalInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select Workflow--
                          </option>
                          {this.state.approveworkflowList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.name} 
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteEmailField(index)}
                        >
                          <i className="fa fa-trash" aria-hidden="true"></i>
                        </button> 
                      </div>
                    ))} */}
                    <Select
                      name="selectedApprovalWorkFlowId"
                      value={
                        this.state.approveworkflowList
                          .map(option => ({ value: String(option.id), label: option.name, id: String(option.id) }))
                          .find(option => option.value === String(this.state.selectedApprovalWorkFlowId)) || null
                      }
                      onChange={option => {
                        this.handleApprovalInputChange({
                          target: {
                            name: 'selectedApprovalWorkFlowId',
                            value: option ? option.value : ''
                          }
                        });
                      }}
                      options={
                        this.state.approveworkflowList.length > 0
                          ? this.state.approveworkflowList.map(wf => ({ value: String(wf.id), label: wf.name, id: String(wf.id) }))
                          : []
                      }
                      placeholder={this.state.approveworkflowList.length === 0 ? 'No workflows available' : '--Select Workflow--'}
                      isClearable
                      // styles={{
                      //   menu: provided => ({ ...provided, maxHeight: 250 }),
                      //   container: provided => ({ ...provided, width: '100%' }),
                      //   control: provided => ({ ...provided, minHeight: 32, fontSize: 14 })
                      // }}
                    />
                  </div> 
                  <div className="row mb-2">
                    <label className="col-sm-3 col-form-label small" htmlFor="type">
                      Type
                    </label>
                    <div className="col-sm-9">
                      <div className="form-check form-check-inline">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="type"
                          id="type1"
                          value="Parallel"
                          required
                          checked={this.state.type === "Parallel"}
                          onChange={this.handleApprovalInputChange}
                        />
                        <label className="form-check-label small" htmlFor="type1">
                          Parallel
                        </label>
                      </div>
                      <div className="form-check form-check-inline">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="type"
                          id="type2"
                          value="Serial"
                          required
                          checked={this.state.type === "Serial"}
                          onChange={this.handleApprovalInputChange}
                        />
                        <label className="form-check-label small" htmlFor="type2">
                          Serial
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="row mb-2">
                    <Form className="mt-1">
                      <Form.Switch
                        onChange={this.onSwitchAction}
                        id="custom-switch"
                        label={<small>Move after successful workflow</small>}
                        checked={this.state.isSwitchOn}
                      />
                    </Form>
                    {this.state.isSwitchOn && (
                      <div className="row mt-2">
                        <label
                          className="col-sm-12 col-form-label small mb-1"
                          htmlFor="folder"
                        >
                          After approval, file will be moved to:
                        </label>
                        <div className="col-12">
                          <input
                            type="text"
                            id="folder"
                            name="folder"
                            className="form-control form-control-sm"
                            placeholder="Type Folder name"
                            value={this.state.folder}
                            onChange={this.handleApprovalInputChange}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="row mb-2">
                    <label className="small mb-1">Approval Users:</label>
                    {this.state.approvalUsers.map((field, index) => (
                      <div key={index} className="align-items-center">
                        <p>{field.name}</p>                         
                      </div>
                    ))}
                  </div>

                  {/* <div className="row mb-2">
                    <label className="small mb-1">Approval Users:</label>
                    {this.state.emailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId-${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleApprovalInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select User--
                          </option>
                          {this.state.userList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.firstName} {user.lastName}
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteEmailField(index)}
                        >
                          <i className="fa fa-trash" aria-hidden="true"></i>
                        </button> 
                      </div>
                    ))}
                  </div> */}
                  {this.state.errorMessage && (
                    <div className="text-danger small mb-2">
                      <strong>{this.state.errorMessage}</strong>
                    </div>
                  )}
                  {/* <div className="mb-3">
                    <button
                      type="button"
                      onClick={this.addEmailField}
                      className="btn btn-sm btn-link p-0"
                    >
                      <i
                        title="add user"
                        className="fa fa-plus-circle me-1"
                        aria-hidden="true"
                      ></i>
                      <small>Add User</small>
                    </button>
                  </div> */}

                  <div className="text-center">
                    <button type="submit" className="btn btn-primary btn-sm">
                      {this.state.isApprovalStarted ? "Update" : "Start"}
                    </button>
                  </div>
                </form>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={this.state.isAcknowledge}
              onHide={this.closeAckModal}
              size="md"
              centered
            >
              <Modal.Header
                closeButton
                className="modal-header-modern py-2"
              >
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Acknowledgement WorkFlow
                </Modal.Title>
              </Modal.Header>
              <Modal.Body style={{ backgroundColor: "#f1f1f1", padding: "1rem" }}>
                <h6 className="mb-3">Document Name: {fileName}</h6>
                <form name="ackWorkFlow" onSubmit={this.handleAckSubmit}>
                  <div className="row mb-2">
                    <label
                      className="col-sm-3 col-form-label small"
                      htmlFor="ackDesc"
                    >
                      Description
                    </label>
                    <div className="col-sm-9">
                      <textarea
                        name="ackDesc"
                        id="ackDesc"
                        className="form-control form-control-sm"
                        rows="3"
                        required
                        value={this.state.ackDesc}
                        onChange={this.handleAckInputChange}
                      ></textarea>
                    </div>
                  </div>
                  <div className="row mb-2">
                    <label className="col-sm-4 col-form-label small">
                    {this.state.selectedAcknowledgementWorkFlowName ? `Selected "${this.state.selectedAcknowledgementWorkFlowName}"` : "Select Workflow"}
                    </label>
                     {/* {this.state.ackEmailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId-${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleAckInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select Workflow--
                          </option>
                          {this.state.ackworkflowList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.name} 
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteEmailField(index)}
                        >
                          <i className="fa fa-trash" aria-hidden="true"></i>
                        </button>
                      </div>
                    ))} */}
                                        {/* <Select
                      name="selectedAcknowledgementWorkFlowId"
                      value={this.state.selectedAcknowledgementWorkFlowId}
                      onChange={this.handleAckInputChange}
                      className="form-control form-control-sm"
                    >
                      <option value="" disabled>--Select Workflow--</option>
                      {this.state.ackworkflowList.map((workflow) => (
                        <option key={workflow.id} value={workflow.id}>
                          {workflow.name}
                        </option>
                      ))}
                    </Select> */}
                    <Select
                      name="selectedAcknowledgementWorkFlowId"
                      value={
                        this.state.ackworkflowList
                          .map(option => ({ value: String(option.id), label: option.name, id: String(option.id) }))
                          .find(option => option.value === String(this.state.selectedAcknowledgementWorkFlowId)) || null
                      }
                      onChange={option => {
                        this.handleAckInputChange({
                          target: {
                            name: 'selectedAcknowledgementWorkFlowId',
                            value: option ? option.value : ''
                          }
                        });
                      }}
                      options={
                        this.state.ackworkflowList.length > 0
                          ? this.state.ackworkflowList.map(wf => ({ value: String(wf.id), label: wf.name, id: String(wf.id) }))
                          : []
                      }
                      placeholder={this.state.ackworkflowList.length === 0 ? 'No workflows available' : '--Select Workflow--'}
                      isClearable
                      // styles={{
                      //   menu: provided => ({ ...provided, maxHeight: 250 }),
                      //   container: provided => ({ ...provided, width: '100%' }),
                      //   control: provided => ({ ...provided, minHeight: 32, fontSize: 14 })
                      // }}
                    />
                  </div> 

                  <div className="row mb-2">
                    <Form className="mt-1">
                      <Form.Switch
                        onChange={this.onAckSwitchAction}
                        id="custom-switch"
                        label={<small>Move after successful workflow</small>}
                        checked={this.state.isAckSwitchOn}
                      />
                    </Form>
                    {this.state.isAckSwitchOn === true ? (
                      <div className="row mt-2">
                        <label
                          className="col-sm-12 col-form-label small mb-1"
                          htmlFor="ackFolder"
                        >
                          After acknowledgment, file will be moved to:
                        </label>
                        <div className="col-12">
                          <input
                            type="text"
                            id="ackFolder"
                            name="ackFolder"
                            className="form-control form-control-sm"
                            placeholder="Type Folder name"
                            value={this.state.ackFolder}
                            onChange={this.handleAckInputChange}
                          />
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="row mb-2">
                    <label className="small mb-1">Acknowledgement Users:</label>
                    {this.state.ackUsers.map((field, index) => (
                      <div key={index} className="align-items-center">
                        <p>{field.name}</p>                         
                      </div>
                    ))}
                  </div>

                  {/* <div className="row mb-2">
                    <label className="small mb-1">Acknowledgement Users:</label>
                    {this.state.ackEmailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId_${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleAckInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select User--
                          </option>
                          {this.state.userList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.firstName} {user.lastName}
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteAckEmailField(index)}
                        >
                          <i
                            title="delete"
                            className="fa fa-trash"
                            aria-hidden="true"
                          ></i>
                        </button>
                      </div>
                    ))}
                    {this.state.errorMessage && (
                      <div className="text-danger small">
                        <strong>{this.state.errorMessage}</strong>
                      </div>
                    )}
                  </div> */}

                  {/* <div className="mb-3">
                    <button
                      type="button"
                      onClick={this.addAckEmailField}
                      className="btn btn-sm btn-link p-0"
                    >
                      <i
                        title="add user"
                        className="fa fa-plus-circle me-1"
                        aria-hidden="true"
                      ></i>
                      <small>Add User</small>
                    </button>
                  </div> */}

                  <div className="text-center">
                    <button type="submit" className="btn btn-primary btn-sm">
                      {this.state.isAckStarted ? "Update" : "Start"}
                    </button>
                  </div>
                </form>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={this.state.isEsign}
              onHide={this.closeEsignModal}
              size="md"
              centered
            >
              <Modal.Header
                closeButton
                className="modal-header-modern py-2"
              >
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Electronic Signing WorkFlow
                </Modal.Title>
              </Modal.Header>
              <Modal.Body style={{ backgroundColor: "#f1f1f1", padding: "1rem" }}>
                <h6 className="mb-3">Document Name: {fileName}</h6>
                <form name="eSignWorkFlow" onSubmit={this.handleESignSubmit}>
                  <div className="row mb-2">
                    <label
                      className="col-sm-3 col-form-label small"
                      htmlFor="eSignDesc"
                    >
                      Description
                    </label>
                    <div className="col-sm-9">
                      <textarea
                        name="eSignDesc"
                        id="eSignDesc"
                        className="form-control form-control-sm"
                        rows="3"
                        required
                        value={this.state.eSignDesc}
                        onChange={this.handleESignInputChange}
                      ></textarea>
                    </div>
                  </div>
                   <div className="row mb-2">
                    <label className="col-sm-4 col-form-label small" htmlFor="desc">
                    {this.state.selectedESignWorkFlowName ? `Selected "${this.state.selectedESignWorkFlowName}"` : "Select Workflow"}
                    </label>
                     {/* {this.state.eSignEmailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId-${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleESignInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select Workflow--
                          </option>
                          {this.state.eSignworkflowList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.name} 
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteEmailField(index)}
                        >
                          <i className="fa fa-trash" aria-hidden="true"></i>
                        </button>
                      </div>
                    ))} */}
                     {/* <Select
                      name="selectedESignWorkFlowId"
                      value={this.state.selectedESignWorkFlowId}
                      onChange={this.handleESignInputChange}
                      className="form-control form-control-sm"
                    >
                      <option value="" disabled>--Select Workflow--</option>
                      {this.state.eSignworkflowList.map((workflow) => (
                        <option key={workflow.id} value={workflow.id}>
                          {workflow.name}
                        </option>
                      ))}
                    </Select> */}
                    <Select
                      name="selectedESignWorkFlowId"
                      value={
                        this.state.eSignworkflowList
                          .map(option => ({ value: String(option.id), label: option.name, id: String(option.id) }))
                          .find(option => option.value === String(this.state.selectedESignWorkFlowId)) || null
                      }
                      onChange={option => {
                        this.handleESignInputChange({
                          target: {
                            name: 'selectedESignWorkFlowId',
                            value: option ? option.value : ''
                          }
                        });
                      }}
                      options={
                        this.state.eSignworkflowList.length > 0
                          ? this.state.eSignworkflowList.map(wf => ({ value: String(wf.id), label: wf.name, id: String(wf.id) }))
                          : []
                      }
                      placeholder={this.state.eSignworkflowList.length === 0 ? 'No workflows available' : '--Select Workflow--'}
                      isClearable
                      // styles={{
                      //   menu: provided => ({ ...provided, maxHeight: 250 }),
                      //   container: provided => ({ ...provided, width: '100%' }),
                      //   control: provided => ({ ...provided, minHeight: 32, fontSize: 14 })
                      // }}
                    />
                  </div> 
                  <div className="row mb-2">
                    <label
                      className="col-sm-3 col-form-label small"
                      htmlFor="eSignType"
                    >
                      Type
                    </label>
                    <div className="col-sm-9">
                      <div className="form-check form-check-inline">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="eSignType"
                          id="Type1"
                          value="Parallel"
                          required
                          checked={this.state.eSignType === "Parallel"}
                          onChange={this.handleESignInputChange}
                        />
                        <label className="form-check-label small" htmlFor="Type1">
                          Parallel
                        </label>
                      </div>
                      <div className="form-check form-check-inline">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="eSignType"
                          id="Type2"
                          value="Serial"
                          required
                          checked={this.state.eSignType === "Serial"}
                          onChange={this.handleESignInputChange}
                        />
                        <label className="form-check-label small" htmlFor="Type2">
                          Serial
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="row mb-2">
                    <Form className="mt-1">
                      <Form.Switch
                        onChange={this.onESignSwitchAction}
                        id="custom-switch"
                        label={<small>Move after successful workflow</small>}
                        checked={this.state.isESignSwitchOn}
                      />
                    </Form>
                    {this.state.isESignSwitchOn === true ? (
                      <div className="row mt-2">
                        <label
                          className="col-sm-12 col-form-label small mb-1"
                          htmlFor="eSignFolder"
                        >
                          After signing, file will be moved to:
                        </label>
                        <div className="col-12">
                          <input
                            type="text"
                            id="eSignFolder"
                            name="eSignFolder"
                            className="form-control form-control-sm"
                            placeholder="Type Folder name"
                            value={this.state.eSignFolder}
                            onChange={this.handleESignInputChange}
                          />
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="row mb-2">
                    <label className="small mb-1">eSign Users:</label>
                    {this.state.eSignUsers.map((field, index) => (
                      <div key={index} className="align-items-center">
                        <p>{field.name}</p>                         
                      </div>
                    ))}
                  </div>
{/* 
                  <div className="row mb-2">
                    <label className="small mb-1">eSign Users:</label>
                    {this.state.eSignEmailFields.map((field, index) => (
                      <div key={index} className="d-flex align-items-center mb-1">
                        <select
                          name={`userId${index}`}
                          value={field.id}
                          onChange={(event) =>
                            this.handleESignInputChange(event, index)
                          }
                          className="form-control form-control-sm"
                        >
                          <option value="" disabled>
                            --Select User--
                          </option>
                          {this.state.userList.map((user, index) => (
                            <option key={user.id} value={user.id}>
                              {user.firstName} {user.lastName}
                            </option>
                          ))}
                        </select>
                        <button
                          className="btn btn-sm btn-link text-danger p-1 ms-1"
                          type="button"
                          onClick={() => this.deleteESignEmailField(index)}
                        >
                          <i className="fa fa-trash" aria-hidden="true"></i>
                        </button>
                      </div>
                    ))}
                    {this.state.errorMessage && (
                      <div className="text-danger small">
                        <strong>{this.state.errorMessage}</strong>
                      </div>
                    )}
                  </div> */}
                  
                  {/* <div className="mb-3">
                    <button
                      type="button"
                      onClick={this.addESignEmailField}
                      className="btn btn-sm btn-link p-0"
                    >
                      <i
                        title="add user"
                        className="fa fa-plus-circle me-1"
                        aria-hidden="true"
                      ></i>
                      <small>Add User</small>
                    </button>
                  </div> */}

                  <div className="text-center">
                    <button type="submit" className="btn btn-primary btn-sm">
                      {this.state.isESignStarted ? "Update" : "Start"}
                    </button>
                  </div>
                </form>
              </Modal.Body>
            </Modal>
          </div>
          <div>
            <Modal
              show={this.state.isDeleteModalOpen}
              onHide={this.closeDeleteModal}
              size="sm"
            >
              <Modal.Header
                closeButton
                className="modal-header-modern py-2"
              >
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Delete File
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <p className="small">Do you want to delete the file?</p>
              </Modal.Body>
              <Modal.Footer className="py-2">
                <Button variant="danger" size="sm" onClick={this.deleteFile}>
                  Move to trash
                </Button>
                <Button variant="danger" size="sm" onClick={this.permanentDeleteFile}>
                  Permanent Delete
                </Button>
              </Modal.Footer>
            </Modal>
            <Modal
              show={this.state.isMoveModalOpen}
              onHide={this.closeMoveModal}
              size="lg"
            >
              <Modal.Header
                closeButton
                className="modal-header-modern py-2"
              >
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Move File
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className="p-3">
                <div className="mb-3">
                  <label htmlFor="folderName" className="small mb-1">Select a Folder</label>
                  <select
                    id="folderName"
                    value={this.state.folderName}
                    onChange={this.handleFolderName}
                    className="form-control form-control-sm"
                  >
                    <option value="" disabled>
                      -- Folder --
                    </option>
                    {this.state.folderList.map((folder, index) => (
                      <option key={index} value={folder}>
                        {folder}
                      </option>
                    ))}
                  </select>
                  {this.state.folderError && (
                    <div className="text-danger small mt-1">{this.state.folderError}</div>
                  )}
                </div>
              </Modal.Body>
              <Modal.Footer className="py-2">
                <Button variant="danger" size="sm" onClick={this.moveFile}>
                  Move
                </Button>
              </Modal.Footer>
            </Modal>
            <Modal
              show={this.state.isDuplicateModalOpen}
              onHide={this.closeDuplicateModal}
              size="lg"
            >
              <Modal.Header
                closeButton
                className="modal-header-modern py-2"
              >
                <Modal.Title style={{ fontSize: "1rem" }}>
                  Duplicate File
                </Modal.Title>
              </Modal.Header>
              <Modal.Body className={`p-3 ${classes.duplicateModalWide}`}>
                <div className="mb-3">
                  <label htmlFor="folderName" className="small mb-1">Select a Folder</label>
                  <select
                    id="folderName"
                    value={this.state.folderName}
                    onChange={this.handleFolderName}
                    className={`form-control form-control-sm ${classes.duplicateFolderSelect}`}
                  >
                    <option value="" disabled>
                      -- Folder --
                    </option>
                    {this.state.folderList.map((folder, index) => (
                      <option key={index} value={folder} title={folder}>
                        {folder}
                      </option>
                    ))}
                  </select>
                  {this.state.folderError && (
                    <div className="text-danger small mt-1">{this.state.folderError}</div>
                  )}
                </div>
              </Modal.Body>
              <Modal.Footer className="py-2">
                <Button variant="danger" size="sm" onClick={this.duplicateFile}>
                  Create Duplicate
                </Button>
              </Modal.Footer>
            </Modal>
            <Modal
              show={this.state.isDeleteVersionModalOpen}
              onHide={this.closeDeleteVersionModal}
            >
              <Modal.Header
                closeButton
                className="modal-header-modern"
                style={{ padding: "8px 15px", height: "45px" }}
              >
                <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                  Delete Version
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                Do you want to delete this version "
                {this.state.versionPath.split(/Emp_Id_\d+\//)[1]}"?
              </Modal.Body>
              <Modal.Footer>
                <Button variant="danger" onClick={this.deleteVersion}>
                  Yes,Delete
                </Button>
                &nbsp;
              </Modal.Footer>
            </Modal>
            <Modal
              show={this.state.isRestoreVersionModalOpen}
              onHide={this.closeRestoreVersionModal}
            >
              <Modal.Header
                closeButton
                className="modal-header-modern"
                style={{ padding: "8px 15px", height: "45px" }}
              >
                <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                  Restore Version
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                Do you want to restore this version "
                {this.state.versionPath.split(/Emp_Id_\d+\//)[1]}"?
              </Modal.Body>
              <Modal.Footer>
                <Button
                  variant="danger"
                  onClick={() => this.restoreVersion("replace")}
                >
                  Replace
                </Button>
                <Button
                  variant="success"
                  onClick={() => this.restoreVersion("save")}
                >
                  Save as version
                </Button>
                &nbsp;
              </Modal.Footer>
            </Modal>

            <Modal
              show={this.state.isPermDeleteVersionModalOpen}
              onHide={this.closePermanentDeleteVersionModal}
            >
              <Modal.Header
                closeButton
                className="modal-header-modern"
                style={{ padding: "8px 15px", height: "45px" }}
              >
                <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                  Permanent Delete
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                Do you want to delete permanently this version "
                {this.state.versionPath.split(/Emp_Id_\d+\//)[1]}"?
              </Modal.Body>
              <Modal.Footer>
                <Button variant="danger" onClick={this.permanentDeleteVersion}>
                  Yes,Delete
                </Button>
                &nbsp;
              </Modal.Footer>
            </Modal>

            <Modal
              show={this.state.isDeletedRestoreModalOpen}
              onHide={this.closeDeletedRestoreVersionModal}
            >
              <Modal.Header
                closeButton
                className="modal-header-modern"
                style={{ padding: "8px 15px", height: "45px" }}
              >
                <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                  Restore Deleted Version
                </Modal.Title>
              </Modal.Header>
              <Modal.Body>
                Do you want to restore this version "
                {this.state.versionPath.split(/Emp_Id_\d+\//)[1]}"?
              </Modal.Body>
              <Modal.Footer>
                <Button
                  variant="success"
                  onClick={() => this.deletedRestoreVersion()}
                >
                  Restore Version
                </Button>
                &nbsp;
              </Modal.Footer>
            </Modal>
            <Modal
    show={this.state.isDocMappingModalOpen}
    onHide={this.closeDocMappingModal}
>
    <Modal.Header
        closeButton
        className="modal-header-modern"
        style={{ padding: "8px 15px", height: "45px" }}
    >
        <Modal.Title style={{ fontSize: "18px", color: "white" }}>
            Document Mapping
        </Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <h5>Document Name : {this.props.fileName}</h5>
        {this.state.isLoading ? (
            <div>Loading documents...</div>
        ) : (
          <>
          <h6>Select Documents to Map: </h6>
          <Select
                isMulti
                options={this.state.docSelectOptions || []}
                onChange={this.handleDocSelection}
                value={this.state.selectedDocuments}
                placeholder="Select documents..."
                className="basic-multi-select"
                classNamePrefix="select"
                isSearchable={true}
                closeMenuOnSelect={false}
            />
          </>
            
        )} 

{this.state.selectedDocuments && this.state.selectedDocuments.length > 0 && (
  <div className="mb-3 p-3 bg-light rounded">
    <h6>Drag to reorder documents:</h6>
    <div className="document-tree">
      <DndContext 
        collisionDetection={closestCenter}
        onDragEnd={this.handleReorder}
      >
        <SortableContext 
          items={this.state.selectedDocuments.map(doc => String(doc.value))}
          strategy={verticalListSortingStrategy}
        >
          <div style={{ padding: 8, minHeight: 50 }}>
            <div className="tree-item fw-bold">
              {this.props.fileName || "Documents"}
            </div>

            {/* Document List */}
            {this.state.selectedDocuments.map((doc,idx) => (
              <SortableItem key={String(doc.value)} id={String(doc.value)}>
                <div className="tree-item" style={{ marginLeft: `${(idx + 1) * 15}px` }}>
                  <span className="text-muted">└──</span> {doc.label}
                </div>
              </SortableItem>
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  </div>
)}
    </Modal.Body>
    <Modal.Footer>
        <Button
            variant="danger"
            onClick={this.closeDocMappingModal}
        >
            Close
        </Button>
        <Button
            variant="success"
            onClick={this.handleDocMapping}
        >
            {this.state.mappedDocsExists ? "Update" : "Save"}
        </Button>
    </Modal.Footer>
</Modal>


            {this.state.isLoading && <Loader />}
            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}
          </div>
        </Container>
        {this.state.showEditIcon && this.renderEditModal()}
      </>
    );
  }
}

export default FileDetail;
