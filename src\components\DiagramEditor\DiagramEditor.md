# Diagram Editor Documentation

## Overview
A React-based diagramming tool with user assignment capabilities built using React Flow.

## Key Features
- Drag & drop node creation
- Node connections via handles
- User assignment to nodes
- Properties panel for editing
- Multiple node types with distinct styling
- Keyboard shortcuts for efficiency

## Components Structure
- **DiagramEditor**: Main container
- **CustomNode**: Individual node rendering
- **UserAssignmentPanel**: Properties/user management
- **Toolbar**: Node creation interface
- **types.js**: Data models and constants

## Usage
Navigate to `/diagram-demo` to test the standalone version.

## User Assignment Flow
1. Click a node to select it
2. Properties panel opens on the right
3. Search and add users
4. Users appear as avatars on the node

## Customization Points
- Node types and styles in types.js
- User data source (currently mock data)
- Styling and layout in each component 