import React, { useEffect, useState } from 'react';
import classes from './SelectCompany.module.css';
import { useNavigate } from 'react-router';
//import Logo from '../../images/DMSLogo.png';
import { getList } from '../../../services/apiService';

function SelectCompany() {
  const navigate = useNavigate();

  const logout = () => {
       // Clear the token and other relevant user information
       localStorage.removeItem('token');
       localStorage.removeItem('userName');
       localStorage.removeItem('firstName');
       localStorage.removeItem('id');
       localStorage.removeItem('role');
       localStorage.removeItem('companyId');
       localStorage.removeItem('companyName');
       
       // Redirect to the login page
       navigate('/', { replace: true });
  };
  const [companies, setCompanies] = useState([]);

  useEffect(() => {
    const fetchCompanies = async () => {
      const api = '/company/list?page=0&size=20&searchParam=';
      try {
        const response = await getList(api);
        setCompanies(response.data.content);
      } catch (err) {
        console.log(err)
      } 
    };
    fetchCompanies();
  }, []);

  useEffect(() => {
    const handlePopState = (event) => {
      // Redirect back to the dashboard if attempting to go back to login
      navigate('/select-company', { replace: true });
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [navigate]);

  const click = (company) => {
    console.log(company);
    navigate("/newDS/workflow-management",{replace:true})
    localStorage.setItem("companyId", company.companyId);
    localStorage.setItem("companyName", company.companyName);
  }

  return (
    <>
  <div className={`${classes.bgTop}`}>
  <div className={classes.blockColor}>
    <div className="row">
      <div className={`col-md-8 ${classes.pT}`}>
        <div className="companyBlock">
          <div className="companyLogo">
            <img
              className="image-fluid logo"
              width="300px"
              height="100px"
              src={process.env.PUBLIC_URL + '/images/DMSLogo.png'}
              alt="Company Logo"
            />
          </div>
        </div>
      </div>

      <div className={`col-md-4 ${classes.pT}`} >
        <a className={`${classes.logoutBtn} btn btnSm`} style={{backgroundColor:"red"}} onClick={logout}>
          <i className="fa fa-power-off"></i> Logout
        </a>
        <div className="companies">
          <h3 className="optionHeading">Select Your Company</h3>
          <ul className="list-group">
            {companies.map((company, index) => (
              <li key={index} className="list-group-item" 
                  style={{cursor:'pointer',}} 
                  onClick={() => click(company)}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#8fc7e6'}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = ''}>
                {company.companyName}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
    </>
  );
}

export default SelectCompany;
