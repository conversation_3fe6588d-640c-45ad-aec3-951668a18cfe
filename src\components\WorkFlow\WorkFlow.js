import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import Form from "react-bootstrap/Form";

export class WorkFlow extends Component {
  state = {
    isApproval: false,
    isAcknowledge: false,
    isEsign: false,
    isSwitchOn: false,
    emailFields: [{ email: "" }],
  };

  onSwitchAction = () => {
    const doesShow = this.state.isSwitchOn;
    this.setState({ isSwitchOn: !doesShow });
  };

  openApprovalModal = () => {
    this.setState({ isApproval: true });
  };

  closeApprovalModal = () => {
    this.setState({ isApproval: false });
  };

  openAckModal = () => {
    this.setState({ isAcknowledge: true });
  };

  closeAckModal = () => {
    this.setState({ isAcknowledge: false });
  };

  openEsignModal = () => {
    this.setState({ isEsign: true });
  };

  closeEsignModal = () => {
    this.setState({ isEsign: false });
  };

  handleInputChange = (index, event) => {
    const newEmailFields = [...this.state.emailFields];
    newEmailFields[index].email = event.target.value;
    this.setState({ emailFields: newEmailFields });
  };

  // Add a new email field when the button is clicked
  addEmailField = () => {
    this.setState((prevState) => ({
      emailFields: [...prevState.emailFields, { email: "" }],
    }));
  };

  // Delete an email field by its index
  deleteEmailField = (index) => {
    const newEmailFields = this.state.emailFields.filter((_, i) => i !== index);
    this.setState({ emailFields: newEmailFields });
  };

  render() {
    return (
      <div className="container mt-4">
        <div className="row text-center">
          <h2>WorkFlow Management</h2>
          <hr />
        </div>
        <div className="row mt-4">
          <div className="col-md-4 col-12 text-center">
            <Button
              variant="primary"
              className="mt-3"
              onClick={() => this.openApprovalModal()}
            >
              Approval WorkFlow
            </Button>
          </div>

          <div className="col-md-4 col-12 text-center">
            <Button
              variant="primary"
              className="mt-3"
              onClick={() => this.openAckModal()}
            >
              Acknowledgement WorkFlow
            </Button>
          </div>

          <div className="col-md-4 col-12 text-center">
            <Button
              variant="primary"
              className="mt-3"
              onClick={() => this.openEsignModal()}
            >
              e-Sign WorkFlow
            </Button>
          </div>
        </div>

        <datalist id="UsersList">
          <option value="<EMAIL>" title="ranjit"></option>
          <option value="<EMAIL>" title="karthik"></option>
          <option value="<EMAIL>" title="praveen"></option>
        </datalist>

        <Modal
          show={this.state.isApproval}
          onHide={this.closeApprovalModal}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title
              style={{ textAlign: "center", width: "100%", margin: 0 }}
            >
              Approval WorkFlow
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ backgroundColor: "#f1f1f1" }}>
            <form name="approvalWorkFlow">
              <div className="row">
                <label className="col-sm-10 col-form-label" htmlFor="Desc">
                  Description
                </label>
                <div className="col-10">
                  <textarea
                    name="desc"
                    id="desc"
                    className="form-control"
                    rows="5"
                    required
                  ></textarea>
                </div>
              </div>
              <div className="row mt-2">
                <label className="col-sm-2 col-form-label" htmlFor="type">
                  Type
                </label>
                <div className="col-sm-10 mt-2">
                  <div className="form-check form-check-inline">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="Type"
                      id="Type1"
                    />
                    <label className="form-check-label" for="inlineCheckbox1">
                      Parallel
                    </label>
                  </div>
                  <div className="form-check form-check-inline">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="Type"
                      id="Type2"
                    />
                    <label className="form-check-label" for="inlineCheckbox2">
                      Serial
                    </label>
                  </div>
                </div>
              </div>

              <div className="row mt-2">
                <Form className="mt-2">
                  <Form.Switch
                    onChange={this.onSwitchAction}
                    id="custom-switch"
                    label=" Move after successful workflow"
                    checked={this.state.isSwitchOn}
                  />
                </Form>
                {this.state.isSwitchOn === true ? (
                  <div className="row mt-2">
                    <label
                      className="col-sm-10 offset-sm-1 col-form-label"
                      htmlFor="folder"
                    >
                      After Everyone has approved file will moved here
                    </label>
                    <div className="col-10 offset-1">
                      <input
                        type="text"
                        id="folder"
                        name="folder"
                        className="form-control"
                        placeholder="Type Folder name"
                      />
                    </div>
                  </div>
                ) : null}
              </div>

              <div className="row mt-2">
                {this.state.emailFields.map((field, index) => (
                  <div key={index} className="mt-2">
                    <input
                      type="email"
                      className=""
                      placeholder="Invite email"
                      value={field.email}
                      size="50"
                      list="UsersList"
                      onChange={(event) => this.handleInputChange(index, event)}
                    />
                    <button
                      className="col-1"
                      title="delete"
                      onClick={() => this.deleteEmailField(index)}
                      style={{
                        backgroundColor: "transparent",
                        border: "none",
                        fontSize: "20px",
                      }}
                    >
                      {/* <i class="fa fa-trash" aria-hidden="true"></i> */}
                    </button>
                  </div>
                ))}
              </div>

              {/* <div className="mt-3">
                <button
                  type="button"
                  onClick={this.addEmailField}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    fontSize: "22px",
                  }}
                >
                  <i
                    title="add"
                    class="fa fa-plus-circle"
                    aria-hidden="true"
                  ></i>
                  User
                </button>
              </div> */}

              <div className="row mt-3">
                <div className="col-12 text-center">
                  <button className="btn btn-primary">Start</button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>

        <Modal
          show={this.state.isAcknowledge}
          onHide={this.closeAckModal}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title
              style={{ textAlign: "center", width: "100%", margin: 0 }}
            >
              Acknowledgement WorkFlow
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ backgroundColor: "#f1f1f1" }}>
            <form name="approvalWorkFlow">
              <div className="row">
                <label className="col-sm-10 col-form-label" htmlFor="Desc">
                  Description
                </label>
                <div className="col-10">
                  <textarea
                    name="desc"
                    id="desc"
                    className="form-control"
                    rows="5"
                    required
                  ></textarea>
                </div>
              </div>

              <div className="row mt-2">
                <Form className="mt-2">
                  <Form.Switch
                    onChange={this.onSwitchAction}
                    id="custom-switch"
                    label=" Move after successful workflow"
                    checked={this.state.isSwitchOn}
                  />
                </Form>
                {this.state.isSwitchOn === true ? (
                  <div className="row mt-2">
                    <label
                      className="col-sm-10 offset-sm-1 col-form-label"
                      htmlFor="folder"
                    >
                      After Everyone has approved file will moved here
                    </label>
                    <div className="col-10 offset-1">
                      <input
                        type="text"
                        id="folder"
                        name="folder"
                        className="form-control"
                        placeholder="Type Folder name"
                      />
                    </div>
                  </div>
                ) : null}
              </div>

              <div className="row mt-2">
                {this.state.emailFields.map((field, index) => (
                  <div key={index} className="mt-2">
                    <input
                      type="email"
                      className=""
                      placeholder="Invite email"
                      value={field.email}
                      size="50"
                      list="UsersList"
                      onChange={(event) => this.handleInputChange(index, event)}
                    />
                    <button
                      title="delete"
                      className="col-1"
                      onClick={() => this.deleteEmailField(index)}
                      style={{
                        backgroundColor: "transparent",
                        border: "none",
                        fontSize: "20px",
                      }}
                    >
                      <i class="fa fa-trash" aria-hidden="true"></i>
                    </button>
                  </div>
                ))}
              </div>

              <div className="mt-3">
                <button
                  type="button"
                  onClick={this.addEmailField}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    fontSize: "22px",
                  }}
                >
                  <i
                    title="add"
                    class="fa fa-plus-circle"
                    aria-hidden="true"
                  ></i>
                  User
                </button>
              </div>

              <div className="row mt-3">
                <div className="col-12 text-center">
                  <button className="btn btn-primary">Start</button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>

        <Modal
          show={this.state.isEsign}
          onHide={this.closeEsignModal}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title
              style={{ textAlign: "center", width: "100%", margin: 0 }}
            >
              Electronic Signing WorkFlow
            </Modal.Title>
          </Modal.Header>
          <Modal.Body style={{ backgroundColor: "#f1f1f1" }}>
            <form name="approvalWorkFlow">
              <div className="row">
                <label className="col-sm-10 col-form-label" htmlFor="Desc">
                  Message
                </label>
                <div className="col-10">
                  <textarea
                    name="desc"
                    id="desc"
                    className="form-control"
                    rows="5"
                    required
                  ></textarea>
                </div>
              </div>
              <div className="row mt-2">
                <label className="col-sm-2 col-form-label" htmlFor="type">
                  Type
                </label>
                <div className="col-sm-10 mt-2">
                  <div className="form-check form-check-inline">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="Type"
                      id="Type1"
                    />
                    <label className="form-check-label" for="inlineCheckbox1">
                      Parallel
                    </label>
                  </div>
                  <div className="form-check form-check-inline">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="Type"
                      id="Type2"
                    />
                    <label className="form-check-label" for="inlineCheckbox2">
                      Serial
                    </label>
                  </div>
                </div>
              </div>

              <div className="row mt-2">
                <Form className="mt-2">
                  <Form.Switch
                    onChange={this.onSwitchAction}
                    id="custom-switch"
                    label=" Move after successful workflow"
                    checked={this.state.isSwitchOn}
                  />
                </Form>
                {this.state.isSwitchOn === true ? (
                  <div className="row mt-2">
                    <label
                      className="col-sm-10 offset-sm-1 col-form-label"
                      htmlFor="folder"
                    >
                      After Everyone has approved file will moved here
                    </label>
                    <div className="col-10 offset-1">
                      <input
                        type="text"
                        id="folder"
                        name="folder"
                        className="form-control"
                        placeholder="Type Folder name"
                      />
                    </div>
                  </div>
                ) : null}
              </div>

              <div className="row mt-2">
                {this.state.emailFields.map((field, index) => (
                  <div key={index} className="mt-2">
                    <input
                      type="email"
                      className=""
                      placeholder="Invite email"
                      value={field.email}
                      size="50"
                      list="UsersList"
                      onChange={(event) => this.handleInputChange(index, event)}
                    />
                    <button
                      title="delete"
                      className="col-1"
                      onClick={() => this.deleteEmailField(index)}
                      style={{
                        backgroundColor: "transparent",
                        border: "none",
                        fontSize: "20px",
                      }}
                    >
                      <i class="fa fa-trash" aria-hidden="true"></i>
                    </button>
                  </div>
                ))}
              </div>

              <div className="mt-3">
                <button
                  type="button"
                  title="delete"
                  onClick={this.addEmailField}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    fontSize: "22px",
                  }}
                >
                  <i class="fa fa-plus-circle" aria-hidden="true"></i> User
                </button>
              </div>

              <div className="row mt-3">
                <div className="col-12 text-center">
                  <button className="btn btn-primary">Start</button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>
      </div>
    );
  }
}

export default WorkFlow;
