import React, { Component } from 'react';
import { getList, formatDate } from "../../services/apiService";
import Notification from '../Notification/Notification';
import { GlobalConstants } from "../../constants/global-constants";
import Loader from "../loader/Loader";
import DownloadReport from '../common/DownloadReport';
import { getDisplayPath } from '../../services/apiService';
import { DataTable } from '../Table/DataTable';
import CustomBreadcrumb from '../common/CustomBreadcrumb';
import { format, isBefore } from "date-fns";
import DatePicker from "react-datepicker";

class AllFilesReport extends Component {
 state={
    isLoading: false,
    uploadedFiles: [],
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    notification: {
        message: "",
        type: "",
        show: false,
    },
    searchParam: "",
    deployedURL: "",
    startDate: "",
    endDate: "",
    currentFilter:null,
 }

 componentDidMount(){
  const today = new Date().toISOString().split("T")[0];
  this.setState({
    startDate: today,
    endDate: today,
  });
    //this.fetchDocumentList();
    const deployedURL = window.location.href.split('/#')[0];
    this.setState({deployedURL: deployedURL});
 }

 handleSearchInput = (event) => {
    const searchParam = event.target.value;  
    this.setState({ searchParam }, () => {
      this.fetchDocumentList();  
    });
  }

 fetchDocumentList = async (filter,page=0, itemsPerPage=this.state.itemsPerPage) => {
    const userId = localStorage.getItem("id");
    this.setState({isLoading: true,currentFilter:filter})
    const api = `/documentreports/list_by_all_files?page=${page}&size=${itemsPerPage}`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      this.setState({
         uploadedFiles: data.content,
         totalItems: data.totalElements,
         currentPage: page,
         itemsPerPage,
         isLoading: false,
        });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    const { startDate, endDate } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
    };
    this.fetchDocumentList(filter,page);
  }

  handleItemsPerPageChange = (newSize) => {
    const { startDate, endDate } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
    };
    this.setState({ itemsPerPage: newSize }, () => {
        this.fetchDocumentList(filter,0, newSize); 
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  
    handleStartDateChange = (date) => {
      const formattedDate = format(date, "yyyy-MM-dd");
  
      const { endDate } = this.state;
      if (endDate && date > new Date(endDate)) {
        console.log("Start date cannot be after the end date");
      } else {
        this.setState({ startDate: formattedDate });
      }
    };
  
    handleEndDateChange = (date) => {
      this.setState({ showEndError: "" });
      if (!date) {
        this.setState({ endDate: null });
        return;
      }
      const formattedDate = format(date, "yyyy-MM-dd");
  
      const { startDate } = this.state;
      if (startDate && isBefore(date, new Date(startDate))) {
        console.log("End date cannot be before the start date");
      } else {
        this.setState({ endDate: formattedDate });
      }
    };
  
    applyFilter = () => {
      this.setState({ uploadedFiles: [] });
      const { startDate, endDate } = this.state;
      const filter = {
        startDate: startDate,
        endDate: endDate,
      };
      this.fetchDocumentList(filter);
    };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true
      }
    });
  };

  render() {
    const { itemsPerPage, startDate,endDate, currentPage, totalItems, uploadedFiles, deployedURL } = this.state;
    
    const columns = [
      {
        key: 'index',
        header: 'S.No',
        render: (_, row, index) => currentPage * itemsPerPage + index + 1
      },
      {
        key: 'documentName',
        header: 'File Name',
        sortable: true,
        render: (value) => (
          <span style={{ color: "#333" }}>
            {value}
          </span>
        )
      },
      {
        key: 'createdBy',
        header: 'Owner Name',
        sortable: true
      },
      {
        key: 'filePath',
        header: 'Location',
        sortable: true,
        render: (value) => value && getDisplayPath(value)
      },
      {
        key: 'createdDate',
        header: 'Added On',
        sortable: true,
        render: (value) => formatDate(value)
      }
    ];

    return (
      <div className="container mt-3">
        <div className="row align-items-center">
          <div className="col-8">
            <h5 className="pt-1">
              <i className="fa fa-file"></i>&nbsp;All Files Reports
            </h5>
          </div>
          <div className="col-4 text-end">
            <div className="d-inline-flex align-items-center gap-2">
              <DownloadReport
                excelEndpoint={`${GlobalConstants.globalURL}/documentreports/allFilesReport/exportAllFilesExcel`}
                pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/allFilesReport/exportAllFilesPDF`}
                reportName="AllFilesReport"
                filter={this.state.currentFilter}
                onError={this.handleDownloadError}
                buttonStyle={{ background: '#fff', color: '#333', border: '1px solid #ccc', fontSize: '16px' }}
              />
              <button
                className="btn btn-link"
                style={{ textDecoration: 'underline', color: '#1976d2', fontWeight: 500, fontSize: '16px' }}
                onClick={() => window.history.back()}
              >
                Back
              </button>
            </div>
          </div>
        </div>


                  <div className="row mt-3">
                    <div className="col-12">
                      <form className="row g-3 align-items-end">
                        <div className="col-md-2">
                          <label htmlFor="startDate">Start Date</label>
                          <DatePicker
                            className="form-control"
                            selected={startDate ? new Date(startDate) : null}
                            onChange={this.handleStartDateChange}
                            dateFormat="dd-MM-yyyy"
                            placeholderText="Start Date"
                            maxDate={endDate ? new Date(endDate) : null}
                          />
                        </div>
        
                        <div className="col-md-2">
                          <label htmlFor="endDate">End Date</label>
                          <DatePicker
                            className="form-control"
                            selected={endDate ? new Date(endDate) : null}
                            onChange={this.handleEndDateChange}
                            dateFormat="dd-MM-yyyy"
                            placeholderText="End Date"
                            minDate={startDate ? new Date(startDate) : null}
                          />
                        </div>
                        <div className="col-md-2">
                          <button
                            type="button"
                            className="btn btn-primary me-2"
                            onClick={this.applyFilter}
                          >
                            Filter
                          </button>
                          {/* <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={this.clearFilter}
                                  >
                                    Clear
                                  </button> */}
                        </div>
                      </form>
                    </div>
                  </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-3">
          <h6 style={{fontWeight: "500"}}>{this.state.totalItems} total Files</h6>
          <div className="col-12">
              <DataTable
                data={uploadedFiles.filter(file => file !== null)}
                columns={columns}
                totalItems={totalItems}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                className="font-weight-normal"
              />
          </div>
        </div>
        {this.state.isLoading && <Loader />}  
      </div>
    );
  }
}

export default AllFilesReport;
