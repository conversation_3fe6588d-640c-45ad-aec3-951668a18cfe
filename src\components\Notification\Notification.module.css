.notification {
    padding:5px 20px;
    position: fixed;
    top: 20px;
    right: -500px; 
    z-index: 2000;
    width:700px;
    max-width: 500px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    transition: right 0.3s ease; 
}

.notification.show {
    right: 20px; 
}

.success {
    background-color: green !important;
    color: white;
}

.error {
    background-color: red !important;
    color: white;
}

.content {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.closeButton {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin-left: 10px;
}

.closeButton:hover {
    opacity: 0.8;
}
