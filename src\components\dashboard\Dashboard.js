import React from 'react';
import { Outlet } from 'react-router-dom';
import MenuBar from '../MenuBar/MenuBar';
import TopMenu from '../MenuBar/TopMenu';
import MainSection from '../MainSection/MainSection';

function Dashboard(props) {
    return (<>
        <div className='container-fluid'>
            <MenuBar />
        </div>
        <div className='container-fluid'>
            {/* <MainSection /> */}
           <div className='row'>
              <div className='col-sm-2 col-12'>       
                <TopMenu count={props.count}/>             
              </div>
              <div className='col-sm-10 col-12' style={{height: '100vh',overflowY: 'auto'}}>
                <Outlet />
              </div>
           </div>     

        </div>
        </>
    );
}

export default Dashboard;