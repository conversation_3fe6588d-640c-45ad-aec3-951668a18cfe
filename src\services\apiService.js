import axios from './api';
import { GlobalConstants } from "../constants/global-constants";
export const getList = async (api, body = null) => {
    const url = `${GlobalConstants.globalURL}${api}`;
    try {
      const response = body
        ? await axios.post(url, body) 
        : await axios.get(url);      
        return response.data;
    } catch (error) {
      console.error("Something went wrong", error);
      throw error;
    }
  };

  export const getListOnly = async (api) => {
    const url = `${GlobalConstants.globalURL}${api}`;
    try {
      const response = await axios.get(url);      
        return response.data;
    } catch (error) {
      console.error("Something went wrong", error);
    }
  };
  
export const addNew=async (api,newUser)=>{
    const url=`${GlobalConstants.globalURL}${api}`
    try{
        const data = await axios.post(url,newUser);
        return data.data;
    }
    catch (error) {
        console.error('Something went wrong', error);
        throw error; 
    }
}

export const uploadDocument=async (api,newUser)=>{
    let axiosConfig = {
        headers: {
            'Content-Type': 'multipart/form-data',
            }
      };
      const companyId = localStorage.getItem('companyId');

      if (companyId) {
        axiosConfig.headers['X-Requested-With'] = companyId;
    }
    const url=`${GlobalConstants.globalURL}${api}`
    try{
        const data = await axios.post(url,newUser,axiosConfig);
        return data.data;
    }
    catch (error) {
        console.error('Something went wrong', error);
        throw error; 
    }
}
//document attachment file-downloade service
export const downloadeDocument=async (filePath)=>{ 
   // alert("hello");
     const url=`${GlobalConstants.globalURL}`+'/documentsattachmentdetail/employeeAddDocdownload?filePath=' + filePath;
    const data = await axios.get(url , { responseType: 'blob' });
    return data.data;
    // const url=`${GlobalConstants.globalURL}${api}`;
    // try {
    //     const data = await axios.get(url)
    //     return data.data;
    // }
    // catch (error) {
    //     console.error('Something went wrong', error);
    //     throw error; 
    // }

}

export const findById=async (api)=>{
    const url = `${GlobalConstants.globalURL}${api}`;
    try{
        const data = await axios.get(url)
        return data.data;
    }
    catch (error) {
        console.error('Something went wrong', error);
        throw error; 
    }
}

export const deleteById=async (api,body=null)=>{
    const url = `${GlobalConstants.globalURL}${api}`;
    try{
        const response = body
        ? await axios.post(url, body) 
        : await axios.delete(url);      
        return response.data;
    }
    catch (error) {
        console.error('Something went wrong', error);
        throw error; 
    }
}
export const editById=async (api,updatedUser)=>{
    const url = `${GlobalConstants.globalURL}${api}`;
    try{
        const data = await axios.put(url,updatedUser)
        return data.data;
    }
    catch (error) {
        console.error('Something went wrong', error);
        throw error; 
    }
}
export const lockUser = async (userId) => {
    const url=`${GlobalConstants.globalURL}`+`/user/inactive/${userId}`;
    const response = await axios.delete(url);
    return response.data;
};

export const unlockUser = async (userId) => {
    const url=`${GlobalConstants.globalURL}`+`/user/active/${userId}`;
    const response = await axios.delete(url);
    return response.data;
};

export const formatDate = (dateString) => {
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit', 
        hour12: false,
        timeZone: 'Asia/Kolkata' 
    };

    const date = new Date(dateString);
    return date.toLocaleString('en-IN', options).replace(',', ' -');
};

export const formatDateOnly = (dateString) => {
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        timeZone: 'Asia/Kolkata' 
    };

    const date = new Date(dateString);
    return date.toLocaleString('en-IN', options).replace(',', ' -');
};

export const getDisplayPath = (fullPath) => {
    const pathParts = fullPath && fullPath.split('/');
    let empIdIndex = null;
    if(pathParts){
        empIdIndex = pathParts.findIndex(part => part.match(/^Emp_Id_\d+$/));
    }
    if(empIdIndex){
        if (empIdIndex >= 0 && empIdIndex < pathParts.length - 1) {
            const relevantParts = pathParts.slice(empIdIndex + 1, -1); 
            return relevantParts.length > 0 ? `${relevantParts.join('/')}/` : '/';
          }
    }
    return '/';
  };

  export const convertFilePathToInboxFormat = (fullPath) => {
    const match = fullPath.match(/Emp_Id_\d+\/(.*\/)/i); 
    return match ? `/Inbox/${match[1]}` : `/Inbox/`;
};