import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register required Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const BarChartAlternative = (props) => {
  const data = {
    labels: ['Duplicate', 'Pending Workflows', 'Retention End', 'Due Date', 'Archive'],
    datasets: [
      {
        label: 'Documents',
        backgroundColor: 'rgba(2,117,216,1)',
        borderColor: 'rgba(2,117,216,1)',
        data: [
          props.duplicateCount || 0,
          props.pendingWFCount || 0,
          props.retentionCount || 0,
          props.dueCount || 0,
          props.archiveCount || 0
        ],
        borderWidth: 1,
      }
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          maxTicksLimit: 6,
          font: {
            size: 10
          }
        }
      },
      y: {
        ticks: {
          min: 0,
          maxTicksLimit: 5,
          font: {
            size: 10
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
    },
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Document Statistics',
        font: {
          size: 14
        }
      }
    },
    layout: {
      padding: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10
      }
    }
  };

  return (
    <div style={{ 
      position: 'relative',
      width: '100%', 
      height: '100%',
      minWidth: '200px',
      minHeight: '150px',
      overflow: 'hidden'
    }}>
      <Bar data={data} options={options} />
    </div>
  );
};

export default BarChartAlternative; 