{"name": "dms-app", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "ajv": "^8.17.1", "array-move": "^4.0.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "buffer": "^6.0.3", "chart.js": "^4.4.7", "clsx": "^2.1.1", "date-fns": "^4.1.0", "docx": "^9.5.0", "docx-preview": "^0.3.5", "docxtemplater": "^3.60.1", "draft-js": "^0.11.7", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html-docx-js": "^0.3.1", "html-to-docx": "^1.8.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lucide-react": "^0.503.0", "mammoth": "^1.9.1", "office-viewer": "^0.3.14", "pako": "^2.1.0", "pdf-lib": "^1.17.1", "pizzip": "^3.1.8", "pptx-preview": "^1.0.5", "pptx2html": "^0.3.4", "pptxgenjs": "^3.12.0", "pptxjs": "^0.0.0", "re-resizable": "^6.11.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.10.5", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-draggable": "^4.4.6", "react-dropdown-select": "^4.11.4", "react-feather": "^2.0.10", "react-file-viewer": "^1.2.1", "react-icons": "^5.5.0", "react-office-viewer": "^1.0.4", "react-paginate": "^8.2.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-select": "^5.10.0", "react-sortable-hoc": "^2.0.0", "reactflow": "^11.11.4", "txml": "^5.1.1", "web-vitals": "^2.1.4", "webslides": "^1.5.0", "xlsx": "^0.18.5"}, "scripts": {"start": "set NODE_OPTIONS=--max-old-space-size=8192 && react-app-rewired start", "build": "set NODE_OPTIONS=--max-old-space-size=4096 && react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"customize-cra": "^1.0.0", "react-app-rewired": "^2.2.1"}}