import React, { useState,useEffect } from 'react';
import { Handle, Position } from 'reactflow';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';
import { DEFAULT_NODE_STYLES, NODE_TYPES, CONNECTION_TYPES } from './types';

const CustomNode = ({ data, selected, type = 'process' }) => {
  const isMultiSelected = data.isMultiSelected || false;
  const [showSubProcesses, setShowSubProcesses] = useState(false);
  const [showActions, setShowActions] = useState(false);

  // Close overlays when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (showSubProcesses || showActions) {
        setShowSubProcesses(false);
        setShowActions(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showSubProcesses, showActions]);

  useEffect(() => {
    // console.log('CustomNode mounted with data:', data);
  }, []);

  // Get levelStatus for color coding
  const levelStatus = data.levelStatus;
  
  // Determine node colors based on levelStatus
  const getNodeColors = () => {
    if (levelStatus === true) {
      return {
        backgroundColor: '#e8f5e8', // Light green
        borderColor: '#4caf50',     // Green
        textColor: '#1b5e20'        // Dark green
      };
    } else if (levelStatus === false) {
      return {
        backgroundColor: '#ffebee', // Light red
        borderColor: '#f44336',     // Red
        textColor: '#c62828'        // Dark red
      };
    } else {
      // Default colors from node type
      return {
        backgroundColor: DEFAULT_NODE_STYLES[type].backgroundColor,
        borderColor: DEFAULT_NODE_STYLES[type].borderColor,
        textColor: DEFAULT_NODE_STYLES[type].textColor
      };
    }
  };
  
  const nodeColors = getNodeColors();

  const nodeStyle = {
    ...DEFAULT_NODE_STYLES[type],
    border: `2px solid ${
      selected ? '#0066cc' : 
      isMultiSelected ? '#ff9800' : 
      nodeColors.borderColor
    }`,
    borderRadius: DEFAULT_NODE_STYLES[type].borderRadius,
    backgroundColor: nodeColors.backgroundColor,
    color: nodeColors.textColor,
    padding: '10px 8px',
    minWidth: '140px',
    minHeight: '80px',
    maxWidth: '180px',
    boxShadow: selected ? '0 0 0 2px rgba(0, 102, 204, 0.2)' : 
               isMultiSelected ? '0 0 0 2px rgba(255, 152, 0, 0.3)' : 
               '0 2px 4px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  };

  const contentStyle = {
    textAlign: 'center',
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  };

  const labelStyle = {
    fontSize: '12px',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '6px',
    wordWrap: 'break-word',
  };

  const usersContainerStyle = {
    display: 'flex',
    flexWrap: 'wrap',
    gap: '3px',
    marginTop: '6px',
    justifyContent: 'center',
  };

  const userAvatarStyle = {
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    backgroundColor: '#007bff',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '9px',
    fontWeight: 'bold',
    border: '1px solid white',
    boxShadow: '0 1px 2px rgba(0,0,0,0.2)',
  };

  const userCountStyle = {
    ...userAvatarStyle,
    backgroundColor: '#6c757d',
    fontSize: '9px',
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const assignedUsers = data.assignedUsers || [];
  const subProcesses = data.subProcesses || [];
  const maxDisplayUsers = 3;
  const displayUsers = assignedUsers.slice(0, maxDisplayUsers);
  const remainingCount = assignedUsers.length - maxDisplayUsers;
  
  // Get connection type information
  const connectionType = data.connectionType || CONNECTION_TYPES.UNKNOWN;
  const connectionReason = data.connectionReason || 'Unknown pattern';
  
  // Check if node has user actions
  const hasUserActions = assignedUsers.length > 0 && (
    type === NODE_TYPES.APPROVAL || 
    type === NODE_TYPES.ESIGN || 
    type === NODE_TYPES.ACKNOWLEDGE ||
    (type === NODE_TYPES.PROCESS && data.workflowType === 'approve') ||
    (type === NODE_TYPES.PROCESS && data.workflowType === 'esign') ||
    (type === NODE_TYPES.PROCESS && data.workflowType === 'acknowledge')
  );

  // Create tooltip content for description
  const tooltipContent = data.description || assignedUsers.length > 0 || connectionType !== CONNECTION_TYPES.UNKNOWN || data.level ? (
    <Tooltip id={`tooltip-${data.id || 'node'}`}>
      <div style={{ maxWidth: '250px' }}>
        <strong>{data.label}</strong>
        {data.description && (
          <>
            <br />
            {data.description}
          </>
        )}
        {assignedUsers.length > 0 && (
          <>
            <br />
            <small style={{ color: '#ccc' }}>
              Assigned: {assignedUsers.map(u => u.name).join(', ')}
            </small>
          </>
        )}
        <br />
        <small style={{ color: '#ccc' }}>
          Type: {type.charAt(0).toUpperCase() + type.slice(1)}
        </small>
        {data.level && (
          <>
            <br />
            <small style={{ color: '#007bff', fontWeight: 'bold' }}>
              Level: {data.level}
            </small>
            {data.rootLevel && (
              <>
                <br />
                <small style={{ color: '#6c757d' }}>
                  Root Level: {data.rootLevel}
                </small>
              </>
            )}
            {data.rootLevelType && (
              <>
                <br />
                <small style={{ 
                  color: data.rootLevelType === 'parallel' ? '#ff9800' : 
                         data.rootLevelType === 'serial' ? '#4caf50' : '#6c757d',
                  fontWeight: 'bold'
                }}>
                  Root Type: {data.rootLevelType.charAt(0).toUpperCase() + data.rootLevelType.slice(1)}
                </small>
              </>
            )}
          </>
        )}
        {levelStatus !== undefined && (
          <>
            <br />
            <small style={{ 
              color: levelStatus === true ? '#4caf50' : levelStatus === false ? '#f44336' : '#6c757d',
              fontWeight: 'bold'
            }}>
              Status: {levelStatus === true ? 'Completed ✓' : levelStatus === false ? 'Pending ✗' : 'Unknown'}
            </small>
          </>
        )}
        {connectionType !== CONNECTION_TYPES.UNKNOWN && (
          <>
            <br />
            <small style={{ color: connectionType === CONNECTION_TYPES.PARALLEL ? '#ff9800' : 
                          connectionType === CONNECTION_TYPES.MIXED ? '#9c27b0' : '#4caf50' }}>
              Connection: {connectionType.charAt(0).toUpperCase() + connectionType.slice(1)}
            </small>
            <br />
            <small style={{ color: '#999', fontSize: '10px' }}>
              {connectionReason}
            </small>
          </>
        )}
      </div>
    </Tooltip>
  ) : null;

  return (
    <OverlayTrigger
      placement="top"
      overlay={tooltipContent}
      delay={{ show: 500, hide: 100 }}
      trigger={(data.description || assignedUsers.length > 0 || connectionType !== CONNECTION_TYPES.UNKNOWN || data.level) ? ['hover', 'focus'] : []}
    >
      <div style={nodeStyle}>
        {/* Input Handles - Left and Top */}
        <Handle
          type="target"
          position={Position.Left}
          id="input-left"
          style={{ 
            background: '#4CAF50',
            width: '10px',
            height: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            border: '2px solid white',
          }}
        />
        <Handle
          type="target"
          position={Position.Top}
          id="input-top"
          style={{ 
            background: '#4CAF50',
            width: '10px',
            height: '10px',
            left: '50%',
            transform: 'translateX(-50%)',
            border: '2px solid white',
          }}
        />
        
        {/* Input labels */}
        <div style={{
          position: 'absolute',
          left: '-28px',
          top: '50%',
          transform: 'translateY(-50%)',
          fontSize: '9px',
          color: '#4CAF50',
          fontWeight: '600',
          pointerEvents: 'none',
        }}>
          IN
        </div>
        <div style={{
          position: 'absolute',
          top: '-25px',
          left: '50%',
          transform: 'translateX(-50%)',
          fontSize: '9px',
          color: '#4CAF50',
          fontWeight: '600',
          pointerEvents: 'none',
        }}>
          IN
        </div>
      
      {/* Node Content with proper styling for decision nodes */}
      <div style={{
        ...contentStyle,
      }}>
        {/* Node Label */}
        <div style={{
          ...labelStyle,
          position: 'relative',
        }}>
          {data.label}
          {selected && (
            <div style={{
              position: 'absolute',
              top: '-8px',
              right: '-8px',
              backgroundColor: '#007bff',
              color: 'white',
              borderRadius: '50%',
              width: '20px',
              height: '20px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              fontWeight: 'bold',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              zIndex: 10,
            }}
            title="Node selected - Press Ctrl+C to copy"
            >
              📋
            </div>
          )}
        </div>
        
        {/* Connection Type Indicator */}
        {connectionType !== CONNECTION_TYPES.UNKNOWN && (
          <div style={{
            position: 'absolute',
            top: '-8px',
            left: '-8px',
            backgroundColor: connectionType === CONNECTION_TYPES.PARALLEL ? '#ff9800' : 
                          connectionType === CONNECTION_TYPES.MIXED ? '#9c27b0' : '#4caf50',
            color: 'white',
            borderRadius: '50%',
            width: '16px',
            height: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '8px',
            fontWeight: 'bold',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            zIndex: 10,
          }}
          title={`Connection Type: ${connectionType.charAt(0).toUpperCase() + connectionType.slice(1)} - ${connectionReason}`}
          >
            {connectionType === CONNECTION_TYPES.PARALLEL ? '∥' : 
             connectionType === CONNECTION_TYPES.MIXED ? '⊞' : '→'}
          </div>
        )}
        
        {/* Description (if exists) - simplified for decision nodes */}
        {data.filePath && (
          <div style={{ 
            fontSize: '11px', 
            color: '#666', 
            textAlign: 'center',
            marginBottom: '4px' 
          }}>
            {data.fileName}
          </div>
        )}
        
        {/* Sub-processes indicator for process nodes */}
        {type === NODE_TYPES.PROCESS && subProcesses.length > 0 && (
          <div style={{
            ...usersContainerStyle,
            marginTop: '4px',
          }}>
            <button
              style={{
                backgroundColor: '#fff3e0',
                color: '#e65100',
                border: '1px solid #f57c00',
                borderRadius: '12px',
                padding: '2px 8px',
                fontSize: '10px',
                fontWeight: 500,
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              onClick={(e) => {
                e.stopPropagation();
                setShowSubProcesses(!showSubProcesses);
              }}
              title={`${subProcesses.length} sub-process${subProcesses.length > 1 ? 'es' : ''} available`}
            >
              <span>📋</span>
              {subProcesses.length} sub-process{subProcesses.length > 1 ? 'es' : ''}
            </button>
          </div>
        )}


        {/* User Actions for nodes with assigned users */}
        {hasUserActions && (
          <div style={{
            ...usersContainerStyle,
            marginTop: '4px',
          }}>
            <button
              style={{
                backgroundColor: '#e8f5e8',
                color: '#1b5e20',
                border: '1px solid #388e3c',
                borderRadius: '12px',
                padding: '2px 8px',
                fontSize: '10px',
                fontWeight: 500,
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              title="View user actions"
            >
              <span>⚡</span>
              Actions
            </button>
          </div>
        )}

        {/* Assigned Users */}
        {assignedUsers.length > 0 && (
          <div style={{
            ...usersContainerStyle,
          }}>
            {assignedUsers.length === 1 && (
              <div
                style={{
                  backgroundColor: '#f0f4fa',
                  color: '#333',
                  borderRadius: '12px',
                  padding: '2px 8px',
                  fontSize: '10px',
                  fontWeight: 500,
                  maxWidth: '120px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
                title={assignedUsers[0].name}
              >
                {assignedUsers[0].name}
              </div>
            )}
            {assignedUsers.length > 1 && (
              <>
                <div
                  style={{
                    backgroundColor: '#f0f4fa',
                    color: '#333',
                    borderRadius: '12px',
                    padding: '2px 8px',
                    fontSize: '10px',
                    fontWeight: 500,
                    maxWidth: '120px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    marginRight: '4px',
                  }}
                  title={assignedUsers[0].name}
                >
                  {assignedUsers[0].name}
                </div>
                <div
                  style={{
                    backgroundColor: '#e0e0e0',
                    color: '#555',
                    borderRadius: '12px',
                    padding: '2px 8px',
                    fontSize: '10px',
                    fontWeight: 500,
                    cursor: 'pointer',
                  }}
                  title={assignedUsers.map(u => u.name).join(', ')}
                >
                  +{assignedUsers.length - 1} more
                </div>
              </>
            )}
          </div>
        )}
        
        {/* No users assigned indicator */}
        {assignedUsers.length === 0 && (
          <div style={{
            ...usersContainerStyle,
            color: '#999',
          }}>
            No users assigned
          </div>
        )}
      </div>
      
      {/* Output Handles - Right and Bottom */}
      <Handle
        type="source"
        position={Position.Right}
        id="output-right"
        style={{ 
          background: '#FF5722',
          width: '10px',
          height: '10px',
          top: '50%',
          transform: 'translateY(-50%)',
          border: '2px solid white',
        }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="output-bottom"
        style={{ 
          background: '#FF5722',
          width: '10px',
          height: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          border: '2px solid white',
        }}
      />
      
      {/* Output labels with connection type indicators */}
      <div style={{
        position: 'absolute',
        right: '-31px',
        top: '50%',
        transform: 'translateY(-50%)',
        fontSize: '9px',
        color: '#FF5722',
        fontWeight: '600',
        pointerEvents: 'none',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '2px',
      }}>
        <span>OUT</span>
        <span style={{ fontSize: '8px', color: '#ff9800' }}>∥</span>
      </div>
      <div style={{
        position: 'absolute',
        bottom: '-25px',
        left: '50%',
        transform: 'translateX(-50%)',
        fontSize: '9px',
        color: '#FF5722',
        fontWeight: '600',
        pointerEvents: 'none',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '2px',
      }}>
        <span>OUT</span>
        <span style={{ fontSize: '8px', color: '#4caf50' }}>→</span>
      </div>

      {/* Sub-processes Overlay */}
      {showSubProcesses && subProcesses.length > 0 && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '0',
          right: '0',
          backgroundColor: '#fff3e0',
          border: '1px solid #f57c00',
          borderRadius: '8px',
          padding: '8px',
          marginTop: '4px',
          zIndex: 1000,
          boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
          minWidth: '200px',
        }}>
          <div style={{
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#e65100',
            marginBottom: '6px',
            borderBottom: '1px solid #f57c00',
            paddingBottom: '4px',
          }}>
            Sub-processes:
          </div>
          {subProcesses.map((subProcess, index) => (
            <div
              key={index}
              style={{
                fontSize: '11px',
                color: '#333',
                padding: '4px 0',
                borderBottom: index < subProcesses.length - 1 ? '1px solid #ffcc80' : 'none',
                cursor: 'pointer',
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Handle sub-process click - could trigger navigation or modal
                console.log('Sub-process clicked:', subProcess);
              }}
            >
              <div style={{ fontWeight: '500' }}>{subProcess.name || `Sub-process ${index + 1}`}</div>
              {subProcess.description && (
                <div style={{ fontSize: '10px', color: '#666', fontStyle: 'italic' }}>
                  {subProcess.description}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* User Actions Overlay */}
      {showActions && hasUserActions && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: '0',
          right: '0',
          backgroundColor: '#e8f5e8',
          border: '1px solid #388e3c',
          borderRadius: '8px',
          padding: '8px',
          marginTop: '4px',
          zIndex: 1000,
          boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
          minWidth: '200px',
        }}>
          <div style={{
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#1b5e20',
            marginBottom: '6px',
            borderBottom: '1px solid #388e3c',
            paddingBottom: '4px',
          }}>
            User Actions:
          </div>
          {assignedUsers.map((user, index) => (
            <div key={index} style={{
              fontSize: '11px',
              color: '#333',
              padding: '4px 0',
              borderBottom: index < assignedUsers.length - 1 ? '1px solid #a5d6a7' : 'none',
            }}>
              <div style={{ fontWeight: '500', marginBottom: '4px' }}>{user.name}</div>
              <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
                {(type === NODE_TYPES.APPROVAL || (type === NODE_TYPES.PROCESS && data.workflowType === 'approve')) && (
                  <button
                    style={{
                      backgroundColor: '#4caf50',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '2px 6px',
                      fontSize: '9px',
                      cursor: 'pointer',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Approval action for user:', user.name);
                    }}
                  >
                    Approve
                  </button>
                )}
                {(type === NODE_TYPES.ESIGN || (type === NODE_TYPES.PROCESS && data.workflowType === 'esign')) && (
                  <button
                    style={{
                      backgroundColor: '#ff9800',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '2px 6px',
                      fontSize: '9px',
                      cursor: 'pointer',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('ESign action for user:', user.name);
                    }}
                  >
                    E-Sign
                  </button>
                )}
                {(type === NODE_TYPES.ACKNOWLEDGE || (type === NODE_TYPES.PROCESS && data.workflowType === 'acknowledge')) && (
                  <button
                    style={{
                      backgroundColor: '#2196f3',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      padding: '2px 6px',
                      fontSize: '9px',
                      cursor: 'pointer',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log('Acknowledge action for user:', user.name);
                    }}
                  >
                    Acknowledge
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      </div>
    </OverlayTrigger>
  );
};

export default CustomNode;
