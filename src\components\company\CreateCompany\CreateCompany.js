import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { DataTable } from "../../Table/DataTable";
import classes from "./CreateCompany.module.css";
import { useNavigate } from "react-router-dom";
import { getList, addNew, editById } from "../../../services/apiService";
import Notification from "../../Notification/Notification";
import { isContentEditable } from "@testing-library/user-event/dist/utils";

export class CreateCompany extends Component {
  state = {
    showPassword: false,
    isCompanyExist: false,
    isOpen: false,
    isEditing: false,
    companyList: [],
    newCompany: {
      companyName: "",
      companyAliasName: "",
      logo: "",
      phone: "",
      website: "",
      address: "",
      receivingMail: "",
      password: "",
    },
    notification: {
      message: "",
      type: "",
      show: false,
    },
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 25,
    searchParam: "",
    aliasNameExists: "",
  };

  componentDidMount() {
    this.fetchCompanyList();
  }

  handleSearchInputChange = (event) => {
    const searchParam = event.target.value;
    this.setState({ searchParam: searchParam }, () => {
      console.log(searchParam);
      this.fetchCompanyList();
    });
  };

  fetchCompanyList = async (page = 0) => {
    const { itemsPerPage } = this.state;
    const api = `/company/list?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        companyList: data.content,
        currentPage: page,
        totalItems: data.totalElements,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      console.log(error);
    }
  };

  handlePageChange = (selectedFile) => {
    this.fetchCompanyList(selectedFile.selected);
  };

  handleInputChange = (e) => {
    this.setState({aliasNameExists:""});
    const { name, value } = e.target;
    this.setState((prevState) => ({
      newCompany: {
        ...prevState.newCompany,
        [name]: value,
      },
    }));
  };

  handleCompanyNameChange = (e) => {
    const companyValue = e.target.value;
    this.setState((prevState) => ({
      isCompanyExist: false,
      newCompany: {
        ...prevState.newCompany,
        companyName: companyValue,
      },
    }));
    this.check(companyValue);
  };  

  createCompany = async (newCompany) => {
    const aliasName=newCompany.companyAliasName;
    if (aliasName) {
      const exists = this.state.companyList.some(i => i.companyAliasName && i.companyAliasName.toLowerCase() === aliasName.toLowerCase());
      if (exists) {
        this.setState({aliasNameExists : "Alias Name already exists in DB"})
        return; 
      }
    }
    
    const api = "/company";
    try {
      const response = await addNew(api, newCompany);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
    finally{
      this.closeModal();
    }
  };

  editCompany = async (companyId, updatedCompany) => {
    const aliasName=updatedCompany.companyAliasName;
    if (aliasName) {
      const exists = this.state.companyList.some(
        (i) => i.companyAliasName && i.companyAliasName.toLowerCase() === aliasName.toLowerCase() && i.companyId !== companyId 
      );
  
      if (exists) {
        this.setState({ aliasNameExists: "Alias Name already exists in another company" });
        return; 
      }
    }
    const api = `/company/${companyId}`;
    try {
      const response = await editById(api, updatedCompany);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
    finally{
      this.closeModal();
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();
    const { newCompany, isEditing } = this.state;

    try {
      if (isEditing) {
        await this.editCompany(newCompany.companyId, newCompany);
      } else {
        await this.createCompany(newCompany);
      }
      this.fetchCompanyList();
    } catch (error) {
      console.log(error);
    }
  };

  openModal = (company = null) => {
    if (company) {
      this.setState({
        isOpen: true,
        isEditing: true,
        newCompany: { ...company },
      });
    } else {
      this.setState({
        isOpen: true,
        isEditing: false,
        newCompany: {
          companyName: "",
          companyAliasName: "",
          logo: "",
          phone: "",
          website: "",
          address: "",
        },
      });
    }
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      isCompanyExist: false,
      isEditing: false,
      aliasNameExists:"",
      newCompany: {
        companyName: "",
        companyAliasName: "",
        logo: "",
        phone: "",
        website: "",
        address: "",
        receivingMail: "",
        password: "",
        showPassword: false,
      },
    });
  };
  selectCompany = () => {
    this.props.navigate("/new-company");
  };

  togglePasswordVisibility = () => {
    this.setState((prevState) => ({ showPassword: !prevState.showPassword }));
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  check = async (companyName) => {
    console.log(companyName);
    const api = `/company/validateCompany?companyName=${companyName}`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({ isCompanyExist: response });
    } catch (error) {}
  };
  render() {
    const { itemsPerPage, currentPage, totalItems } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);
    
    // Define columns for DataTable
    const columns = [
      {
        key: 'index',
        header: 'S.No',
        width: '60px',
        render: (_, row, index) => start + index
      },
      {
        key: 'companyName',
        header: 'Company Name',
        sortable: true
      },
      {
        key: 'companyAliasName',
        header: 'Alias Name',
        sortable: true
      },
      {
        key: 'phone',
        header: 'Phone',
        sortable: true
      },
      {
        key: 'website',
        header: 'Website',
        sortable: true
      },
      {
        key: 'address',
        header: 'Address',
        sortable: true
      },
      {
        key: 'actions',
        header: 'Actions',
        width: '100px',
        render: (_, company) => (
          <button
            title="edit company"
            className="btn btn-primary"
            onClick={() => this.openModal(company)}
          >
            <i className="fa fa-edit"></i>
          </button>
        )
      }
    ];

    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Create Company</h4>
            <hr />
            {/* <Button variant="primary" onClick={() => this.openModal()}>
              <i className="fa fa-user-plus"></i>&nbsp; Add Company
            </Button> */}
            {/* &nbsp;&nbsp;&nbsp;
            <Button variant="primary" onClick={this.selectCompany}>
              Select Company
            </Button> */}
          </div>
        </div>
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <Modal show={this.state.isOpen} onHide={this.closeModal} size="lg">
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title>
              {this.state.isEditing ? "Edit Company" : "New Company"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <form name="companyCreate" onSubmit={this.handleSubmit}>
              <div className="row">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="companyName"
                >
                  Company Name :
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="companyName"
                    id="companyName"
                    value={this.state.newCompany.companyName}
                    className="form-control"
                    placeholder="Enter Company Name"
                    required
                    onChange={this.handleCompanyNameChange}
                  />
                  {this.state.isCompanyExist && (
                    <span style={{ color: "red" }}>
                      Company Name already exists in DB
                    </span>
                  )}
                </div>
              </div>

              <div className="row">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="companyAliasName"
                >
                  Alias Name :
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="companyAliasName"
                    id="companyAliasName"
                    value={this.state.newCompany.companyAliasName}
                    className="form-control"
                    placeholder="Enter Alias Name"
                    required
                    onChange={this.handleInputChange}
                  />
                   {this.state.aliasNameExists && <p className="text-danger">
                    {this.state.aliasNameExists} 
                    </p>}
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="logo"
                >
                  Logo
                </label>
                <div className="col-sm-7">
                  <input
                    type="file"
                    id="logo"
                    name="logo"
                    value={this.state.newCompany.logo}
                    className="form-control"
                    placeholder=""
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="phone"
                >
                  Phone :
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="phone"
                    id="phone"
                    value={this.state.newCompany.phone}
                    className="form-control"
                    placeholder="Enter Phone No."
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="receivingMail"
                >
                  Receiving email :
                </label>
                <div className="col-sm-7">
                  
                  <input
                    type="email"
                    name="receivingMail"
                    id="receivingMail"
                    value={this.state.newCompany.receivingMail}
                    className="form-control"
                    placeholder="Enter Receiving email"
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="password"
                >
                  Password :
                </label>
                <div className="col-sm-7">
                  <div className="input-group">
                    <input
                      type={this.state.showPassword ? "text" : "password"}
                      name="password"
                      id="password"
                      value={this.state.newCompany.password}
                      className="form-control"
                      placeholder="Enter password"
                      required
                      onChange={this.handleInputChange}
                    />
                    <div className="input-group-append">
                      <span
                        className="btn btn-outline-gray"
                        type="button"
                        onClick={this.togglePasswordVisibility}
                      >
                        {this.state.showPassword ? (
                          <i className="fa fa-eye-slash" title="hide"></i>
                        ) : (
                          <i className="fa fa-eye" title="show"></i>
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="website"
                >
                  Website :
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    id="website"
                    name="website"
                    value={this.state.newCompany.website}
                    className="form-control"
                    placeholder="Enter Website URL"
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="address"
                >
                  Address :
                </label>
                <div className="col-sm-7">
                  <textarea
                    type="text"
                    id="address"
                    name="address"
                    value={this.state.newCompany.address}
                    className="form-control"
                    placeholder="Enter Company Address"
                    required
                    rows="3"
                    onChange={this.handleInputChange}
                  ></textarea>
                </div>
              </div>

              <div className="row m-3">
                <div className="col-12 text-end">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={this.state.isCompanyExist}
                  >
                    {this.state.isEditing ? "Update" : "Submit"}
                  </Button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>

        <Modal
          show={this.state.showDeleteConfirm}
          onHide={this.closeDeleteConfirm}
        >
          <Modal.Header closeButton>
            <Modal.Title>Confirm Deletion</Modal.Title>
          </Modal.Header>
          <Modal.Body>Are you sure you want to delete this company?</Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={this.closeDeleteConfirm}>
              Cancel
            </Button>
            <Button variant="danger" onClick={this.confirmDeleteCompany}>
              Delete
            </Button>
          </Modal.Footer>
        </Modal>

        <div className="row mt-2">
          <div className="col-12 d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <h4 className="mb-0">
                <i className="fa fa-building-o"></i>&nbsp;List of Companies
              </h4>
              <Button
                className="ms-3"
                variant="primary"
                onClick={() => this.openModal()}
              >
                <i className="fa fa-user-plus"></i>&nbsp;Add
              </Button>
              <Button
                className="ms-3"
                variant="primary"
                onClick={this.selectCompany}
              >
                Select Company
              </Button>
            </div>

            {/* Server-side search is handled by the API, so we don't need the DataTable's built-in search */}
            <div className="d-flex align-items-center">
              <input
                title="search"
                type="search"
                value={this.state.searchParam}
                name="searchParam"
                className="form-control"
                placeholder="Search"
                style={{ height: "40px" }}
                onChange={this.handleSearchInputChange}
              />
            </div>
          </div>
        </div>

        {/* <div className="row mt-4"> */}
        {/* <div className="col-12"> */}
        <div className="mt-4">
          <DataTable
            data={this.state.companyList}
            columns={columns}
            className="table-sm"
            searchable={false} // Disable built-in search since we're using server-side search
            itemsPerPage={this.state.itemsPerPage}
            totalItems={this.state.totalItems}
            currentPage={this.state.currentPage}
            onPageChange={(page) => this.fetchCompanyList(page)}
            onItemsPerPageChange={(size) => {
              this.setState({ itemsPerPage: size }, () => {
                this.fetchCompanyList(0);
              });
            }}
          />
        </div>

        {/* </div> */}
        {/* </div> */}
      </div>
    );
  }
}

const CreateCompanyWrapper = (props) => {
  const navigate = useNavigate();
  return <CreateCompany {...props} navigate={navigate} />;
};

export default CreateCompanyWrapper;
