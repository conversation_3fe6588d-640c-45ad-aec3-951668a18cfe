const { override } = require('customize-cra');

module.exports = override((config) => {
  // Customize output paths for JS, CSS, and media files
  config.output = {
    ...config.output,
    filename: 'dss/js/[name].js', // JS files
    chunkFilename: 'dss/js/[name].chunk.js', // Chunked JS
    assetModuleFilename: 'dss-assets/media/[hash][ext]', // Media files
  };

  // Modify CSS paths by targeting MiniCssExtractPlugin
  config.plugins.forEach((plugin) => {
    if (plugin.constructor.name === 'MiniCssExtractPlugin') {
      plugin.options.filename = 'dss/css/[name].css'; // CSS files
      plugin.options.chunkFilename = 'dss/css/[name].chunk.css'; // Chunked CSS
    }
  });

  return config;
});
