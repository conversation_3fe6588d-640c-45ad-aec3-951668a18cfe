.marginnTop {
  margin-top: 10px;
}

.fileNameDiv {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.fileInfoDiv {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ColMd4 {
  flex: 0 0 33.33%;
  padding: 5px;
}

.fileInfoItem {
  display: flex;
  flex-direction: column;
  margin-bottom: 5px;
}

.Label {
  font-weight: 600;
  margin-bottom: 2px;
  color: #555;
  font-size: 0.75rem;
  text-transform: uppercase;
}

.Span {
  color: #333;
  font-size: 0.85rem;
}

.preVerison {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.preVerison h6 {
  color: #4a6cf7;
  margin-bottom: 8px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
  font-size: 0.9rem;
}

.auditLog {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.auditLog h5 {
  color: #4a6cf7;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 1rem;
}

.rightTopSpan {
  color: #4a6cf7;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  transition: all 0.2s;
  font-size: 0.85rem;
}

.rightTopSpan:hover {
  color: #3a5ce5;
  text-decoration: underline;
}

.rightTopSpan i {
  margin-right: 3px;
  font-size: 0.85rem;
}

.previewModalBody {
  height: 70vh;
  overflow: auto;
  background-color: #f5f5f5;
  padding: 0.5rem;
}

.zoomButtons {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
}

.zoomButtons i {
  margin: 0 5px;
  cursor: pointer;
  color: #4a6cf7;
  transition: transform 0.2s;
  font-size: 0.9rem;
}

.zoomButtons i:hover {
  transform: scale(1.1);
}

.slider {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 18px;
}

.sliderTrack {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 18px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}

.sliderThumb {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

input:checked + .sliderTrack {
  background-color: #4a6cf7;
}

input:checked + .sliderTrack .sliderThumb {
  transform: translateX(18px);
}

.centeredModal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.Ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.Li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.Li:hover {
  background-color: #f8f9fa;
}

.Li:last-child {
  border-bottom: none;
}

/* Card styling */
.card {
  background-color: #fff;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

/* File view button */
.fileView {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 32px;
  background-color: #4a6cf7;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 0.85rem;
  box-shadow: 0 1px 3px rgba(74, 108, 247, 0.2);
}

.fileView:hover {
  background-color: #3a5ce5;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.3);
}

/* Action buttons */
.Buttons {
  display: block;
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 8px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 0.85rem;
  box-shadow: 0 1px 3px rgba(74, 108, 247, 0.15);
  display: flex;
  align-items: center;
}

.Buttons:hover {
  background-color: #3a5ce5;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.25);
}

.Buttons:disabled {
  background-color: #b0b0b0;
  cursor: not-allowed;
  box-shadow: none;
}

.secondaryButton {
  background-color: transparent;
  color: #4a6cf7;
  border: 1px solid #4a6cf7;
  box-shadow: none;
}

.secondaryButton:hover {
  background-color: #f0f4ff;
  box-shadow: 0 1px 3px rgba(74, 108, 247, 0.08);
}

/* Metadata chips */
.metadataChip {
  display: inline-flex;
  align-items: center;
  background-color: #f0f4ff;
  color: #4a6cf7;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.8rem;
  margin-right: 6px;
  margin-bottom: 4px;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(74, 108, 247, 0.08);
}

.metadataChip:hover {
  background-color: #e0e8ff;
  transform: translateY(-1px);
}

.metadataChip i {
  margin-right: 4px;
  font-size: 0.75rem;
}

/* Breadcrumb navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 0.8rem;
  background-color: #f8f9fa;
  padding: 6px 10px;
  border-radius: 4px;
}

.breadcrumb a {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s;
}

.breadcrumb a:hover {
  color: #4a6cf7;
}

.separator {
  margin: 0 5px;
  color: #6c757d;
}

.current {
  color: #4a6cf7;
  font-weight: 600;
}

/* Link styling */
.linkRef {
  color: white;
  text-decoration: none;
  margin: 0 3px;
  font-weight: 500;
  transition: color 0.2s;
  font-size: 0.85rem;
}

.linkRef:hover {
  color: #f0f4ff;
  text-decoration: underline;
}

.backButtonLink {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  color: #4a6cf7;
  text-decoration: none;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s;
  background-color: transparent;
}

.backButtonLink:hover {
  background-color: rgba(74, 108, 247, 0.1);
  transform: translateY(-1px);
}

.backButtonLink i {
  font-size: 16px;
  transition: transform 0.2s;
}

.backButtonLink:hover i {
  transform: scale(1.1);
}

.backButtonLink span {
  font-size: 10px;
  font-weight: 500;
}

/* Add to FileDetail.module.css */
.sharedHeader {
  color: #fd1f9b;
  font-weight: 600;
  font-size: 14px;
}

.sharedEditLink {
  font-size: 12px;
  color: #017efa;
  cursor: pointer;
}

.sharedBadge {
  background-color: #f0f4ff;
  color: #4a6cf7;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.8rem;
  transition: all 0.2s;
}

.sharedBadge:hover {
  background-color: #e0e8ff;
}

/* Action buttons specific styling */
.actionBtns {
  gap: 0.25rem !important;
}

.actionBtns button {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.8rem !important;
}

.actionBtns button svg {
  width: 14px !important;
  height: 14px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ColMd4 {
    flex: 0 0 50%;
  }
  
  .fileInfoDiv {
    padding: 6px;
  }
  
  .Buttons {
    padding: 6px 8px;
    font-size: 0.8rem;
  }
  
  .preVerison, .auditLog {
    padding: 8px;
  }
  .auditlogHeader {
    flex-direction: row !important;
    width: 100%;
  }
  
  .actionBtns button span {
    display: none !important;
  }
}

@media (max-width: 576px) {
  .ColMd4 {
    flex: 0 0 100%;
  }
  
  .fileNameDiv {
    padding: 6px;
  }
  
  .zoomButtons {
    top: 3px;
    right: 3px;
    padding: 3px 5px;
  }
  
  .metadataChip {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
  .actionBtns {
    gap: 0.2rem !important;
  }
}

/* Table specific compact styles */
.table-sm td,
.table-sm th {
  padding: 0.25rem !important;
  font-size: 0.85rem !important;
}

/* Card header compact */
.cardHeader {
  padding: 0.5rem 1rem !important;
  font-size: 0.9rem !important;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.cardHeader-shared {
  padding: 0.4rem 1rem !important;
  font-size: 0.85rem !important;
  background-color: #ffeef8;
  border-bottom: 1px solid #ffd0e8;
}

.boxShadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
}

/* Modal compact styles */
.modal-header-modern {
  background-color: #4a6cf7;
  color: white;
  padding: 0.5rem 1rem !important;
}

.modal-header-modern .modal-title {
  font-size: 1rem !important;
  color: white !important;
}

.modal-header-modern .btn-close {
  filter: brightness(0) invert(1);
}

/* Ensure audit log header alignment */
.auditlogHeader {
  font-size: 0.85rem;
}

/* Duplicate Modal Custom Styles */
.duplicateModalWide {
  min-width: 450px;
  max-width: 98vw;
}

.duplicateFolderSelect {
  min-width: 350px;
  max-width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  text-overflow: ellipsis;
}

.duplicateFolderSelect option {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
