import { GlobalConstants } from '../constants/global-constants';
import axios from './api';

const WORKFLOW_BASE_URL = GlobalConstants.globalURL;

// Create a new approval workflow
export const createWorkflow = async (workflowData) => {
    try {
        const response = await axios.post(`${WORKFLOW_BASE_URL}/approvalworkflow`, workflowData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error creating workflow:', error);
        throw error;
    }
};

// Get all workflows with pagination and search
export const getWorkflows = async (search = '', page = 0, size = 10) => {
    const companyId = localStorage.getItem('companyId');
    const userName = localStorage.getItem('userName');
    try {
        const response = await axios.get(
            `${WORKFLOW_BASE_URL}/approvalworkflow/all_workflow_list/${companyId}/?search=${search}&userName=${userName}&page=${page}&size=${size}`,
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            }
        );
        return response.data;
    } catch (error) {
        console.error('Error fetching workflows:', error);
        
        // Handle 404 specifically - return empty state structure
        if (error.response && error.response.status === 404) {
            return {
                status: 'success',
                data: {
                    content: [],
                    totalElements: 0,
                    totalPages: 0,
                    last: true,
                    first: true,
                    numberOfElements: 0,
                    size: size,
                    number: page
                },
                message: 'No workflows found'
            };
        }
        
        throw error;
    }
};

// Get workflow by ID
export const getWorkflowById = async (workflow_type,workflowId) => {
    try {
        const response = await axios.get(`${WORKFLOW_BASE_URL}/approvalworkflow/workflow-id/${workflow_type}/${workflowId}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching workflow by ID:', error);
        throw error;
    }
};

// Get workflow by ID
export const updateVisibleMode = async (workflowId,visibleMode) => {
    try {
        const response = await axios.get(`${WORKFLOW_BASE_URL}/approvalworkflow/update-visibleMode/${workflowId}/${visibleMode}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error update mode of workflow by ID:', error);
        throw error;
    }
};

// Update workflow
export const updateWorkflow = async (workflowId, workflowData) => {
    try {
        const response = await axios.put(`${WORKFLOW_BASE_URL}/approvalworkflow/update/${workflowId}`, workflowData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error updating workflow:', error);
        throw error;
    }
};

// Delete workflow
export const deleteWorkflow = async (workflowId) => {
    try {
        const response = await axios.delete(`${WORKFLOW_BASE_URL}/approvalworkflow/${workflowId}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error deleting workflow:', error);
        throw error;
    }
};


// Helper function to convert diagram data to workflow API format
export const convertDiagramToWorkflow = (nodes, edges, workflowName, workflowType = 'approve') => {
    const approvalLevels = [];
    // Filter nodes that have assigned users and are not start/end nodes
    const processNodes = nodes.filter(node => 
        node.type !== 'start' && 
        node.type !== 'end' 
    );

    // Sort nodes by position to maintain order
    processNodes.sort((a, b) => a.position.y - b.position.y);

    // Analyze main node output connections to determine root level type
    // Find the main node (node with most outgoing connections)
    let mainNode = null;
    if (processNodes.length > 0) {
      mainNode = processNodes.reduce((main, current) => {
        const mainOutgoing = edges.filter(edge => edge.source === main.id).length;
        const currentOutgoing = edges.filter(edge => edge.source === current.id).length;
        return currentOutgoing > mainOutgoing ? current : main;
      }, processNodes[0]);
    }
    
    const outgoingEdges = mainNode ? edges.filter(edge => edge.source === mainNode.id) : [];
    const rightOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-right');
    const bottomOutputEdges = outgoingEdges.filter(edge => edge.sourceHandle === 'output-bottom');
    
    let rootLevelType = 'serial'; // default
    let rootLevelReason = 'Default serial pattern';
    
    // Determine root level type based on output handle usage
    if (rightOutputEdges.length > 0 && bottomOutputEdges.length === 0) {
      rootLevelType = 'parallel';
      rootLevelReason = `Main node connected via right output (${rightOutputEdges.length} parallel connections)`;
    } else if (bottomOutputEdges.length > 0 && rightOutputEdges.length === 0) {
      rootLevelType = 'serial';
      rootLevelReason = `Main node connected via bottom output (${bottomOutputEdges.length} serial connections)`;
    } else if (rightOutputEdges.length > 0 && bottomOutputEdges.length > 0) {
      // When both outputs are used, determine the primary pattern
      // Business logic: If there are parallel connections, the workflow is primarily parallel
      // because parallel execution is the defining characteristic
      rootLevelType = 'parallel';
      rootLevelReason = `Main node has parallel execution: ${rightOutputEdges.length} parallel + ${bottomOutputEdges.length} serial connections`;
    }

    // Enhanced level tracking system with proper parent-child relationships
    const nodeLevels = new Map();
    const processedNodes = new Set();
    let currentLevel = 1;

    // Helper function to find the parent node of a given node
    const findParentNode = (nodeId) => {
        const incomingEdges = edges.filter(edge => edge.target === nodeId);
        if (incomingEdges.length === 0) return null;
        
        // Return the source node of the first incoming edge
        const parentEdge = incomingEdges[0];
        const parentNode = processNodes.find(n => n.id === parentEdge.source);
        return parentNode;
    };

    // Helper function to get the level info of a parent node
    const getParentLevelInfo = (parentNode) => {
        if (!parentNode) return null;
        return nodeLevels.get(parentNode.id);
    };

    // Helper function to determine connection type from edge
    const getConnectionTypeFromEdge = (sourceNodeId, targetNodeId) => {
        const edge = edges.find(e => e.source === sourceNodeId && e.target === targetNodeId);
        if (!edge) return 'serial';
        
        if (edge.sourceHandle === 'output-right') {
            return 'parallel';
        } else if (edge.sourceHandle === 'output-bottom') {
            return 'serial';
        }
        return 'serial'; // default
    };

    // Recursive function to assign levels based on actual parent-child relationships
    const assignNodeLevel = (node) => {
        if (processedNodes.has(node.id)) {
            return nodeLevels.get(node.id);
        }

        const parentNode = findParentNode(node.id);
        
        if (!parentNode) {
            // This is a root node (no parent)
            const levelInfo = {
                level: `level${currentLevel}`,
                rootLevel: null,
                rootLevelType: null
            };
            nodeLevels.set(node.id, levelInfo);
            processedNodes.add(node.id);
            currentLevel++;
            return levelInfo;
        } else {
            // This node has a parent - ensure parent is processed first
            const parentLevelInfo = assignNodeLevel(parentNode);
            
            // Determine connection type between parent and this node
            const connectionType = getConnectionTypeFromEdge(parentNode.id, node.id);
            
            // Assign level info based on parent relationship
            const levelInfo = {
                level: `level${currentLevel}`,
                rootLevel: parentLevelInfo.level, // Parent's level becomes this node's rootLevel
                rootLevelType: connectionType
            };
            
            nodeLevels.set(node.id, levelInfo);
            processedNodes.add(node.id);
            currentLevel++;
            return levelInfo;
        }
    };

    // Process all nodes to assign proper levels
    processNodes.forEach(node => {
        assignNodeLevel(node);
    });

    processNodes.forEach(node => {
        const nodeLevelInfo = nodeLevels.get(node.id) || {
            level: `level${processNodes.indexOf(node) + 1}`,
            rootLevel: null,
            rootLevelType: null
        };

        // Normalize connection type to be consistent
        const normalizedConnectionType = node.data?.connectionType === 'series' ? 'serial' : (node.data?.connectionType || 'unknown');
        
        if (!node.data.assignedUsers || node.data.assignedUsers.length === 0) {
            approvalLevels.push({
               name: "",
               label: node.data.label,
               usersId: 0,
               connectionType: normalizedConnectionType,
               level: nodeLevelInfo.level,
               rootLevel: nodeLevelInfo.rootLevel,
               rootLevelType: nodeLevelInfo.rootLevelType,
               levelStatus: node.data.levelStatus !== undefined ? node.data.levelStatus : false
            });
        } else {
            node.data.assignedUsers.forEach(user => {
                approvalLevels.push({
                    name: user.name,
                    usersId: user.id,
                    connectionType: normalizedConnectionType,
                    level: nodeLevelInfo.level,
                    rootLevel: nodeLevelInfo.rootLevel,
                    rootLevelType: nodeLevelInfo.rootLevelType,
                    levelStatus: node.data.levelStatus !== undefined ? node.data.levelStatus : false
                });
            });
        }
    });

    const workflowData = {
        name: workflowName,
        type: rootLevelType,
        company: {
            companyId: localStorage.getItem("companyId"),
        },
        approvalLevelDTO: approvalLevels,
        workflow_type: workflowType,
        // Store creator information
        createdBy: localStorage.getItem("userName") || localStorage.getItem("firstName") || "Unknown User",
        createdById: localStorage.getItem("id"),
        // Store diagram data as metadata for better reconstruction
        diagramData: {
            nodes: nodes,
            edges: edges
        }
    };
    
    return workflowData;
};

// Helper function to convert workflow API format to diagram data
export const convertWorkflowToDiagram = (workflowData) => {
    console.log("worfkflowdata",workflowData)
    // If diagram data is stored in the workflow, use it
    if (workflowData.diagramData) {
        return {
            nodes: workflowData.diagramData.nodes,
            edges: workflowData.diagramData.edges
        };
    }

    // Otherwise, create a workflow from approval levels with level information
    const nodes = [];
    const edges = [];
    
    // Add approval nodes
    const approvalLevels = workflowData.approvalLevelDTO || [];
    
    // Group approval levels by level to handle multiple users per level
    const levelGroups = new Map();
    approvalLevels.forEach((level) => {
        const levelKey = level.level || `level${approvalLevels.indexOf(level) + 1}`;
        if (!levelGroups.has(levelKey)) {
            levelGroups.set(levelKey, []);
        }
        levelGroups.get(levelKey).push(level);
    });

    // Create a map to track node positions and connections
    const nodeMap = new Map();
    const levelPositions = new Map();
    
    // First pass: Create nodes and determine positions
    levelGroups.forEach((levels, levelKey) => {
        const nodeId = `approval-${levelKey}`;

        const currentLevel = levels.find(level => level.level === levelKey) || levels[0];
        const docList = currentLevel?.docList;
        //console.log("doc list =======",docList);
        const dtoId = currentLevel?.id;

        //console.log("==========",dtoId)
        
        // Combine all users for this level
        const assignedUsers = levels.map(level => ({
            id: level.usersId.toString(),
            name: level.name,
            email: `${level.name.toLowerCase().replace(' ', '.')}@company.com`
        }));

        // Determine position based on level structure
        let position;
        
        if (levelKey === 'level1') {
            // Root level - center position
            position = { x: 250, y: 150 };
            levelPositions.set(levelKey, position);
        } else {
            // Child levels - position based on rootLevelType
            const rootLevelType = levels[0]?.rootLevelType;
            const rootLevel = levels[0]?.rootLevel;
            
            if (rootLevelType === 'parallel') {
                // Parallel nodes go to the right of their root
                const rootPosition = levelPositions.get(rootLevel) || { x: 250, y: 150 };
                const existingParallelNodes = Array.from(levelPositions.entries())
                    .filter(([key, pos]) => key !== levelKey && pos.x > rootPosition.x)
                    .length;
                
                position = { 
                    x: rootPosition.x + 200, 
                    y: rootPosition.y + (existingParallelNodes * 100) 
                };
            } else {
                // Serial nodes go below their root
                const rootPosition = levelPositions.get(rootLevel) || { x: 250, y: 150 };
                const existingSerialNodes = Array.from(levelPositions.entries())
                    .filter(([key, pos]) => key !== levelKey && pos.y > rootPosition.y)
                    .length;
                
                position = { 
                    x: rootPosition.x, 
                    y: rootPosition.y + 150 + (existingSerialNodes * 100) 
                };
            }
            levelPositions.set(levelKey, position);
        }

        // Find the most common label for this level, or use the first non-empty label
        let nodeLabel = 'Process Node'; // default label
        const labels = levels.map(level => level.label).filter(label => label && label.trim() !== '');
        if (labels.length > 0) {
            // Use the first non-empty label, or the most common one
            nodeLabel = labels[0];
        }

        // Normalize connection type when loading
        const normalizedConnectionType = levels[0]?.connectionType === 'series' ? 'serial' : (levels[0]?.connectionType || 'unknown');
        
        const nodeData = {
            dtoId:dtoId,
            docList:docList[0],
            id: nodeId,
            type: 'process',
            position: position,
            data: {
                label: nodeLabel,
                assignedUsers: assignedUsers,
                connectionType: normalizedConnectionType,
                level: levels[0]?.level || levelKey,
                rootLevel: levels[0]?.rootLevel || null,
                rootLevelType: levels[0]?.rootLevelType || null,
                levelStatus: levels[0]?.levelStatus !== undefined ? levels[0].levelStatus : false // Default to false if not provided
            }
        };
        
        nodes.push(nodeData);
        nodeMap.set(levelKey, nodeId);
    });

    // Second pass: Create edges based on rootLevel relationships
    levelGroups.forEach((levels, levelKey) => {
        if (levelKey === 'level1') {
            // Root level has no incoming edges
            return;
        }
        
        const rootLevel = levels[0]?.rootLevel;
        const rootLevelType = levels[0]?.rootLevelType;
        
        if (rootLevel && nodeMap.has(rootLevel)) {
            const sourceId = nodeMap.get(rootLevel);
            const targetId = nodeMap.get(levelKey);
            
            let sourceHandle = 'output-bottom'; // default serial
            let targetHandle = 'input-top'; // default serial
            
            // If this is a parallel node, connect from right output
            if (rootLevelType === 'parallel') {
                sourceHandle = 'output-right';
                targetHandle = 'input-left';
            }
            
            const edgeData = {
                id: `edge-${sourceId}-${targetId}`,
                source: sourceId,
                target: targetId,
                sourceHandle: sourceHandle,
                targetHandle: targetHandle,
                type: 'default'
            };
            
            edges.push(edgeData);
        }
    });

    return { nodes, edges };
};
