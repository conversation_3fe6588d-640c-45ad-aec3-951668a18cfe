import React, { useState, useMemo, useEffect, useRef } from 'react';

const UserAssignmentPanel = ({ 
  selectedNode, 
  availableUsers = [], 
  onUpdateNode, 
  isVisible = false,
  onClose,
  usersLoading = false,
  onRefreshUsers,
  locked = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setSearchTerm('');
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  // Filter available users based on search and not already assigned
  const filteredUsers = useMemo(() => {
    if (!selectedNode) return [];
    
    const assignedUserIds = selectedNode.data.assignedUsers?.map(u => u.id) || [];
    
    const unassignedUsers = availableUsers.filter(user => !assignedUserIds.includes(user.id));
    
    // If no search term, return all unassigned users
    if (!searchTerm.trim()) {
      return unassignedUsers;
    }
    
    // Filter by search term
    return unassignedUsers.filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [availableUsers, selectedNode, searchTerm]);

  const handleAddUser = (user) => {
    if (!selectedNode || locked) return;
    
    const updatedUsers = [...(selectedNode.data.assignedUsers || []), user];
    onUpdateNode({
      id: selectedNode.id,
      ...selectedNode.data,
      assignedUsers: updatedUsers,
    });
    setSearchTerm('');
    setIsDropdownOpen(false);
  };

  const handleRemoveUser = (userId) => {
    if (!selectedNode || locked) return;
    
    const updatedUsers = selectedNode.data.assignedUsers?.filter(u => u.id !== userId) || [];
    onUpdateNode({
      id: selectedNode.id,
      ...selectedNode.data,
      assignedUsers: updatedUsers,
    });
  };

  const handleLabelChange = (newLabel) => {
    if (!selectedNode || locked) return;
    
    onUpdateNode({
      id: selectedNode.id,
      ...selectedNode.data,
      label: newLabel,
    });
  };

  const handleDescriptionChange = (newDescription) => {
    if (!selectedNode || locked) return;
    
    onUpdateNode({
      id: selectedNode.id,
      ...selectedNode.data,
      description: newDescription,
    });
  };

  const handleLevelStatusChange = (newStatus) => {
    if (!selectedNode || locked) return;
    
    onUpdateNode({
      id: selectedNode.id,
      ...selectedNode.data,
      levelStatus: newStatus,
    });
  };

  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen);
    if (!isDropdownOpen) {
      setSearchTerm(''); // Clear search when opening dropdown
    }
  };

  const panelStyle = {
    position: 'fixed',
    top: 0,
    right: isVisible ? 0 : '-100vw',
    width: isMinimized ? '50px' : '320px',
    height: '100%',
    backgroundColor: 'white',
    borderLeft: '1px solid #ddd',
    boxShadow: '-2px 0 4px rgba(0,0,0,0.1)',
    transition: 'all 0.3s ease',
    zIndex: 1000,
    display: isVisible ? 'flex' : 'none',
    flexDirection: 'column',
    overflow: 'hidden',
  };

  const headerStyle = {
    padding: isMinimized ? '8px 4px' : '16px',
    borderBottom: '1px solid #eee',
    backgroundColor: '#f8f9fa',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const toggleButtonStyle = {
    position: 'absolute',
    bottom: '8px',
    left: '8px',
    width: '28px',
    height: '28px',
    border: '2px solid #007bff',
    borderRadius: '6px',
    backgroundColor: '#007bff',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    color: 'white',
    fontWeight: 'bold',
    transition: 'all 0.2s',
    zIndex: 100,
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  };

  const contentStyle = {
    flex: 1,
    padding: isMinimized ? '8px 4px' : '16px',
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: isMinimized ? '4px' : '6px',
  };

  const sectionStyle = {
    marginBottom: isMinimized ? '2px' : '4px',
  };

  const labelStyle = {
    fontSize: isMinimized ? '11px' : '14px',
    fontWeight: 'bold',
    marginBottom: isMinimized ? '2px' : '4px',
    color: '#333',
  };

  const inputStyle = {
    width: '100%',
    padding: isMinimized ? '6px 8px' : '8px 12px',
    border: '1px solid #ddd',
    borderRadius: '6px',
    fontSize: isMinimized ? '11px' : '14px',
    marginBottom: isMinimized ? '2px' : '4px',
    transition: 'all 0.2s',
  };

  const userChipStyle = {
    display: 'inline-flex',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    border: '1px solid #90caf9',
    borderRadius: '16px',
    padding: isMinimized ? '2px 6px' : '4px 12px',
    margin: isMinimized ? '2px' : '4px',
    fontSize: isMinimized ? '10px' : '12px',
    gap: isMinimized ? '4px' : '6px',
  };

  const removeButtonStyle = {
    background: 'none',
    border: 'none',
    color: '#666',
    cursor: 'pointer',
    padding: '0',
    fontSize: isMinimized ? '10px' : '14px',
    marginLeft: isMinimized ? '2px' : '4px',
    transition: 'all 0.2s',
  };

  const userSuggestionStyle = {
    display: 'flex',
    alignItems: 'center',
    padding: isMinimized ? '6px 8px' : '8px 12px',
    borderBottom: '1px solid #eee',
    cursor: 'pointer',
    gap: isMinimized ? '4px' : '6px',
    transition: 'background-color 0.2s',
  };

  const avatarStyle = {
    width: isMinimized ? '24px' : '32px',
    height: isMinimized ? '24px' : '32px',
    borderRadius: '50%',
    backgroundColor: '#007bff',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: isMinimized ? '10px' : '12px',
    fontWeight: 'bold',
  };

  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!selectedNode) {
    return (
      <div style={{ ...panelStyle, position: 'relative' }}>
        {/* Toggle Button */}
        <button
          style={toggleButtonStyle}
          onClick={() => setIsMinimized(!isMinimized)}
          title={isMinimized ? 'Expand Panel' : 'Minimize Panel'}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = '#0056b3';
            e.target.style.transform = 'scale(1.1)';
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = '#007bff';
            e.target.style.transform = 'scale(1)';
          }}
        >
          {isMinimized ? '▶' : '◀'}
        </button>

        {!isMinimized ? (
          <>
            <div style={headerStyle}>
              <h3>Properties</h3>
              <button onClick={onClose} style={removeButtonStyle}>✕</button>
            </div>
            <div style={contentStyle}>
              <p style={{ textAlign: 'center', color: '#666', marginTop: '40px' }}>
                Select a node to edit its properties
              </p>
            </div>
          </>
        ) : (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            padding: '8px 4px',
            textAlign: 'center',
            gap: '6px',
          }}>
            <div style={{
              fontSize: '11px',
              color: '#333',
              fontWeight: 'bold',
            }}>
              Props
            </div>
            <div style={{
              fontSize: '9px',
              color: '#666',
              lineHeight: '1.2',
            }}>
              Select<br/>a node
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div style={{ ...panelStyle, position: 'relative' }}>
      {/* Toggle Button */}
      <button
        style={toggleButtonStyle}
        onClick={() => setIsMinimized(!isMinimized)}
        title={isMinimized ? 'Expand Panel' : 'Minimize Panel'}
        onMouseOver={(e) => {
          e.target.style.backgroundColor = '#0056b3';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseOut={(e) => {
          e.target.style.backgroundColor = '#007bff';
          e.target.style.transform = 'scale(1)';
        }}
      >
        {isMinimized ?  '◀' : '▶'}
      </button>

      {!isMinimized ? (
        <>
          <div className='text-center' style={headerStyle}>
            <h5>Node Properties</h5>
            <button onClick={onClose} style={removeButtonStyle}>✕</button>
          </div>
          
          <div style={contentStyle}>
        {/* Node Label */}
        <div style={sectionStyle}>
          <label style={labelStyle}>Label</label>
          <input
            type="text"
            style={{
              ...inputStyle,
              backgroundColor: locked ? '#f5f5f5' : 'white',
              cursor: locked ? 'not-allowed' : 'text',
              opacity: locked ? 0.7 : 1
            }}
            value={selectedNode.data.label || ''}
            onChange={(e) => !locked && handleLabelChange(e.target.value)}
            placeholder={locked ? "Read-only mode" : "Enter node label"}
            disabled={locked}
            onFocus={(e) => {
              if (!locked) {
                e.target.style.borderColor = '#007bff';
                e.target.style.boxShadow = '0 0 0 2px rgba(0,123,255,0.25)';
              }
            }}
            onBlur={(e) => {
              if (!locked) {
                e.target.style.borderColor = '#ddd';
                e.target.style.boxShadow = 'none';
              }
            }}
          />
        </div>

        {/* Node Description */}
        <div style={sectionStyle}>
          <label style={labelStyle}>Description</label>
          <textarea
            style={{
              ...inputStyle,
              minHeight: '60px',
              resize: 'vertical',
              backgroundColor: locked ? '#f5f5f5' : 'white',
              cursor: locked ? 'not-allowed' : 'text',
              opacity: locked ? 0.7 : 1
            }}
            value={selectedNode.data.description || ''}
            onChange={(e) => !locked && handleDescriptionChange(e.target.value)}
            placeholder={locked ? "Read-only mode" : "Enter node description"}
            disabled={locked}
            onFocus={(e) => {
              if (!locked) {
                e.target.style.borderColor = '#007bff';
                e.target.style.boxShadow = '0 0 0 2px rgba(0,123,255,0.25)';
              }
            }}
            onBlur={(e) => {
              if (!locked) {
                e.target.style.borderColor = '#ddd';
                e.target.style.boxShadow = 'none';
              }
            }}
          />
        </div>

        {/* Level Status */}
        <div style={sectionStyle}>
          <label style={labelStyle}>Level Status</label>
          <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>
            <button
              style={{
                padding: '8px 16px',
                border: selectedNode.data.levelStatus === true ? '2px solid #4caf50' : '1px solid #ddd',
                borderRadius: '6px',
                backgroundColor: selectedNode.data.levelStatus === true ? '#e8f5e8' : '#f8f9fa',
                color: selectedNode.data.levelStatus === true ? '#1b5e20' : '#666',
                cursor: locked ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: selectedNode.data.levelStatus === true ? 'bold' : 'normal',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                opacity: locked ? 0.7 : 1
              }}
              onClick={() => !locked && handleLevelStatusChange(true)}
              disabled={locked}
              onMouseOver={(e) => {
                if (!locked && selectedNode.data.levelStatus !== true) {
                  e.target.style.backgroundColor = '#e8f5e8';
                  e.target.style.borderColor = '#4caf50';
                }
              }}
              onMouseOut={(e) => {
                if (!locked && selectedNode.data.levelStatus !== true) {
                  e.target.style.backgroundColor = '#f8f9fa';
                  e.target.style.borderColor = '#ddd';
                }
              }}
            >
              <span style={{ fontSize: '16px' }}>✓</span>
              Completed
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: selectedNode.data.levelStatus === false ? '2px solid #f44336' : '1px solid #ddd',
                borderRadius: '6px',
                backgroundColor: selectedNode.data.levelStatus === false ? '#ffebee' : '#f8f9fa',
                color: selectedNode.data.levelStatus === false ? '#c62828' : '#666',
                cursor: locked ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: selectedNode.data.levelStatus === false ? 'bold' : 'normal',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                opacity: locked ? 0.7 : 1
              }}
              onClick={() => !locked && handleLevelStatusChange(false)}
              disabled={locked}
              onMouseOver={(e) => {
                if (!locked && selectedNode.data.levelStatus !== false) {
                  e.target.style.backgroundColor = '#ffebee';
                  e.target.style.borderColor = '#f44336';
                }
              }}
              onMouseOut={(e) => {
                if (!locked && selectedNode.data.levelStatus !== false) {
                  e.target.style.backgroundColor = '#f8f9fa';
                  e.target.style.borderColor = '#ddd';
                }
              }}
            >
              <span style={{ fontSize: '16px' }}>✗</span>
              Pending
            </button>
          </div>
          <div style={{ 
            fontSize: '12px', 
            color: '#666', 
            marginTop: '6px',
            fontStyle: 'italic'
          }}>
            Current status: {
              selectedNode.data.levelStatus === true ? 'Completed (Green)' :
              selectedNode.data.levelStatus === false ? 'Pending (Red)' :
              'Not set (Default colors)'
            }
            {locked && (
              <span style={{ color: '#ff9800', marginLeft: '8px' }}>
                (Read-only mode)
              </span>
            )}
          </div>
        </div>

        {/* Assigned Users */}
        <div style={sectionStyle}>
          <label style={labelStyle}>Assigned Users</label>
          {/* Current assigned users */}
              <div style={{ marginBottom: '12px' }}>
                {selectedNode.data.assignedUsers?.length > 0 ? (
                  selectedNode.data.assignedUsers.map(user => (
                    <div key={user.id} style={userChipStyle}>
                      <div style={{ ...avatarStyle, width: '20px', height: '20px', fontSize: '10px' }}>
                        {user.avatar ? (
                          <img 
                            src={user.avatar} 
                            alt={user.name}
                            style={{ width: '100%', height: '100%', borderRadius: '50%' }}
                          />
                        ) : (
                          getInitials(user.name)
                        )}
                      </div>
                      {user.name}
                      {!locked && (
                        <button
                          style={{
                            ...removeButtonStyle,
                            cursor: locked ? 'not-allowed' : 'pointer',
                            opacity: locked ? 0.5 : 1
                          }}
                          onClick={() => !locked && handleRemoveUser(user.id)}
                          title={locked ? "Cannot remove users in read-only mode" : "Remove user"}
                          disabled={locked}
                        >
                          ✕
                        </button>
                      )}
                    </div>
                  ))
                ) : (
                  <p style={{ color: '#666', fontSize: '14px', fontStyle: 'italic' }}>
                    No users assigned
                  </p>
                )}
              </div>

             {/* Add user dropdown with search */}
             <div ref={dropdownRef} style={{ position: 'relative' }}>
                <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>
                  <button
                    type="button"
                    style={{
                      ...inputStyle,
                      flex: 1,
                      marginBottom: 0,
                      textAlign: 'left',
                      backgroundColor: 'white',
                      cursor: 'pointer',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                    onClick={handleDropdownToggle}
                    disabled={usersLoading}
                  >
                    <span style={{ color: '#666' }}>
                      {usersLoading ? 'Loading users...' : 'Select user to add...'}
                    </span>
                    <span style={{ color: '#666' }}>
                      {isDropdownOpen ? '▲' : '▼'}
                    </span>
                  </button>
                  
                  {onRefreshUsers && (
                    <button
                      style={{
                        padding: '8px 12px',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        backgroundColor: '#f8f9fa',
                        cursor: 'pointer',
                        fontSize: '14px',
                      }}
                      onClick={onRefreshUsers}
                      title="Refresh users list"
                      disabled={usersLoading}
                    >
                      {usersLoading ? '⟳' : '↻'}
                    </button>
                  )}
                </div>

                {/* Dropdown content */}
                {isDropdownOpen && (
                  <div style={{
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    right: 0,
                    zIndex: 1000,
                    backgroundColor: 'white',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    maxHeight: '250px',
                    marginTop: '4px',
                  }}>
                    {/* Search input inside dropdown */}
                    <div style={{ padding: '8px', borderBottom: '1px solid #eee' }}>
                      <input
                        type="text"
                        style={{
                          width: '100%',
                          padding: '6px 8px',
                          border: '1px solid #ddd',
                          borderRadius: '4px',
                          fontSize: '14px',
                        }}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Search users..."
                        autoFocus
                      />
                    </div>

                  {/* Users list */}
                  <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                      {usersLoading ? (
                        <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>
                          <div style={{ marginBottom: '4px' }}>⟳</div>
                          Loading users...
                        </div>
                      ) : filteredUsers.length > 0 ? (
                        filteredUsers.map(user => (
                          <div
                            key={user.id}
                            style={userSuggestionStyle}
                            onClick={() => handleAddUser(user)}
                            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                          >
                            <div style={avatarStyle}>
                              {user.avatar ? (
                                <img 
                                  src={user.avatar} 
                                  alt={user.name}
                                  style={{ width: '100%', height: '100%', borderRadius: '50%' }}
                                />
                              ) : (
                                getInitials(user.name)
                              )}
                            </div>
                            <div>
                              <div style={{ fontSize: '14px', fontWeight: '500' }}>{user.name}</div>
                              <div style={{ fontSize: '12px', color: '#666' }}>{user.email}</div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>
                          {availableUsers.length === 0 ? 'No users available' : 'No users found'}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
        </div>
          </div>
        </>
              ) : (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            padding: '8px 4px',
            textAlign: 'center',
            gap: '6px',
          }}>
            <div style={{
              fontSize: '11px',
              color: '#333',
              fontWeight: 'bold',
            }}>
              Props
            </div>
            <div style={{
              fontSize: '9px',
              color: '#666',
              lineHeight: '1.2',
            }}>
              Click ▶<br/>to expand
            </div>
          </div>
        )}
    </div>
  );
};

export default UserAssignmentPanel;
