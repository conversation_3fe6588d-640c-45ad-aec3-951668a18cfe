import React, { useState, useEffect } from "react";
import "./NewCompany.css";
import { getList, addNew } from "../../../services/apiService";
import "font-awesome/css/font-awesome.min.css";
import { Card, Row, Col, Button, Modal } from "react-bootstrap";
import Logo from "../../images/logo1.png";
import { DataTable } from "../../Table/DataTable";
import Notification from "../../Notification/Notification";
import { useNavigate } from "react-router-dom";

function NewCompany() {
  const navigate = useNavigate();
  const [viewType, setViewType] = useState("tile");
  const [companies, setCompanies] = useState([]);
  const [createCompanyModalOpen, setCreateCompanyModalOpen] = useState(false);
  const [newCompany, setNewCompany] = useState({
    companyName: "",
    companyAliasName: "",
    logo: "",
    phone: "",
    website: "",
    address: "",
    receivingMail: "",
    password: "",
  });
  const [notification, setNotification] = useState({
    message: "",
    type: "",
    show: false,
  });
  const [aliasNameExists, setAliasNameExists] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isCompanyExist, setIsCompanyExist] = useState(false);

  useEffect(() => {
    fetchCompanies();
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  const fetchCompanies = async () => {
    const api = "/company/list?page=0&size=20&searchParam=";
    try {
      const response = await getList(api);
      setCompanies(response.data.content);
    } catch (err) {
      console.log(err);
    }
  };

  const handlePopState = (event) => {
    navigate("/new-company", { replace: true });
  };

  const logout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("userName");
    localStorage.removeItem("firstName");
    localStorage.removeItem("id");
    localStorage.removeItem("role");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    navigate("/", { replace: true });
  };

  const click = (company) => {
    console.log(company);
    navigate("/newDS/workflows", { replace: true });
    localStorage.setItem("companyId", company.companyId);
    localStorage.setItem("companyName", company.companyName);
  };

  const openModal = () => {
    setCreateCompanyModalOpen(true);
  };

  const closeModal = () => {
    setCreateCompanyModalOpen(false);
    setNewCompany({
      companyName: "",
      companyAliasName: "",
      logo: "",
      phone: "",
      website: "",
      address: "",
      receivingMail: "",
      password: "",
    });
    setIsCompanyExist(false);
    setAliasNameExists("");
  };

  const handleInputChange = (e) => {
    setAliasNameExists("");
    const { name, value } = e.target;
    setNewCompany(prev => ({ ...prev, [name]: value }));
  };

  const handleCompanyNameChange = (e) => {
    const companyValue = e.target.value;
    setNewCompany(prev => ({ ...prev, companyName: companyValue }));
    setIsCompanyExist(false);
    check(companyValue);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const closeNotification = () => {
    setNotification(prev => ({ ...prev, show: false }));
  };

  const check = async (companyName) => {
    console.log(companyName);
    const api = `/company/validateCompany?companyName=${companyName}`;
    try {
      const response = await getList(api);
      console.log(response);
      setIsCompanyExist(response);
    } catch (error) {}
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await createCompany(newCompany);
      fetchCompanies();
    } catch (error) {
      console.log(error);
    }
  };

  const createCompany = async (companyData) => {
    const aliasName = companyData.companyAliasName;
    if (aliasName) {
      const exists = companies.some(
        i => i.companyAliasName && i.companyAliasName.toLowerCase() === aliasName.toLowerCase()
      );
      if (exists) {
        setAliasNameExists("Alias Name already exists in DB");
        return;
      }
    }

    console.log("=======", companyData);

    const api = "/company";
    try {
      const response = await addNew(api, companyData);
      setNotification({
        message: response.message,
        type: "success",
        show: true,
      });
      return response.data;
    } catch (error) {
      setNotification({
        message: "Something went wrong",
        type: "error",
        show: true,
      });
      throw error;
    } finally {
      closeModal();
    }
  };

  return (
    <div>
      {notification.show && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={closeNotification}
        />
      )}
      <div id="layoutAuthentication">
        <div id="layoutAuthentication_content">
          <main>
            <div className="container">
              <div className="row justify-content-center">
                <div className="col-lg-12">
                  <div className="card shadow-lg border-0 rounded-lg card-container">
                    <div className="card-header p-2">
                      <div className="row">
                        <div className="col-12 d-flex align-items-center justify-content-lg-between ">
                          <img
                            src={Logo}
                            className="img-fluid me-3"
                            alt="Logo"
                            style={{ height: 40 }}
                          />
                          <a
                            className="logoutBtn btn btnSm  btn-primary ms-auto"
                            onClick={openModal}
                          >
                            New Company
                          </a>
                          <a
                            className="logoutBtn btn btnSm ms-auto"
                            style={{
                              backgroundColor: "maroon",
                              color: "white",
                            }}
                            onClick={logout}
                          >
                            <i className="fa fa-power-off"></i> Logout
                          </a>
                        </div>
                      </div>
                    </div>

                    <div className="card-body p-2 mt-2">
                      <div className="companies w-100">
                        <div className="d-flex justify-content-between align-items-center mb-1 w-100">
                          <div className="d-flex align-items-center justify-content-between gap-1 w-100">
                            <h4 className="m-0">Select your Company</h4>
                            <div style={{ display: "flex", gap: "4px" }}>
                              <button
                                className={`btn btn-sm ${
                                  viewType === "tile"
                                    ? "btn-primary"
                                    : "btn-outline-primary"
                                }`}
                                onClick={() => setViewType("tile")}
                                title="Tile View"
                              >
                                <i className="fa fa-th-large"></i>
                              </button>
                              <button
                                className={`btn btn-sm ${
                                  viewType === "list"
                                    ? "btn-primary"
                                    : "btn-outline-primary"
                                }`}
                                onClick={() => setViewType("list")}
                                title="List View"
                              >
                                <i className="fa fa-list"></i>
                              </button>
                              <button
                                className={`btn btn-sm ${
                                  viewType === "table"
                                    ? "btn-primary"
                                    : "btn-outline-primary"
                                }`}
                                onClick={() => setViewType("table")}
                                title="Table View"
                              >
                                <i className="fa fa-table"></i>
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Tile View */}
                        {viewType === "tile" && (
                          <Row className="g-2 mt-1">
                            {companies.map((company, index) => (
                              <Col
                                key={index}
                                xs={12}
                                md={4}
                                lg={4}
                                className="d-flex align-items-stretch"
                              >
                                <Card
                                  className="company-tile position-relative w-100 border-0 shadow-sm p-4"
                                  onClick={() => click(company)}
                                  style={{
                                    backgroundColor: "#fafafa",
                                    borderRadius: "8px",
                                  }}
                                >
                                  <div className="overlay d-flex justify-content-center align-items-center">
                                    {/* Eye icon removed as requested */}
                                  </div>
                                  <Card.Body className="d-flex flex-row align-items-center justify-content-start p-2 w-100">
                                    <div
                                      className={`company-avatar me-2 bg-color-${
                                        index % 8
                                      }`}
                                      style={{
                                        minWidth: "40px",
                                        height: "40px",
                                        fontSize: "1.5rem",
                                      }}
                                    >
                                      <span
                                        className="avatar-text"
                                        style={{ fontSize: "1.5rem" }}
                                      >
                                        {company.companyName
                                          ?.charAt(0)
                                          .toUpperCase()}
                                      </span>
                                    </div>
                                    <div>
                                      <span className="card-title text-start fw-bold mb-0">
                                        {company.companyName}
                                      </span>
                                    </div>
                                  </Card.Body>
                                </Card>
                              </Col>
                            ))}
                          </Row>
                        )}

                        {/* List View */}
                        {viewType === "list" && (
                          <div
                            style={{
                              height: "calc(100vh - 220px)",
                              overflow: "auto",
                              overflowX: "hidden",
                              width: "100%",
                            }}
                          >
                            <ul className="list-group">
                              {companies.map((company, index) => (
                                <li
                                  key={index}
                                  className="list-group-item py-2"
                                  style={{ cursor: "pointer" }}
                                  onClick={() => click(company)}
                                  onMouseOver={(e) =>
                                    (e.currentTarget.style.backgroundColor =
                                      "#8fc7e6")
                                  }
                                  onMouseOut={(e) =>
                                    (e.currentTarget.style.backgroundColor = "")
                                  }
                                >
                                  {company.companyName}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Table View */}
                        {viewType === "table" && (
                          <div
                            style={{
                              height: "calc(100vh - 220px)",
                              overflowY: "auto",
                            }}
                          >
                            <DataTable
                              data={companies}
                              columns={[
                                {
                                  key: "companyName",
                                  header: "Company Name",
                                  sortable: true,
                                  render: (value, row) => (
                                    <span
                                      style={{ cursor: "pointer" }}
                                      onClick={() => click(row)}
                                    >
                                      {value}
                                    </span>
                                  ),
                                },
                              ]}
                              showSno={true}
                              searchable={false}
                              itemsPerPage={companies.length}
                              className="mb-0"
                              rowClassName="py-1"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
        <div id="layoutAuthentication_footer">
          <footer className="py-2 bg-light">
            <div className="container-fluid px-4">
              <div className="d-flex align-items-center justify-content-between small">
                <div className="text-muted">Copyright &copy; DMS 2024</div>
                <div>
                  <a>Privacy Policy</a>
                  &middot;
                  <a>Terms &amp; Conditions</a>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
      <Modal
        show={createCompanyModalOpen}
        onHide={closeModal}
        size="lg"
      >
        <Modal.Header closeButton className="modal-header-modern">
          <Modal.Title>New Company</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <form name="companyCreate" onSubmit={handleSubmit}>
            <div className="row">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="companyName"
              >
                Company Name :
              </label>
              <div className="col-sm-7">
                <input
                  type="text"
                  name="companyName"
                  id="companyName"
                  value={newCompany.companyName}
                  className="form-control"
                  placeholder="Enter Company Name"
                  required
                  onChange={handleCompanyNameChange}
                />
                {isCompanyExist && (
                  <span style={{ color: "red" }}>
                    Company Name already exists in DB
                  </span>
                )}
              </div>
            </div>

            <div className="row">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="companyAliasName"
              >
                Alias Name :
              </label>
              <div className="col-sm-7">
                <input
                  type="text"
                  name="companyAliasName"
                  id="companyAliasName"
                  value={newCompany.companyAliasName}
                  className="form-control"
                  placeholder="Enter Alias Name"
                  required
                  onChange={handleInputChange}
                />
                {aliasNameExists && (
                  <p className="text-danger">{aliasNameExists}</p>
                )}
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="logo"
              >
                Logo
              </label>
              <div className="col-sm-7">
                <input
                  type="file"
                  id="logo"
                  name="logo"
                  value={newCompany.logo}
                  className="form-control"
                  placeholder=""
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="phone"
              >
                Phone :
              </label>
              <div className="col-sm-7">
                <input
                  type="text"
                  name="phone"
                  id="phone"
                  value={newCompany.phone}
                  className="form-control"
                  placeholder="Enter Phone No."
                  required
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="receivingMail"
              >
                Receiving email :
              </label>
              <div className="col-sm-7">
                <input
                  type="email"
                  name="receivingMail"
                  id="receivingMail"
                  value={newCompany.receivingMail}
                  className="form-control"
                  placeholder="Enter Receiving email"
                  required
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="password"
              >
                Password :
              </label>
              <div className="col-sm-7">
                <div className="input-group">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    id="password"
                    value={newCompany.password}
                    className="form-control"
                    placeholder="Enter password"
                    required
                    onChange={handleInputChange}
                  />
                  <div className="input-group-append">
                    <span
                      className="btn btn-outline-gray"
                      type="button"
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <i className="fa fa-eye-slash" title="hide"></i>
                      ) : (
                        <i className="fa fa-eye" title="show"></i>
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="website"
              >
                Website :
              </label>
              <div className="col-sm-7">
                <input
                  type="text"
                  id="website"
                  name="website"
                  value={newCompany.website}
                  className="form-control"
                  placeholder="Enter Website URL"
                  required
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="row mt-2">
              <label
                className="col-sm-3 offset-sm-1 col-form-label"
                htmlFor="address"
              >
                Address :
              </label>
              <div className="col-sm-7">
                <textarea
                  type="text"
                  id="address"
                  name="address"
                  value={newCompany.address}
                  className="form-control"
                  placeholder="Enter Company Address"
                  required
                  rows="3"
                  onChange={handleInputChange}
                ></textarea>
              </div>
            </div>

            <div className="row m-3">
              <div className="col-12 text-end">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={isCompanyExist}
                >
                  Submit
                </Button>
              </div>
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </div>
  );
}

export default NewCompany;