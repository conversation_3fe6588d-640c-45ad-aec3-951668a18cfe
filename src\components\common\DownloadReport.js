import React, { Component } from "react";
import PropTypes from "prop-types";
import axios from "../../services/api";
import Loader from "../loader/Loader";
import { FaFilePdf } from "react-icons/fa6";


class DownloadReport extends Component {
  state = {
    isLoading: false,
  };

  exportToExcel = async (event) => {
    event.preventDefault();
    this.setState({ isLoading: true });

    try {
      let response;
      if (this.props.filter && Object.keys(this.props.filter).length > 0) {
        // Use POST with filter if filter exists
        response = await axios.post(
          this.props.excelEndpoint,
          this.props.filter,
          {
            responseType: "blob",
            headers: {
              Accept:
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            },
          }
        );
      } else {
        // Use GET if no filter
        response = await axios.get(this.props.excelEndpoint, {
          responseType: "blob",
          headers: {
            Accept:
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          },
        });
      }

      if (response.data.size === 0) {
        throw new Error("Received an empty response from the server.");
      }

      const contentType =
        response.headers["content-type"] ||
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      const blob = new Blob([response.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${this.props.reportName}.xls`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      this.setState({ isLoading: false });

      if (this.props.onDownloadComplete) {
        this.props.onDownloadComplete("excel");
      }
    } catch (error) {
      console.error("Error downloading Excel:", error.message);
      this.setState({ isLoading: false });

      if (this.props.onError) {
        this.props.onError("Something went wrong while downloading Excel");
      }
    }
  };

  exportToPDF = async (event) => {
    event.preventDefault();
    this.setState({ isLoading: true });

    try {
      let response;
      if (this.props.filter && Object.keys(this.props.filter).length > 0) {
        // Use POST with filter if filter exists
        response = await axios.post(this.props.pdfEndpoint, this.props.filter, {
          responseType: "blob",
          headers: {
            Accept: "application/pdf",
          },
        });
      } else {
        // Use GET if no filter
        response = await axios.get(this.props.pdfEndpoint, {
          responseType: "blob",
          headers: {
            Accept: "application/pdf",
          },
        });
      }

      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${this.props.reportName}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      this.setState({ isLoading: false });

      if (this.props.onDownloadComplete) {
        this.props.onDownloadComplete("pdf");
      }
    } catch (error) {
      console.error("Error downloading PDF:", error);
      this.setState({ isLoading: false });

      if (this.props.onError) {
        this.props.onError("Something went wrong while downloading PDF");
      }
    }
  };

  render() {
    const { buttonClassName, buttonStyle } = this.props;

    return (
      <>
        <button
          type="button"
          className={buttonClassName || "btn bg-none boder-none"}
          style={buttonStyle || { fontSize: "14px" }}
        >
          <a
            href="#"
            onClick={this.exportToExcel}
            style={{ color: "white", marginLeft: "5px" }}
            title="Download Excel"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              x="0px"
              y="0px"
              width="24"
              height="24"
              viewBox="0 0 48 48"
            >
              <rect width="16" height="9" x="28" y="15" fill="#21a366"></rect>
              <path
                fill="#185c37"
                d="M44,24H12v16c0,1.105,0.895,2,2,2h28c1.105,0,2-0.895,2-2V24z"
              ></path>
              <rect width="16" height="9" x="28" y="24" fill="#107c42"></rect>
              <rect width="16" height="9" x="12" y="15" fill="#3fa071"></rect>
              <path
                fill="#33c481"
                d="M42,6H28v9h16V8C44,6.895,43.105,6,42,6z"
              ></path>
              <path
                fill="#21a366"
                d="M14,6h14v9H12V8C12,6.895,12.895,6,14,6z"
              ></path>
              <path
                d="M22.319,13H12v24h10.319C24.352,37,26,35.352,26,33.319V16.681C26,14.648,24.352,13,22.319,13z"
                opacity=".05"
              ></path>
              <path
                d="M22.213,36H12V13.333h10.213c1.724,0,3.121,1.397,3.121,3.121v16.425	C25.333,34.603,23.936,36,22.213,36z"
                opacity=".07"
              ></path>
              <path
                d="M22.106,35H12V13.667h10.106c1.414,0,2.56,1.146,2.56,2.56V32.44C24.667,33.854,23.52,35,22.106,35z"
                opacity=".09"
              ></path>
              <linearGradient
                id="flEJnwg7q~uKUdkX0KCyBa_UECmBSgBOvPT_gr1"
                x1="4.725"
                x2="23.055"
                y1="14.725"
                y2="33.055"
                gradientUnits="userSpaceOnUse"
              >
                <stop offset="0" stopColor="#18884f"></stop>
                <stop offset="1" stopColor="#0b6731"></stop>
              </linearGradient>
              <path
                fill="url(#flEJnwg7q~uKUdkX0KCyBa_UECmBSgBOvPT_gr1)"
                d="M22,34H6c-1.105,0-2-0.895-2-2V16c0-1.105,0.895-2,2-2h16c1.105,0,2,0.895,2,2v16	C24,33.105,23.105,34,22,34z"
              ></path>
              <path
                fill="#fff"
                d="M9.807,19h2.386l1.936,3.754L16.175,19h2.229l-3.071,5l3.141,5h-2.351l-2.11-3.93L11.912,29H9.526	l3.193-5.018L9.807,19z"
              ></path>
            </svg>
          </a>
          /
          <a
            href="#"
            onClick={this.exportToPDF}
            style={{ color: "white", marginLeft: "5px" }}
            title="Download PDF"
          >
            <FaFilePdf fill="red" height={"24"} width={"24"}/>
          </a>
        </button>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

DownloadReport.propTypes = {
  excelEndpoint: PropTypes.string.isRequired,
  pdfEndpoint: PropTypes.string.isRequired,
  reportName: PropTypes.string.isRequired,
  filter: PropTypes.object,
  buttonClassName: PropTypes.string,
  buttonStyle: PropTypes.object,
  onDownloadComplete: PropTypes.func,
  onError: PropTypes.func,
};

export default DownloadReport;
