import React, { useState, useEffect, useRef } from "react";
import classes from "./Login.module.css";
import logo from "../images/logo1.png";
import loginImage from '../images/loginImage.png';
import { useNavigate } from "react-router";
import { authenticate } from "../../services/authentication";
import { Button, Container, Form, Row, Col } from "react-bootstrap";
import Notification from "../Notification/Notification";
import Loader from "../loader/Loader";

function Login(props) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
  });
  const [role, setRole] = useState("");
  const [showEyeIcon, setShowEyeIcon] = useState(false);
  const [usernameFocused, setUsernameFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [notification, setNotification] = useState({
    message: "",
    type: "",
    show: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const [deployedURL, setDeployedURL] = useState("");

  const usernameRef = useRef(null);

  useEffect(() => {
    const deployedURL = window.location.href.split('/#')[0];
    setDeployedURL(deployedURL);
    localStorage.removeItem("token");
    localStorage.removeItem("type");
    localStorage.removeItem("userName");
    localStorage.removeItem("firstName");
    localStorage.removeItem("id");
    localStorage.removeItem("role");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    if (usernameRef.current) {
      usernameRef.current.focus();
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCredentials((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const userObject = {
      userName: credentials.username,
      password: credentials.password,
    };
    //console.log(userObject);
    try {
      const userData = await authenticate(userObject);
      // alert('User Details: ', userData);
      localStorage.setItem("token", userData.token);
      localStorage.setItem("type", "");
      localStorage.setItem("userName", userData.userName);
      localStorage.setItem("firstName", userData.firstName);
      localStorage.setItem("id", userData.id);
      localStorage.setItem("role", "" + userData.roles[0]);
      const storedRole = localStorage.getItem("role");
      setRole(storedRole);
      setIsLoggedIn(true);
      setIsLoading(false);
      if (storedRole === "ROLE_SUPER_ADMIN") {
        navigate("new-company", { replace: true });
      } else {
        localStorage.setItem("companyId", userData.company.companyId);
        localStorage.setItem("companyName", userData.company.companyName);
        navigate("/newDS/document-management", { replace: true });
      }
    } catch (error) {
      //alert(error);
      //console.log('Error:', error.response.data.message);
      setNotification({
        message: error.response.data.message,
        type: "error",
        show: true,
      });
      //console.log('Notification state:', notification);
      setIsLoading(false);
    }
  };

  const closeNotification = () => {
    setNotification({ message: "", type: "", show: false });
  };
  const forgotHandler = () => {
    navigate("forgot");
  };

  return (
    <Container fluid className="p-0 d-flex align-items-center">
      <Row className="w-100 m-0">
        {/* Left side */}
        <Col md={6} className={`${classes["left-side"]}`}>
          <div className={classes["logo-container"]}>
            <img src={logo} alt="Datadot Logo" style={{ width: "150px" }} />
          </div>

          <div className="d-flex flex-column align-items-center justify-content-center h-100">
            <h1 className={`${classes["heading"]}`}>
              <i className="fa fa-user me-2" title="User Login"></i>
              Welcome - Login Here
            </h1>
            <p className={`${classes["sub-heading"]}`}>Login to get your data information</p>
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3" controlId="formBasicEmail">
                <Form.Label>Username:</Form.Label>
                <Form.Control
                  type="text"
                  name="username"
                  value={credentials.username}
                  onChange={handleChange}
                  placeholder="Enter username"
                  className={`${classes['input-email']}`}
                />
              </Form.Group>
              <Form.Group className="mb-3" controlId="formBasicPassword">
                <Form.Label>Password:</Form.Label>
                <div className="input-group">
                  <Form.Control
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={credentials.password}
                    onChange={handleChange}
                    placeholder="Enter password"
                  />
                  <button
                    type="button"
                    className="btn btn-outline-gray"
                    onClick={() => setShowPassword((prev) => !prev)}
                    tabIndex={-1}
                  >
                    {showPassword ? (
                      <i className="fa fa-eye-slash" title="Hide password"></i>
                    ) : (
                      <i className="fa fa-eye" title="Show password"></i>
                    )}
                  </button>
                </div>
              </Form.Group>
              <div className="mb-3 d-flex justify-content-end">
                <a href={`${deployedURL}#/forgot`}>Forgot password?</a>
              </div>
              <Button variant="primary" type="submit" className={`${classes['login-button']}`}>
                LOGIN
              </Button>
            </Form>
          </div>
        </Col>

        {/* Right side */}
        <Col
          md={6}
          className={`${classes["bg-gradient-custom"]} d-flex align-items-center justify-content-center`}
        >
          <div className="text-center px-4">
            <h4 className={`${classes['right-heading']}`}>Manage All Your</h4>
            <h2 className="fw-bold">Documents & Folders</h2>
            <img
                src={loginImage}
                alt="Document illustration"
                className="img-fluid mt-4"
                style={{ maxHeight: "400px" }}
              />
          </div>
        </Col>
      </Row>
      {notification.show && <Notification message={notification.message} type={notification.type} onClose={closeNotification}/>}
    </Container>
  );
}

export default Login;
