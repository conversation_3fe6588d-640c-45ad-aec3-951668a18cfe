import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Modal, Button } from 'react-bootstrap';
import { DataTable } from '../Table/DataTable';
import CustomBreadcrumb from '../common/CustomBreadcrumb';
import Notification from '../Notification/Notification';
import { deleteById, editById, formatDate, getList } from '../../services/apiService';
import Loader from "../loader/Loader"
import { availableWidgets } from '../../config/widgetConfig';
import Select from 'react-select';

function DashboardManagement() {
  const navigate = useNavigate();
  const [dashboards, setDashboards] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(10);

  // Get current user role and ID
  const currentUserRole = localStorage.getItem("role");
  const currentUserId = parseInt(localStorage.getItem("id"));
  const isAdmin = currentUserRole === "ROLE_SUPER_ADMIN";

  // Modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDefaultModal, setShowDefaultModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCopyModal, setShowCopyModal] = useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState(null);

  // Form states
  const [editFormData, setEditFormData] = useState({
    id: '',
    name: '',
    description: '',
    assignedUsers:[],
  });

  const [copyFormData, setCopyFormData] = useState({
    name: '',
    description: ''
  });


  const [userList, setUserList] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  
  // Reports list
  const [reportList] = useState([
    'Total Files', 'Duplicate Files', 'Pending Workflows Files', 
    'Due Date Files', 'Archive Files', 'Retention End Files'
  ]);

  // Notification state
  const [notification, setNotification] = useState({
    message: '',
    type: '',
    show: false
  });

  // Mock data for demonstration - in real app, this would come from API
  const [mockDashboards] = useState([
    // {
    //   id: 1,
    //   name: 'Sales Dashboard',
    //   description: 'Dashboard showing sales metrics and performance',
    //   widgets: ['bar-chart', 'pie-chart', 'stats-card'],
    //   createdBy: 'John Doe',
    //   createdDate: '2024-01-15T10:30:00Z',
    //   lastModified: '2024-01-20T14:45:00Z',
    //   status: 'Active'
    // },
    // {
    //   id: 2,
    //   name: 'Analytics Overview',
    //   description: 'Comprehensive analytics dashboard with multiple widgets',
    //   widgets: ['line-chart', 'table', 'stats-card', 'bar-chart'],
    //   createdBy: 'Jane Smith',
    //   createdDate: '2024-01-10T09:15:00Z',
    //   lastModified: '2024-01-18T16:20:00Z',
    //   status: 'Active'
    // },
    // {
    //   id: 3,
    //   name: 'Performance Metrics',
    //   description: 'Key performance indicators and metrics tracking',
    //   widgets: ['pie-chart', 'stats-card'],
    //   createdBy: 'Mike Johnson',
    //   createdDate: '2024-01-05T08:00:00Z',
    //   lastModified: '2024-01-15T11:30:00Z',
    //   status: 'Draft'
    // },
    // {
    //   id: 4,
    //   name: 'Financial Report Dashboard',
    //   description: 'Financial data visualization and reporting',
    //   widgets: ['bar-chart', 'line-chart', 'table'],
    //   createdBy: 'Sarah Wilson',
    //   createdDate: '2024-01-12T13:45:00Z',
    //   lastModified: '2024-01-22T10:15:00Z',
    //   status: 'Active'
    // }
  ]);
  const fetchDashboards = useCallback(async () => {
    setLoading(true);
    try {
      console.log('fetching dashboards')
      let api;
      
      if (isAdmin) {
        // Admin users: Get dashboards they created
        api = `/managedashboard/list?id=${localStorage.getItem("id")}`;
      } else {
        // Assignee users: Get dashboards assigned to them
        //api = `/managedashboard/assigned?userId=${localStorage.getItem("id")}`;
        api = `/managedashboard/list?id=${localStorage.getItem("id")}`;
      }
      
      const response = await getList(api);
      console.log(response)
      
      let dashboardList = response.data || [];
      
      // If assignee user has no assigned dashboards, fallback to admin dashboards
      if (!isAdmin && dashboardList.length === 0) {
        console.log('No assigned dashboards found, fetching admin dashboards as fallback');
        const fallbackApi = `/managedashboard/admin-fallback`;
        try {
          const fallbackResponse = await getList(fallbackApi);
          dashboardList = fallbackResponse.data || [];
        } catch (fallbackError) {
          console.log('Failed to fetch admin fallback dashboards:', fallbackError);
        }
      }
      
      // Handle automatic default dashboard logic for assignee users
      if (!isAdmin && dashboardList.length > 0) {
        await handleDefaultDashboardLogic(dashboardList);
      }
      
      // Apply client-side filtering based on search term
      let filteredDashboards = dashboardList;
      
      if (searchTerm.trim()) {
        filteredDashboards = dashboardList.filter(dashboard =>
          dashboard.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (dashboard.description && dashboard.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (dashboard.createdBy && dashboard.createdBy.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }
      
      // Sort dashboards with default on top
      const sortedDashboards = filteredDashboards.sort((a, b) => {
        // Default dashboards come first
        if (a.isDefault && !b.isDefault) return -1;
        if (!a.isDefault && b.isDefault) return 1;
        // For assignee users, sort by assignment date (latest first)
        if (!isAdmin && a.assignedDate && b.assignedDate) {
          return new Date(b.assignedDate) - new Date(a.assignedDate);
        }
        return 0; // Keep original order for other cases
      });
      
      setDashboards(sortedDashboards);
      setTotalItems(sortedDashboards.length);
    } catch (error) {
      showNotification('Error fetching dashboards', 'error');
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, isAdmin]);
  useEffect(() => {
    fetchDashboards();
  }, [fetchDashboards]);

  // Handle automatic default dashboard logic for assignee users
  const handleDefaultDashboardLogic = async (dashboardList) => {
    try {
      const hasDefault = dashboardList.some(d => d.isDefault);
      
      if (!hasDefault) {
        if (dashboardList.length === 1) {
          // Only one dashboard assigned - set as default automatically
          console.log('Auto-setting single assigned dashboard as default');
          await setDashboardAsDefault(dashboardList[0].id);
        } else if (dashboardList.length > 1) {
          // Multiple dashboards - set latest assigned as default
          const sortedByAssignmentDate = dashboardList.sort((a, b) => 
            new Date(b.assignedDate || b.createdDate) - new Date(a.assignedDate || a.createdDate)
          );
          console.log('Auto-setting latest assigned dashboard as default');
          await setDashboardAsDefault(sortedByAssignmentDate[0].id);
        }
      }
    } catch (error) {
      console.log('Error in automatic default dashboard logic:', error);
    }
  };

  // Helper function to set dashboard as default
  const setDashboardAsDefault = async (dashboardId) => {
    try {
      const api = `/managedashboard/setDefault/${dashboardId}`;
      await deleteById(api);
      return true;
    } catch (error) {
      console.log('Error setting dashboard as default:', error);
      return false;
    }
  };

  const showNotification = (message, type) => {
    setNotification({
      message,
      type,
      show: true
    });
  };

  const fetchUserList = async () => {
    const api = "/user/list?page=0&size=300&search=&sort=";
    try {
      const data = await getList(api);
      const filteredList = data.data.content
        .filter(user => user.id != localStorage.getItem("id"))
        .map(user => ({
          value: user.id,
          label: `${user.firstName} ${user.lastName} (${user.email})`,
          userData: user
        }));
        //const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      setUserList(filteredList);
    } catch (error) { }
  };

  const handleEdit = (dashboard) => {
    // Restrict edit for assignee users
    if (!isAdmin && !canUserModifyDashboard(dashboard)) {
      showNotification('You do not have permission to edit this dashboard', 'error');
      return;
    }
    
    fetchUserList();
    setSelectedDashboard(dashboard);
    setEditFormData({
      id: dashboard.id,
      name: dashboard.name,
      description: dashboard.description,
      assignedUsers: dashboard.assignedUsers || []
    });
    setShowEditModal(true);
  };

  const handleCopy = (dashboard) => {
    // Restrict copy for assignee users
    if (!isAdmin) {
      showNotification('You do not have permission to copy dashboards', 'error');
      return;
    }
    
    setSelectedDashboard(dashboard);
    setCopyFormData({
      name: `Copy of ${dashboard.name}`,
      description: dashboard.description
    });
    setShowCopyModal(true);
  };

  const handleDelete = (dashboard) => {
    // Restrict delete for assignee users
    if (!isAdmin && !canUserModifyDashboard(dashboard)) {
      showNotification('You do not have permission to delete this dashboard', 'error');
      return;
    }
    
    setSelectedDashboard(dashboard);
    setShowDeleteModal(true);
  };

  const handleDefault = (dashboard) => {
    // Allow setting default for both admin and assignee users, but with different logic
    if (!isAdmin) {
      // For assignee users, they can only set assigned dashboards as default
      const isAssigned = dashboard.assignedUsers && dashboard.assignedUsers.includes(currentUserId);
      if (!isAssigned) {
        showNotification('You can only set assigned dashboards as default', 'error');
        return;
      }
    } else {
      // For admin users, check if they own the dashboard
      if (dashboard.createdBy !== localStorage.getItem("userName")) {
        showNotification('You are not the owner of this dashboard', 'error');
        return;
      }
    }
    
    setSelectedDashboard(dashboard);
    setShowDefaultModal(true);
  };

  // Helper function to check if user can modify dashboard
  const canUserModifyDashboard = (dashboard) => {
    if (isAdmin) {
      // Admin users can modify dashboards they created
      return dashboard.createdBy === localStorage.getItem("userName");
    }
    // Assignee users cannot modify dashboards
    return false;
  };

  const handleView = (dashboard) => {
    // Transform widgetReports keys to hyphenated format
    console.log(dashboard)
    const transformedWidgetReports = {};
    
    // Check if widgetReports exists and is not null/undefined
    if (dashboard.widgetReports && typeof dashboard.widgetReports === 'object') {
      for (const [key, value] of Object.entries(dashboard.widgetReports)) {
        // Convert camelCase to hyphen-case (e.g., barChart -> bar-chart)
        const newKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
        transformedWidgetReports[newKey] = value;
      }
    }

    // Navigate to dashboard view with the dashboard data
    const dashboardData = {
      id: dashboard.id,
      dashboardName: dashboard.name,
      dashboardDescription: dashboard.description,
      selectedWidgets: dashboard.widgets || [],
      widgetReports: transformedWidgetReports,
      widgetLayout: dashboard.widgetLayout || {}, // Include widget layout data
      assignedUsers: dashboard.assignedUsers || []
    };

    console.log(dashboardData);
    navigate('/newDS/dashboardView', {
      state: { dashboardData }
    });
  };

  const confirmEdit = async () => {
    try {
      const updatedData = {
        id: editFormData.id,
        name: editFormData.name,
        description: editFormData.description,
        assignedUsers: editFormData.assignedUsers
      };
      const api = `/managedashboard/${editFormData.id}`;
      const resp = await editById(api, updatedData);
      const updatedDashboards = dashboards.map(dashboard =>
        dashboard.id === editFormData.id
          ? { 
              ...dashboard, 
              name: editFormData.name, 
              description: editFormData.description, 
              assignedUsers: editFormData.assignedUsers,
              lastModified: new Date().toISOString() 
            }
          : dashboard
      );
      setDashboards(updatedDashboards);
      setShowEditModal(false);
      showNotification('Dashboard updated successfully', 'success');
      return resp;
    } catch (error) {
      showNotification('Error updating dashboard', 'error');
    }
  };

  const confirmCopy = async () => {
    try {
      // In a real application, you would call an API here
      // const api = '/dashboard';
      // const newDashboard = {
      //   name: copyFormData.name,
      //   description: copyFormData.description,
      //   widgets: selectedDashboard.widgets
      // };
      // await addNew(api, newDashboard);

      // For now, just add to mock data
      const newDashboard = {
        id: Date.now(), // Simple ID generation for demo
        name: copyFormData.name,
        description: copyFormData.description,
        widgets: selectedDashboard.widgets,
        createdBy: localStorage.getItem('firstName') || 'Current User',
        createdDate: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        status: 'Draft'
      };

      setDashboards([newDashboard, ...dashboards]);
      setTotalItems(totalItems + 1);

      setShowCopyModal(false);
      showNotification('Dashboard copied successfully', 'success');
    } catch (error) {
      showNotification('Error copying dashboard', 'error');
    }
  };

  const confirmDelete = async () => {
    try {
      // In a real application, you would call an API here
      const api = `/managedashboard/${selectedDashboard.id}`;
      await deleteById(api);

      // For now, just remove from mock data
      const updatedDashboards = dashboards.filter(dashboard => dashboard.id !== selectedDashboard.id);
      setDashboards(updatedDashboards);
      setTotalItems(totalItems - 1);

      setShowDeleteModal(false);
      showNotification('Dashboard deleted successfully', 'success');
    } catch (error) {
      showNotification('Error deleting dashboard', 'error');
    }
  };

  const confirmSetDefault = async () => {
    console.log(selectedDashboard.id)
    try {
      const api = `/managedashboard/setDefault/${selectedDashboard.id}`;
      const resp = await deleteById(api);
      setShowDefaultModal(false);
      showNotification('Dashboard set as default successfully', 'success');
      
      // No redirection needed; stay on the list page and refresh the dashboard list
      return resp;
    }
    catch (error) {
      showNotification('Error setting default dashboard', 'error');
    }
    finally {
      fetchDashboards();
    }
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, show: false });
  };

  const handleSearchChange = (value) => {
    setSearchTerm(value);
    setCurrentPage(0);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Widget selection functions for edit modal
  // const handleWidgetToggleInEdit = (widgetId) => {
  //   const isSelected = selectedWidgetsForEdit.includes(widgetId);
  //   
  //   if (!isSelected) {
  //     setCurrentWidgetForData(widgetId);
  //     initializeWidgetReportsForEdit(widgetId);
  //     setShowWidgetDataModal(true);
  //   } else {
  //     setSelectedWidgetsForEdit(prev => prev.filter(id => id !== widgetId));
  //     // Remove the reports when unselecting
  //     setWidgetReportsForEdit(prev => {
  //       const newReports = {...prev};
  //       delete newReports[widgetId];
  //       return newReports;
  //     });
  //   }
  // };
  //
  // const initializeWidgetReportsForEdit = (widgetId) => {
  //   if (!widgetReportsForEdit[widgetId]) {
  //     setWidgetReportsForEdit(prev => ({
  //       ...prev,
  //       [widgetId]: []
  //     }));
  //   }
  // };
  //
  // const handleReportToggleInEdit = (widgetId, report) => {
  //   setWidgetReportsForEdit(prev => {
  //     const currentReports = prev[widgetId] || [];
  //     const updatedReports = currentReports.includes(report)
  //       ? currentReports.filter(r => r !== report)
  //       : [...currentReports, report];
  //       
  //     return {
  //       ...prev,
  //       [widgetId]: updatedReports
  //     };
  //   });
  // };
  //
  // const handleWidgetDataModalConfirm = () => {
  //   if (!selectedWidgetsForEdit.includes(currentWidgetForData)) {
  //     setSelectedWidgetsForEdit(prev => [...prev, currentWidgetForData]);
  //   }
  //   setShowWidgetDataModal(false);
  // };
  //
  // const handleWidgetDataModalClose = () => {
  //   setShowWidgetDataModal(false);
  // };
  //
  // const validateWidgetReportsForEdit = () => {
  //   for (const widgetId in widgetReportsForEdit) {
  //     if (widgetReportsForEdit[widgetId].length < 2) {
  //       const widget = availableWidgets.find(w => w.id === widgetId);
  //       const widgetName = widget ? widget.name : widgetId;
  //       showNotification(`${widgetName}: must contain at least 2 reports to compare data`, 'error');
  //       return false;
  //     }
  //   }
  //   return true;
  // };

  // Define table columns
  const columns = [
    {
      key: 'name',
      header: 'Dashboard Name',
      sortable: true,
      width: '20%',
      render: (value, row) => (
        <div>
          <div className="d-flex align-items-center gap-2">
            <strong className="text-primary" style={{ cursor: 'pointer' }} onClick={() => handleView(row)}>
              {value}
            </strong>
            {row.isDefault && (
              <span className="badge bg-success small">
                <i className="fa fa-star me-1"></i>
                Default
              </span>
            )}
          </div>
          <div className="text-muted small d-flex align-items-center gap-1 mt-1">
            <i className="fa fa-puzzle-piece"></i>
            {row.widgets.length} widget{row.widgets.length !== 1 ? 's' : ''}
          </div>
        </div>
      )
    },
    {
      key: 'description',
      header: 'Description',
      sortable: false,
      width: '25%',
      render: (value) => (
        <span className="text-muted">
          {value || 'No description'}
        </span>
      )
    },
    {
      key: 'createdBy',
      header: 'Created By',
      sortable: true,
      width: '15%'
    },
    {
      key: 'createdDate',
      header: 'Created Date',
      sortable: true,
      width: '15%',
      render: (value) => formatDate(value)
    },
    // {
    //   key: 'status',
    //   header: 'Status',
    //   sortable: true,
    //   width: '10%',
    //   render: (value) => (
    //     <span className={`badge ${
    //       value === 'Active' ? 'bg-success' :
    //       value === 'Draft' ? 'bg-warning' : 'bg-secondary'
    //     }`}>
    //       {value}
    //     </span>
    //   )
    // },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      width: '15%',
      render: (value, row) => {
        const canModify = canUserModifyDashboard(row);
        const canSetDefault = isAdmin ? 
          (row.createdBy === localStorage.getItem("userName")) : 
          (row.assignedUsers && row.assignedUsers.includes(currentUserId));
        
        return (
          <div className="d-flex align-items-center gap-1 flex-nowrap">
            <button
              className="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center"
              onClick={() => handleView(row)}
              title="View Dashboard"
              style={{ minWidth: '32px', height: '32px' }}
            >
              <i className="fa fa-eye"></i>
            </button>
            
            {/* Edit button - only for admin users who own the dashboard */}
            {canModify && (
              <button
                className="btn btn-sm btn-outline-secondary d-flex align-items-center justify-content-center"
                onClick={() => handleEdit(row)}
                title="Edit Dashboard"
                style={{ minWidth: '32px', height: '32px' }}
              >
                <i className="fa fa-edit"></i>
              </button>
            )}
            
            {/* Copy button - only for admin users */}
            {/* {isAdmin && (
              <button
                className="btn btn-sm btn-outline-info d-flex align-items-center justify-content-center"
                onClick={() => handleCopy(row)}
                title="Copy Dashboard"
                style={{ minWidth: '32px', height: '32px' }}
              >
                <i className="fa fa-copy"></i>
              </button>
            )} */}
            
            {/* Delete button - only for admin users who own the dashboard */}
            {canModify && (
              <button
                className="btn btn-sm btn-outline-danger d-flex align-items-center justify-content-center"
                onClick={() => handleDelete(row)}
                title="Delete Dashboard"
                style={{ minWidth: '32px', height: '32px' }}
              >
                <i className="fa fa-trash"></i>
              </button>
            )}
            
            {/* Default button - conditional logic based on user role */}
            {row.isDefault ? (
              <button
                className="btn btn-sm btn-success d-flex align-items-center justify-content-center"
                title="Default Dashboard"
                style={{ minWidth: '32px', height: '32px' }}
                disabled
              >
                <i className="fa fa-check"></i>
              </button>
            ) : (
              canSetDefault && (
                <button
                  className="btn btn-sm btn-outline-warning d-flex align-items-center justify-content-center"
                  onClick={() => handleDefault(row)}
                  title="Set as default"
                  style={{ minWidth: '32px', height: '32px' }}
                >
                  <i className="fa fa-star-o"></i>
                </button>
              )
            )}
            
            {/* Show assignment indicator for assignee users */}
            {!isAdmin && row.assignedUsers && row.assignedUsers.includes(currentUserId) && (
              <span 
                className="badge bg-info small"
                title="Assigned to you"
                style={{ fontSize: '10px' }}
              >
                <i className="fa fa-user"></i>
              </span>
            )}
          </div>
        );
      }
    }
  ];

  return (
    <div className="container-fluid mt-4">
      {notification.show &&
        <Notification
          message={notification.message}
          type={notification.type}
          show={notification.show}
          onClose={handleCloseNotification}
        />
      }
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div>
          <h2 className="text-primary mb-1">
            <i className="fa fa-dashboard me-2"></i>
            {isAdmin ? 'Dashboard Management' : 'My Dashboards'}
          </h2>
          <p className="text-muted mb-0">
            {isAdmin 
              ? 'Manage and organize your dashboards' 
              : 'View dashboards assigned to you and set your default dashboard'
            }
          </p>
          {!isAdmin && (
            <div className="mt-2">
              <span className="badge bg-info small">
                <i className="fa fa-info-circle me-1"></i>
                You can view and set default for assigned dashboards
              </span>
            </div>
          )}
        </div>
        <div className="d-flex align-items-center gap-3">
          {/* Create button only for admin users */}
          {isAdmin && (
            <button
              className="btn btn-success d-flex align-items-center"
              onClick={() => navigate('/newDS/createDashboard')}
            >
              <i className="fa fa-plus me-2"></i>
              Create New Dashboard
            </button>
          )}
          <CustomBreadcrumb
            companyName={localStorage.getItem("companyName") || "Company"}
            featureName={isAdmin ? "Dashboard Management" : "My Dashboards"}
          />
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card shadow-sm mb-4">
        <div>
          <div className="row">
            <div className="col-12 d-flex align-items-center">
              <div className="input-group">
                <span className="input-group-text">
                  <i className="fa fa-search"></i>
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search dashboards by name, description, or creator..."
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
                {searchTerm && (
                  <button
                    className="btn btn-outline-secondary"
                    type="button"
                    onClick={() => handleSearchChange('')}
                    title="Clear search"
                  >
                    <i className="fa fa-times"></i>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Table */}
      <div className="card shadow">
        <div className="card-body">
          <div style={{ width: '100%' }}>
            <DataTable
              data={dashboards}
              columns={columns}
              loading={loading}
              showSno
              searchable={false} // We're handling search externally
              pagination={true}
              currentPage={currentPage}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              emptyStateMessage="No dashboards found. Create your first dashboard to get started."
            />
          </div>
        </div>
      </div>

      {/* Edit Dashboard Modal */}
      <Modal show={showEditModal} onHide={() => setShowEditModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fa fa-edit me-2"></i>
            {isAdmin ? 'Edit Dashboard' : 'View Dashboard Details'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {!isAdmin && (
            <div className="alert alert-info mb-4">
              <i className="fa fa-info-circle me-2"></i>
              You can view dashboard details but cannot make changes. Only dashboard owners can edit dashboards.
            </div>
          )}
          <form>
            {/* Dashboard Basic Information */}
            <div className="row mb-4">
              <div className="col-md-6">
                <label htmlFor="editName" className="form-label">
                  <strong>Dashboard Name</strong> {isAdmin && <span className="text-danger">*</span>}
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="editName"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                  required={isAdmin}
                  disabled={!isAdmin}
                />
              </div>
              <div className="col-md-6">
                <label htmlFor="editDescription" className="form-label">
                  <strong>Description</strong>
                </label>
                <textarea
                  className="form-control"
                  id="editDescription"
                  rows="3"
                  value={editFormData.description}
                  onChange={(e) => setEditFormData({ ...editFormData, description: e.target.value })}
                  placeholder="Enter dashboard description"
                  disabled={!isAdmin}
                />
              </div>
            </div>
            <div className="mb-4">
              <h6 className="mb-3">
                <i className="fa fa-users me-2"></i>
                {isAdmin ? 'Share With Users' : 'Shared With Users'}
              </h6>
              {isAdmin ? (
                <Select
                  isMulti
                  options={userList}
                  value={userList.filter(user => 
                    editFormData.assignedUsers.includes(user.value)
                  )}
                  onChange={(selectedOptions) => {
                    const selectedUserIds = selectedOptions.map(option => option.value);
                    setEditFormData({
                      ...editFormData,
                      assignedUsers: selectedUserIds
                    });
                  }}
                  placeholder="Search and select users..."
                  closeMenuOnSelect={false}
                  className="basic-multi-select"
                  classNamePrefix="select"
                  noOptionsMessage={() => "No users found"}
                />
              ) : (
                <div className="border rounded p-3 bg-light">
                  {editFormData.assignedUsers.length > 0 ? (
                    <div className="d-flex flex-wrap gap-2">
                      {userList
                        .filter(user => editFormData.assignedUsers.includes(user.value))
                        .map(user => (
                          <span key={user.value} className="badge bg-primary">
                            {user.label}
                          </span>
                        ))
                      }
                    </div>
                  ) : (
                    <span className="text-muted">No users assigned</span>
                  )}
                </div>
              )}
              {/* Show selected users count */}
              {editFormData.assignedUsers.length > 0 && (
                <div className="mt-2 small text-muted d-flex align-items-center gap-1">
                  <i className="fa fa-info-circle"></i>
                  {editFormData.assignedUsers.length} user(s) {isAdmin ? 'selected' : 'assigned'}
                </div>
              )}
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-end gap-2">
          <Button variant="secondary" onClick={() => setShowEditModal(false)} className="d-flex align-items-center">
            <i className="fa fa-times me-2"></i>
            {isAdmin ? 'Cancel' : 'Close'}
          </Button>
          {isAdmin && (
            <Button
              variant="primary"
              onClick={confirmEdit}
              className="d-flex align-items-center"
            >
              <i className="fa fa-save me-2"></i>
              Save Changes
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Copy Dashboard Modal */}
      <Modal show={showCopyModal} onHide={() => setShowCopyModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fa fa-copy me-2"></i>
            Copy Dashboard
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="alert alert-info">
            <i className="fa fa-info-circle me-2"></i>
            This will create a copy of "{selectedDashboard?.name}" with all its widgets.
          </div>
          <form>
            <div className="mb-3">
              <label htmlFor="copyName" className="form-label">
                New Dashboard Name <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                className="form-control"
                id="copyName"
                value={copyFormData.name}
                onChange={(e) => setCopyFormData({ ...copyFormData, name: e.target.value })}
                required
              />
            </div>
            <div className="mb-3">
              <label htmlFor="copyDescription" className="form-label">
                Description
              </label>
              <textarea
                className="form-control"
                id="copyDescription"
                rows="3"
                value={copyFormData.description}
                onChange={(e) => setCopyFormData({ ...copyFormData, description: e.target.value })}
                placeholder="Enter description for the copied dashboard"
              />
            </div>
            <div className="mb-3">
              <label className="form-label">Widgets to be Copied</label>
              <div className="d-flex flex-wrap gap-2">
                {selectedDashboard?.widgets.map((widget, index) => (
                  <span key={index} className="badge bg-primary">
                    {widget.replace('-', ' ').toUpperCase()}
                  </span>
                ))}
              </div>
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-end gap-2">
          <Button variant="secondary" onClick={() => setShowCopyModal(false)} className="d-flex align-items-center">
            <i className="fa fa-times me-2"></i>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={confirmCopy}
            disabled={!copyFormData.name.trim()}
            className="d-flex align-items-center"
          >
            <i className="fa fa-copy me-2"></i>
            Create Copy
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <i className="fa fa-exclamation-triangle me-2"></i>
            Delete Dashboard
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="alert alert-danger">
            <strong>Warning:</strong> This action cannot be undone.
          </div>
          <p>
            Are you sure you want to delete the dashboard <strong>"{selectedDashboard?.name}"</strong>?
          </p>
          <p className="text-muted">
            This will permanently remove the dashboard and all its configurations.
          </p>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-end gap-2">
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)} className="d-flex align-items-center">
            <i className="fa fa-times me-2"></i>
            Cancel
          </Button>
          <Button variant="danger" onClick={confirmDelete} className="d-flex align-items-center">
            <i className="fa fa-trash me-2"></i>
            Delete Dashboard
          </Button>
        </Modal.Footer>
      </Modal>

      <Modal show={showDefaultModal} onHide={() => setShowDefaultModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title className="text-primary">
            <i className="fa fa-exclamation-triangle me-2"></i>
            Set Default Dashboard
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>
            Are you sure you want to set the dashboard "{selectedDashboard?.name}" as default?
          </p>
        </Modal.Body>
        <Modal.Footer className="d-flex justify-content-end gap-2">
          <Button variant="secondary" onClick={() => setShowDefaultModal(false)} className="d-flex align-items-center">
            <i className="fa fa-times me-2"></i>
            Cancel
          </Button>
          <Button variant="success" onClick={confirmSetDefault} className="d-flex align-items-center">
            <i className="fa fa-star me-2"></i>
            Set as Default
          </Button>
        </Modal.Footer>
      </Modal>

      {loading && <Loader />}
    </div>
  );
}

export default DashboardManagement; 