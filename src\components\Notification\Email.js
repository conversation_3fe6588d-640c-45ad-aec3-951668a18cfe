import React, { Component } from "react";
import classes from "./Email.module.css";
import Notification from "./Notification";
import { addNew, getList, editById } from "../../services/apiService";
import Loader from "../loader/Loader";

class Email extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      emailId: null,
      email: "",
      name: "",
      password: "",
      testEmail: "",
      showPassword: false,
      errors: {},
      testEmailError: "",
      notification: {
        message: "",
        type: "",
        show: false,
      },
      isEditMode: false,
    };
  }

  componentDidMount() {
    this.fetchEmailList();
  }

  fetchEmailList = async () => {
    const api = `/manageemail/list`;
    try {
      const response = await getList(api);
      const data = response.data;
      if (data) {
        this.setState({
          email: data.email,
          name: data.name,
          password: data.password,
          emailId: data.id,
          isEditMode: true,
        });
      }
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };
  handleChange = (event) => {
    this.setState({
      [event.target.name]: event.target.value,
      errors: { ...this.state.errors, [event.target.name]: "" },
      testEmailError: "",
    });
  };

  handleSave = () => {
    const { email, name, password } = this.state;
    let errors = {};
    if (!email) errors.email = "Email is required";
    if (!name) errors.name = "Name is required";
    if (!password) errors.password = "Passkey is required";

    if (Object.keys(errors).length > 0) {
      this.setState({ errors });
      return;
    }
    let obj = {
      email: this.state.email,
      name: this.state.name,
      password: this.state.password,
    };
    console.log("Saved Data:", obj);
    if (this.state.isEditMode) {
      this.updateData(obj);
    } else {
      this.submitData(obj);
    }
  };

  submitData = async (obj) => {
    const api = "/manageemail";
    try {
      const response = await addNew(api, obj);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  updateData = async (obj) => {
    const api = `/manageemail/${this.state.emailId}`;
    try {
      const response = await editById(api, obj);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleClear = () => {
    this.setState({
      email: "",
      name: "",
      password: "",
      showPassword: false,
      errors: {},
      testEmailError:"",
      testEmail:"",
    });
  };

  togglePasswordVisibility = () => {
    this.setState((prevState) => ({ showPassword: !prevState.showPassword }));
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  diagnose = async () => {
    this.setState({ isLoading: true });
    const { testEmail } = this.state;
    if (testEmail === "") {
      this.setState({ testEmailError: "Email is required" });
      return;
    }
    console.log("Test Email:", this.state.testEmail);


      const api = `/manageemail/validateEmail?testEmail=${this.state.testEmail}`;
        try {
          const response = await getList(api);
          //alert(response);
          if(response){
        this.setState({
        notification: {
          message: "Mail working fine",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
     // this.componentDidMount();
      return response.data;
    }else{
      this.setState({
        notification: {
          message: "Please Enter valid email and passcode",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
      //throw error;
    }
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong please check mail details once",
          type: "error",
          show: true,
        },
      });
      throw error;
    }


  };

  render() {
    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Email Settings</h4>
            <hr />
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 p-3 border rounded">
            <h4>
              <i className="fa fa-user"></i>&nbsp;Email Information
            </h4>
            <hr />
            <div className="row mt-3">
              <div className="col-md-6">
                <label>
                  Email <span style={{ color: "red" }}>*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  value={this.state.email}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Email"
                />
                {this.state.errors.email && (
                  <span className="text-danger">{this.state.errors.email}</span>
                )}
              </div>

              <div className="col-md-6 mt-3 mt-md-0">
                <label>
                  Name <span style={{ color: "red" }}>*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={this.state.name}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Name"
                />
                {this.state.errors.name && (
                  <span className="text-danger">{this.state.errors.name}</span>
                )}
              </div>

              <div className="col-md-6 mt-3">
                <label>
                  Passkey <span style={{ color: "red" }}>*</span>
                </label>
                <div className="input-group">
                  <input
                    type={this.state.showPassword ? "text" : "password"}
                    name="password"
                    value={this.state.password}
                    onChange={this.handleChange}
                    className="form-control"
                    placeholder="Passkey"
                  />
                  <div className="input-group-append">
                    <span
                      className="btn btn-outline-gray"
                      type="button"
                      onClick={this.togglePasswordVisibility}
                    >
                      {this.state.showPassword ? (
                        <i className="fa fa-eye-slash"></i>
                      ) : (
                        <i className="fa fa-eye"></i>
                      )}
                    </span>
                  </div>
                </div>
                {this.state.errors.password && (
                  <span className="text-danger">
                    {this.state.errors.password}
                  </span>
                )}
              </div>

              <div className="col-md-6 mt-3">
                <label>Test Email</label>
                <input
                  type="email"
                  name="testEmail"
                  value={this.state.testEmail}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Test Email"
                />
                {this.state.testEmailError && (
                  <span className="text-danger">
                    {this.state.testEmailError}
                  </span>
                )}
              </div>
            </div>

            <div className="row mt-3">
              <div className="col-12 d-flex justify-content-start">
                <button
                  className="btn btn-primary mr-2"
                  onClick={this.handleSave}
                >
                  {this.state.isEditMode ? "Update" : "Save"}
                </button>&nbsp;&nbsp;
                <button
                  className="btn btn-danger mr-2"
                  onClick={this.handleClear}
                >
                  Clear
                </button>&nbsp;&nbsp;
                <button className="btn btn-secondary" onClick={this.diagnose}>
                  Diagnose Test Mail
                </button>
              </div>
            </div>
          </div>
        </div>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default Email;
