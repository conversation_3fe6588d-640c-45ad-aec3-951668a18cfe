import React, { Component } from "react";
import Loader from "../loader/Loader";
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import DownloadReport from "../common/DownloadReport";
import { getList, formatDate } from "../../services/apiService";
import { format, isBefore } from "date-fns";
import DatePicker from "react-datepicker";
import Select from "react-dropdown-select";
import { DataTable } from "../Table/DataTable";
import CustomBreadcrumb from "../common/CustomBreadcrumb";

class SpecificDocLogReport extends Component {
  state = {
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    docList: [],
    userList: [],
    logList: [],
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    startDate: "",
    endDate: "",
    fileName: "",
    user: "",
    fileError: "",
    currentFilter: null,
  };

  componentDidMount() {
    const today = new Date().toISOString().split("T")[0];
    this.setState({
      startDate: today,
      endDate: today,
    });
    this.fetchDocumentList();
    this.fetchUserList();
  }

  fetchDocumentList = async () => {
    this.setState({ isLoading: true });
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_files?page=0&size=3000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      this.setState({
        docList: data.content,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  fetchUserList = async () => {
    const api = `/user/list?page=0&size=100&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        userList: data.content,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  fetchSpecificDocLogList = async (
    filter,
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    this.setState({ isLoading: true, currentFilter: filter });
    const api = `/documentreports/list_by_specific_file_logs?page=${page}&size=${itemsPerPage}`;
    try {
      const response = await getList(api, filter);
      const data = response.data;
      this.setState({
        logList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (e) {
      console.log(e);
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    const { startDate, endDate, fileName, user } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
      id: fileName,
      user: user,
    };
    this.fetchSpecificDocLogList(filter, page);
  };

  handleItemsPerPageChange = (newSize) => {
    const { startDate, endDate, fileName, user } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
      id: fileName,
      user: user,
    };
    this.setState({ itemsPerPage: newSize }, () => {
      this.fetchSpecificDocLogList(filter, 0, newSize);
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true,
      },
    });
  };

  handleStartDateChange = (date) => {
    const formattedDate = format(date, "yyyy-MM-dd");

    const { endDate } = this.state;
    if (endDate && date > new Date(endDate)) {
      console.log("Start date cannot be after the end date");
    } else {
      this.setState({ startDate: formattedDate });
    }
  };

  handleEndDateChange = (date) => {
    this.setState({ showEndError: "" });
    if (!date) {
      this.setState({ endDate: null });
      return;
    }
    const formattedDate = format(date, "yyyy-MM-dd");

    const { startDate } = this.state;
    if (startDate && isBefore(date, new Date(startDate))) {
      console.log("End date cannot be before the start date");
    } else {
      this.setState({ endDate: formattedDate });
    }
  };

  handleFileSelect = (values) => {
    const selectedValue = values.length > 0 ? values[0].value : "";
    this.setState({
      fileName: selectedValue + "",
      fileError: "",
    });
  };

  applyFilter = () => {
    this.setState({ logList: [] });
    const { startDate, endDate, fileName, user } = this.state;
    if (fileName === "") {
      this.setState({ fileError: "Select a File to show logs" });
    } else {
      const filter = {
        startDate: startDate,
        endDate: endDate,
        id: fileName,
        user: user,
      };
      this.fetchSpecificDocLogList(filter);
    }
  };

  clearFilter = () => {
    this.setState({
      startDate: "",
      endDate: "",
      fileName: "",
      fileError: "",
      user: "",
      logList: [],
      currentPage: 0,
      currentFilter: null,
    });
  };

  render() {
    const {
      startDate,
      endDate,
      fileName,
      user,
      fileError,
      logList,
      itemsPerPage,
      currentPage,
      totalItems,
      docList,
    } = this.state;

    const columns = [
      {
        key: "index",
        header: "S.No",
        render: (_, __, index) => currentPage * itemsPerPage + index + 1,
        width: "60px",
      },
      {
        key: "date",
        header: "Date & Time",
        sortable: true,
        render: (_, row) =>
          row.modifiedDate
            ? formatDate(row.modifiedDate)
            : formatDate(row.createdDate),
        filterValue: (row) =>
          row.modifiedDate
            ? formatDate(row.modifiedDate)
            : formatDate(row.createdDate),
        width: "160px",
      },
      {
        key: "location",
        header: "Location",
        sortable: true,
        render: (_, row) => {
          // Get the selected file's location from docList
          const selectedFile = docList?.find(
            (doc) => doc.documentsAttachmentId == fileName
          );
          if (selectedFile && selectedFile.filePath) {
            // Extract the folder path by removing the filename
            const fullPath = selectedFile.filePath.split(/Emp_Id_\d+\//)[1] || selectedFile.filePath;
            const pathParts = fullPath.split('/');
            const folderPath = pathParts.slice(0, -1).join('/');
            return folderPath || '/';
          }
          return 'N/A';
        },
        width: "200px",
      },
      {
        key: "logMessage",
        header: "Action",
        sortable: true,
        width: "130px",
      },
      {
        key: "createdBy",
        header: "Done by User",
        sortable: true,
        width: "120px",
      },
    ];

    return (
      <>
        <div className="container mt-3">
          {this.state.notification.show && (
            <Notification
              message={this.state.notification.message}
              type={this.state.notification.type}
              onClose={this.closeNotification}
            />
          )}
          <div className="row align-items-center">
            <div className="col-8">
              <h5 className="pt-1">Specific Document Log Report</h5>
            </div>
            <div className="col-4 text-end">
              <div className="d-inline-flex align-items-center gap-2">
                {" "}
                <DownloadReport
                  excelEndpoint={`${GlobalConstants.globalURL}/documentreports/specificDocLogReport/exportAllSpecificDocLogExcel`}
                  pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/specificDocLogReport/exportAllSpecificDocLogPDF`}
                  reportName="SpecificDocLogReport"
                  filter={this.state.currentFilter}
                  onError={this.handleDownloadError}
                  buttonStyle={{
                    background: "#fff",
                    color: "#333",
                    border: "1px solid #ccc",
                    fontSize: "16px",
                  }}
                />
                <button
                  className="btn btn-link"
                  style={{
                    textDecoration: "underline",
                    color: "#1976d2",
                    fontWeight: 500,
                    fontSize: "16px",
                  }}
                  onClick={() => window.history.back()}
                >
                  Back
                </button>
              </div>
            </div>
          </div>

          {/* <div className="row mt-3">
            <div className="col-12">
              <CustomBreadcrumb
                companyName={localStorage.getItem("companyName") || "Company"}
                featureName="Specific Document Log Report"
              />
            </div>
          </div> */}

          <div className="row mt-3">
            <div className="col-12">
              <form className="row g-3 align-items-end">
                <div className="col-md-2">
                  <label htmlFor="startDate">Start Date</label>
                  <DatePicker
                    className="form-control"
                    selected={startDate ? new Date(startDate) : null}
                    onChange={this.handleStartDateChange}
                    dateFormat="dd-MM-yyyy"
                    placeholderText="Start Date"
                    maxDate={endDate ? new Date(endDate) : null}
                  />
                </div>

                <div className="col-md-2">
                  <label htmlFor="endDate">End Date</label>
                  <DatePicker
                    className="form-control"
                    selected={endDate ? new Date(endDate) : null}
                    onChange={this.handleEndDateChange}
                    dateFormat="dd-MM-yyyy"
                    placeholderText="End Date"
                    minDate={startDate ? new Date(startDate) : null}
                  />
                </div>

                <div className="col-md-4">
                  <label htmlFor="fileName">
                    File Name <span style={{ color: "red" }}>*</span>
                  </label>
                  <Select
                    key={`select-${fileName || "empty"}`}
                    options={
                      docList
                        ? docList.map((doc) => ({
                            value: doc.documentsAttachmentId,
                            label:
                              doc.filePath &&
                              doc.filePath.split(/Emp_Id_\d+\//)[1],
                          }))
                        : []
                    }
                    values={
                      fileName
                        ? [
                            {
                              value: fileName,
                              label: docList
                                ?.find(
                                  (doc) => doc.documentsAttachmentId == fileName
                                )
                                ?.filePath?.split(/Emp_Id_\d+\//)[1],
                            },
                          ]
                        : []
                    }
                    onChange={(values) => this.handleFileSelect(values)}
                    placeholder="Select a file"
                    searchable={true}
                    dropdownPosition="auto"
                    className="form-control"
                    dropdownHandle={false}
                    noDataLabel="No files found"
                  />
                  {fileError && <div className="text-danger">{fileError}</div>}
                </div>

                <div className="col-md-2">
                  <label htmlFor="user">User</label>
                  <select
                    id="user"
                    value={user}
                    className="form-control"
                    onChange={(e) => this.setState({ user: e.target.value })}
                  >
                    <option value="">All</option>
                    {this.state.userList &&
                      this.state.userList.map((user, index) => (
                        <option key={index} value={user.userName}>
                          {user.firstName} {user.lastName}
                        </option>
                      ))}
                  </select>
                </div>

                <div className="col-md-2">
                  <button
                    type="button"
                    className="btn btn-primary me-2"
                    onClick={this.applyFilter}
                  >
                    Filter
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={this.clearFilter}
                  >
                    Clear
                  </button>
                </div>
              </form>
            </div>
          </div>

          <div className="row mt-4">
            <div className="col-12">
              {logList.length > 0 ? (
                <DataTable
                  data={logList}
                  columns={columns}
                  totalItems={totalItems}
                  currentPage={currentPage}
                  itemsPerPage={itemsPerPage}
                  onPageChange={this.handlePageChange}
                  onItemsPerPageChange={this.handleItemsPerPageChange}
                  className="font-weight-normal"
                />
              ) : (
                <div className="text-center py-5">
                  <div className="card">
                    <div className="card-body">
                      <i className="fa fa-file-text-o fa-3x text-muted mb-3"></i>
                      <h5 className="text-muted">
                        {this.state.currentFilter 
                          ? "No results found for the applied filters" 
                          : "Please select a file and apply filters to view log data"}
                      </h5>
                      <p className="text-muted mb-0">
                        {this.state.currentFilter 
                          ? "Try adjusting your filter criteria and search again."
                          : "Choose a document from the dropdown and click 'Filter' to see its activity logs."}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default SpecificDocLogReport;
