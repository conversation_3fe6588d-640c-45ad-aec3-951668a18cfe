import React from "react";
import <PERSON><PERSON><PERSON> from "./Charts/BarChart";
import <PERSON><PERSON><PERSON> from "./Charts/PieChart";
import LineC<PERSON> from "./Charts/LineChart";
import { DataTable } from "../Table/DataTable";
import { getDisplayPath, formatDate } from "../../services/apiService";
import { Link } from "react-router-dom";

function StaticDashboardView({ dashboardConfig, counts, documents }) {
  if (!dashboardConfig) return null;

  const { name, description, widgets } = dashboardConfig;
  const {
    duplicateCount,
    pendingWFCount,
    retentionCount,
    dueCount,
    archiveCount,
    totalCount,
  } = counts;

  // Sample data for the DataTable widget
  const sampleTableData = documents;

  // Column configuration for DataTable
  const tableColumns = [
    {
      key: "documentName",
      header: "DOCUMENT NAME",
      sortable: true,
      width: "30%",
    },
    {
      key: "filePath",
      header: "LOCATION",
      sortable: true,
      width: "10%",
      render: (value) => value && getDisplayPath(value),
    },
    // {
    //   key: 'type',
    //   header: 'Type',
    //   sortable: true,
    //   width: '10%',
    //   render: (value) => (
    //     <span className={`badge ${
    //       value === 'PDF' ? 'bg-danger' :
    //       value === 'DOCX' ? 'bg-primary' :
    //       value === 'XLSX' ? 'bg-success' :
    //       value === 'PPTX' ? 'bg-warning' : 'bg-secondary'
    //     }`}>
    //       {value}
    //     </span>
    //   )
    // },
    // {
    //   key: 'status',
    //   header: 'Status',
    //   sortable: true,
    //   width: '15%',
    //   render: (value) => (
    //     <span className={`badge ${
    //       value === 'Active' ? 'bg-success' :
    //       value === 'Pending' ? 'bg-warning' :
    //       value === 'Draft' ? 'bg-info' : 'bg-secondary'
    //     }`}>
    //       {value}
    //     </span>
    //   )
    // },
    {
      key: "createdDate",
      header: "CREATED DATE",
      sortable: true,
      width: "15%",
      render: (value) => formatDate(value),
    },
    {
      key: "createdBy",
      header: "OWNER",
      sortable: true,
      width: "20%",
    },
  ];

  const renderWidget = (widget) => {
    const { id, type, position, size, reports } = widget;
    const widgetNumber = id.split("-").pop() || "1";

    // Map report names to actual count values
    const getReportValue = (reportName) => {
      switch (reportName) {
        case "Total Files":
          return totalCount;
        case "Duplicate Files":
          return duplicateCount;
        case "Pending Workflows Files":
          return pendingWFCount;
        case "Due Date Files":
          return dueCount;
        case "Archive Files":
          return archiveCount;
        case "Retention End Files":
          return retentionCount;
        default:
          return 0;
      }
    };

    // Create widget-specific data based on reports
    const getWidgetData = () => {
      if (reports && reports.length > 0) {
        // If widget has specific reports configured, use only those
        const chartData = {};
        reports.forEach(reportName => {
          chartData[reportName] = getReportValue(reportName);
        });
        return { data: chartData };
      } else {
        // Fallback to all data
        return {
          duplicateCount,
          pendingWFCount,
          retentionCount,
          dueCount,
          archiveCount,
          totalCount,
        };
      }
    };

    const widgetData = getWidgetData();

    const getWidgetContent = () => {
      switch (type) {
        case "bar-chart":
          return (
            <div className="card shadow h-100">
              <div className="card-header bg-primary text-white">
                <h5 className="mb-0">
                  <i className="fa fa-chart-bar me-2"></i>
                  Bar Chart #{widgetNumber}
                </h5>
              </div>
              <div className="card-body" style={{ minHeight: "250px" }}>
                <BarChart {...widgetData} />
              </div>
            </div>
          );

        case "pie-chart":
          return (
            <div className="card shadow h-100">
              <div className="card-header bg-success text-white">
                <h5 className="mb-0">
                  <i className="fa fa-pie-chart me-2"></i>
                  Pie Chart #{widgetNumber}
                </h5>
              </div>
              <div className="card-body" style={{ minHeight: "250px" }}>
                <PieChart {...widgetData} />
              </div>
            </div>
          );

          case "stats-card":
            console.log("first", counts);
            console.log("second", widgetData.data);
            console.log("third", dashboardConfig);
            
            // Create a mapping between stat labels and widgetData keys
            const labelToDataKey = {
              "Total Documents": "Total Files",
              "Pending Workflows": "Pending Workflows Files",
              "Duplicates Found": "Duplicate Files",
              "Archived Files": "Archive Files",
              "Due Items": "Due Date Files"
            };
          
            const statTypes = [
              {
                label: "Total Documents",
                value: counts.totalCount,
                icon: "fa fa-file-text",
                color: "primary",
                link: "/newDS/allfilesReport"
              },
              {
                label: "Pending Workflows",
                value: counts.pendingWFCount,
                icon: "fa fa-clock-o",
                color: "warning",
                link: "/newDS/pendingWorkFlows"
              },
              {
                label: "Duplicates Found",
                value: counts.duplicateCount,
                icon: "fa fa-copy",
                color: "danger",
                link: "/newDS/duplicatesReport"
              },
              {
                label: "Archived Files",
                value: counts.archiveCount,
                icon: "fa fa-archive",
                color: "info",
                link: "/newDS/archivedReport"
              },
              {
                label: "Due Items",
                value: counts.dueCount,
                icon: "fa fa-calendar",
                color: "secondary",
                link: "/newDS/duedateReport"
              },
            ];
          
            // Filter statTypes to only include items that have a corresponding key in widgetData.data
            const filteredStatTypes = statTypes.filter(stat => {
              const dataKey = labelToDataKey[stat.label];
              return widgetData.data.hasOwnProperty(dataKey);
            });
          
            // If no matching stats found, return null or a message
            if (filteredStatTypes.length === 0) {
              return (
                <div className="card shadow border-left-primary h-100" style={{ width: "300px" }}>
                  <div className="card-body d-flex align-items-center justify-content-center p-2">
                    <div className="text-muted">No matching stats available</div>
                  </div>
                </div>
              );
            }
          
            return (
                <div className="card shadow border-left-primary" style={{ width: "300px", height: "400px", display: "flex", flexDirection: "column" }}>
                  <div className="card-header bg-light p-2">
                  <h6 className="mb-0 text-primary" style={{ fontSize: "0.9rem" }}>
                    <i className="fa fa-chart-pie me-1"></i>
                    Stats Cards
                  </h6>
                </div>
                <div className="card-body p-0" style={{ overflowY: "auto", flex: "1" }}>
                  <div style={{ marginTop: "8px" }}>
                    {filteredStatTypes.map((stat, index) => (
                      <div key={index} className="mb-2" style={{ 
                        borderBottom: index < filteredStatTypes.length - 1 ? "1px solid #eee" : "none", 
                        padding: "8px 0" 
                      }}>
                        <div className="row no-gutters align-items-center w-100 g-0">
                          <div className="col mr-1">
                            <div
                              className={`text-xs font-weight-bold text-${stat.color} text-uppercase mb-0`}
                              style={{ fontSize: "0.7rem" }}
                            >
                              {stat.label}
                            </div>
                            <Link
                              to={stat.link}
                              style={{ textDecoration: "none", color: "inherit" }}
                            >
                              <div className="h5 mb-0 font-weight-bold text-gray-800" style={{ fontSize: "1.25rem" }}>
                                {stat.value}
                              </div>
                            </Link>
                          </div>
                          <div className="col-auto">
                            <i
                              className={`${stat.icon} fa-2x text-gray-300 text-${stat.color}`}
                              style={{ opacity: 0.8 }}
                            ></i>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );

        case "table":
          return (
            <div className="card shadow h-100">
              <div className="card-header bg-info text-white">
                <h5 className="mb-0">
                  <i className="fa fa-table me-2"></i>
                  Table #{widgetNumber}
                </h5>
              </div>
              <div className="card-body p-0 overflow-hidden">
                <div className="p-3 pb-0 w-100 h-100 overflow-auto">
                  <DataTable
                    data={sampleTableData}
                    columns={tableColumns}
                    searchable={true}
                    itemsPerPage={5}
                    className="table-sm"
                    showSno={true}
                  />
                </div>
              </div>
            </div>
          );

        case "line-chart":
          return (
            <div className="card shadow h-100">
              <div className="card-header bg-warning text-white">
                <h5 className="mb-0">
                  <i className="fa fa-line-chart me-2"></i>
                  Line Chart #{widgetNumber}
                </h5>
              </div>
              <div className="card-body" style={{ minHeight: "250px" }}>
                <LineChart {...widgetData} />
              </div>
            </div>
          );

        default:
          return (
            <div className="card shadow h-100">
              <div className="card-header bg-secondary text-white">
                <h5 className="mb-0">
                  <i className="fa fa-square me-2"></i>
                  {type.charAt(0).toUpperCase() +
                    type.slice(1).replace("-", " ")}{" "}
                  #{widgetNumber}
                </h5>
              </div>
              <div className="card-body text-center d-flex align-items-center justify-content-center">
                <div>
                  <i className={`fa fa-${type} fa-3x text-muted mb-3`}></i>
                  <h5>
                    {type.charAt(0).toUpperCase() +
                      type.slice(1).replace("-", " ")}
                  </h5>
                  <p className="text-muted">Widget content</p>
                </div>
              </div>
            </div>
          );
      }
    };

    return (
      <div
        key={id}
        style={{
          position: "absolute",
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${size.width}px`,
          height: `${size.height}px`,
        }}
      >
        {getWidgetContent()}
      </div>
    );
  };

  return (
    <div className="container-fluid mt-1">
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div>
          <h4 className="text-primary mb-1">
            <i className="fa fa-dashboard me-2"></i>
            {name}
          </h4>
          {description && <p className="text-muted mb-0">{description}</p>}
        </div>
      </div>

      <div
        style={{
          minHeight: "900px",
          position: "relative",
          background:
            "linear-gradient(90deg, #f8f9fa 1px, transparent 1px), linear-gradient(#f8f9fa 1px, transparent 1px)",
          backgroundSize: "20px 20px",
          border: "1px solid #e9ecef",
          borderRadius: "8px",
        }}
      >
        {widgets.map((widget) => renderWidget(widget))}
      </div>
    </div>
  );
}

export default StaticDashboardView;
