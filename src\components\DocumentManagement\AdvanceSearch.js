import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Table } from "react-bootstrap";
import classes from "../CreateUser/CreateUser.module.css";
import {
  getList,
  addNew,
  editById,
  deleteById,
  formatDate,
  getDisplayPath,
} from "../../services/apiService";
import Notification from "../Notification/Notification";
import ReactPaginate from "react-paginate";
import { DataTable } from "../Table/DataTable";

export class AdvanceSearch extends Component {
  state = {
    isOpen: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 20,
    startDate: "",
    endDate: "",
    searchBy: "",
    filename: "",
    owner: "",
    tag: "",
    metaDataField: "",
    metaDataValue: "",
    startDateError: "",
    endDateError: "",
    userList: [],
    metaDataList: [],
  };

  componentDidMount() {
    const today = new Date().toISOString().split("T")[0];
    this.setState({
      startDate: today,
      endDate: today,
    });
    this.fetchUserList();
    this.fetchMetaDataList();
    this.setState({ isOpen: true });
  }

  fetchMetaDataList = async () => {
    const api = `/metadata/list?type=metadata`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({
        metaDataList: response.data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  fetchUserList = async () => {
    const api = `/user/list?page=0&size=2000&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        userList: data.content,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openModal = () => {
    this.setState({
      isOpen: true,
    });
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
    });
  };

  handleSearchByChange = (event) => {
    this.setState({ searchBy: event.target.value, metaDataField: "" });
  };

  handleSubmit = () => {
    const {
      startDate,
      endDate,
      searchBy,
      filename,
      owner,
      tag,
      metaDataField,
      metaDataValue,
    } = this.state;
    let value = "";

    let startDateError = "";
    let endDateError = "";

    if (startDate === "") {
      startDateError = "Start Date is required.";
    }
    if (endDate === "") {
      endDateError = "End Date is required.";
    }

    if (startDateError || endDateError) {
      this.setState({ startDateError, endDateError });
      return;
    }

    this.setState({ startDateError: "", endDateError: "" });
    if (searchBy === "filename") {
      value = filename;
    } else if (searchBy === "owner") {
      value = owner;
    } else if (searchBy === "tag") {
      value = tag;
    } else if (searchBy === "metadata") {
      value = metaDataValue;
    }
    const obj = {
      startDate: startDate,
      endDate: endDate,
      typeName: searchBy,
      typeField: metaDataField,
      typeValue: value,
    };

    console.log("Form Data Object:", obj);
    this.fetchDocumentList(0, obj);
  };

  handlePageChange = (selectedPage) => {
    const {
      startDate,
      endDate,
      searchBy,
      filename,
      owner,
      tag,
      metaDataField,
      metaDataValue,
    } = this.state;
    let value = "";
    if (searchBy === "filename") {
      value = filename;
    } else if (searchBy === "owner") {
      value = owner;
    } else if (searchBy === "tag") {
      value = tag;
    } else if (searchBy === "metadata") {
      value = metaDataValue;
    }
    const obj = {
      startDate: startDate,
      endDate: endDate,
      typeName: searchBy,
      typeField: metaDataField,
      typeValue: value,
    };
    this.fetchDocumentList(selectedPage.selected, obj);
  };

  fetchDocumentList = async (page = 0, obj) => {
    const { itemsPerPage } = this.state;
    const api =
      "/documentsattachmentdetail/list-by-advance-search" +
      `?page=${page}&size=${itemsPerPage}`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api, obj);
      const data = response.data;
      //console.log("Fetched documents:", data.content); // Log fetched documents
      this.setState({
        documentList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
        isOpen: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      // alert("error");
    }
  };

  render() {
    const {
      isOpen,
      startDate,
      endDate,
      searchBy,
      filename,
      owner,
      tag,
      userList,
      metaDataList,
      metaDataField,
      metaDataValue,
    } = this.state;

    const { currentPage, totalItems, itemsPerPage, documentList } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    // DataTable columns config
    const columns = [
      {
        key: "sno",
        header: "SNo",
        width: "70px",
        render: (_v, _row, idx) => start + idx,
        sortable: true,
      },
      {
        key: "documentName",
        header: "Filename",
        sortable: true,
      },
      {
        key: "filePath",
        header: "Location",
        render: (v) => (v ? getDisplayPath(v) : ""),
        sortable: true,
      },
      {
        key: "createdDate",
        header: "Upload Date",
        render: (v) => formatDate(v),
        sortable: true,
      },
      {
        key: "createdBy",
        header: "Owner",
        sortable: true,
      },
    ];

    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>List of Documents</h4>
            <hr />
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        {/* DataTable Modern Table */}
        <div className="row mt-4 justify-content-center">
          <div className="col-12 col-lg-11">
            <div
              style={{
                background: "#f8fafc",
                borderRadius: "16px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
                padding: "2rem 1.5rem",
                marginBottom: "2rem",
              }}
            >
              <div className="d-flex align-items-center justify-content-between mb-3">
                <h5 className="fw-bold mb-0" style={{ letterSpacing: "0.5px" }}>
                  <i className="fa fa-file-text-o text-primary me-2"></i>
                  Document List
                </h5>
                <Button
                  variant="primary"
                  onClick={() => this.openModal()}
                  className="ms-3"
                >
                  <i className="fa fa-search"></i>&nbsp; Search
                </Button>
              </div>
              <DataTable
                data={Array.isArray(documentList) ? documentList : []}
                columns={columns}
                className="table-striped table-hover"
                itemsPerPage={itemsPerPage}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                showSno={false}
              />
            </div>
          </div>
        </div>

        <Modal show={this.state.isOpen} onHide={this.closeModal} size="md">
          <Modal.Header
            closeButton
            className="modal-header-modern"
          >
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              Advanced Search
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {/* Start Date and End Date */}
            <div className="mb-3">
              <label htmlFor="startDate">
                Start Date <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => this.setState({ startDate: e.target.value })}
                className="form-control"
              />
              {this.state.startDateError && (
                <div className="text-danger">{this.state.startDateError}</div>
              )}
            </div>

            <div className="mb-3">
              <label htmlFor="endDate">
                End Date <span style={{ color: "red" }}>*</span>
              </label>
              <input
                type="date"
                id="endDate"
                value={endDate}
                onChange={(e) => this.setState({ endDate: e.target.value })}
                className="form-control"
              />
              {this.state.endDateError && (
                <div className="text-danger">{this.state.endDateError}</div>
              )}
            </div>

            {/* Search By Dropdown */}
            <div className="mb-3">
              <label htmlFor="searchBy">Search By</label>
              <select
                id="searchBy"
                value={searchBy}
                onChange={this.handleSearchByChange}
                className="form-control"
              >
                <option value="">All</option>
                <option value="filename">Filename</option>
                <option value="owner">Owner</option>
                <option value="tag">Tag</option>
                <option value="metadata">Meta Data</option>
              </select>
            </div>

            {/* Dynamic fields based on Search By selection */}
            {searchBy === "filename" && (
              <div className="mb-3">
                <label htmlFor="filename">Filename</label>
                <input
                  type="text"
                  id="filename"
                  value={filename}
                  placeholder="Enter filename"
                  onChange={(e) => this.setState({ filename: e.target.value })}
                  className="form-control"
                />
              </div>
            )}

            {searchBy === "owner" && (
              <div className="mb-3">
                <label htmlFor="owner">Owner</label>
                <select
                  id="owner"
                  value={owner}
                  onChange={(e) => this.setState({ owner: e.target.value })}
                  className="form-control"
                >
                  <option value="">Select Owner</option>
                  {userList.map((user, index) => (
                    <option key={index} value={user.userName}>
                      {user.firstName} {user.lastName}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {searchBy === "tag" && (
              <div className="mb-3">
                <label htmlFor="tag">Tag</label>
                <input
                  type="text"
                  id="tag"
                  placeholder="Enter tag name"
                  value={tag}
                  onChange={(e) => this.setState({ tag: e.target.value })}
                  className="form-control"
                />
              </div>
            )}

            {searchBy === "metadata" && (
              <div className="mb-3">
                <label htmlFor="metadata">Meta data</label>
                <select
                  id="metadata"
                  value={metaDataField}
                  onChange={(e) =>
                    this.setState({ metaDataField: e.target.value })
                  }
                  className="form-control"
                >
                  <option value="">Select Meta data field</option>
                  {metaDataList &&
                    metaDataList.map((md, index) => (
                      <option key={index} value={md.fieldName}>
                        {md.fieldName}
                      </option>
                    ))}
                </select>
              </div>
            )}

            {(metaDataField !== "" || searchBy === "metadata") && (
              <div className="mb-3">
                <label htmlFor="metaDataValue">Meta Data Value</label>
                <input
                  type="text"
                  id="metaDataValue"
                  placeholder="Enter metadata value"
                  value={metaDataValue}
                  onChange={(e) =>
                    this.setState({ metaDataValue: e.target.value })
                  }
                  className="form-control"
                />
              </div>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button
              variant="primary"
              onClick={this.handleSubmit}
              className="btn btn-primary"
            >
              Search
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

export default AdvanceSearch;
