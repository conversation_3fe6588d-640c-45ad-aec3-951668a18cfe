/* Pagination Styles */
.pagination-container {
  margin: 20px 0;
}

.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 15px 0;
}

.pagination .page-item {
  margin: 0 2px;
}

.pagination .page-link {
  cursor: pointer;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #333;
  text-decoration: none;
  background-color: #fff;
  display: flex;
  align-items: center;
}

.pagination .active .page-link {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination .disabled .page-link {
  color: #ccc;
  cursor: not-allowed;
  background-color: #f8f9fa;
}

.pagination .page-link:hover:not(.active) {
  background-color: #f1f1f1;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.items-per-page {
  display: flex;
  align-items: center;
}

.items-per-page span {
  margin-right: 5px;
  color: #666;
}

.items-per-page select {
  margin-left: 10px;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: #fff;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

/* Add some spacing for the icons in pagination buttons */
.pagination .fa {
  margin: 0 4px;
}
