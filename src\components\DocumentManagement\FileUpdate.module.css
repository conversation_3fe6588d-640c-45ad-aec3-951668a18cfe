.fileView {
    border: 1px solid #e0e0e0;
    height: 80px;
    width: 120px;
    background: linear-gradient(135deg, #f8fafc 60%, #e3e9f3 100%);
    cursor: pointer;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #2d3a4a;
    transition: box-shadow 0.2s;
}
.fileView:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    background: linear-gradient(135deg, #e3e9f3 60%, #f8fafc 100%);
}

.previewModalBody{
    max-height: 60vh; 
    overflow-y: auto; 
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
    padding: 24px 16px;
}

.zoomButtons{
    display: flex;
    flex-direction: row;
    justify-content:flex-end; 
    position: sticky;
    top: 0;
    z-index: 10; 
}

.sectionTitle {
    font-size: 1.15rem;
    font-weight: 600;
    color: #2d3a4a;
    margin: 20px 0 6px 0;
    letter-spacing: 0.01em;
}

.divider {
    border-bottom: 1px solid #e0e0e0;
    margin: 8px 0 12px 0;
}

.inputModern {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    padding: 8px 12px;
    font-size: 1rem;
    transition: border 0.2s;
}
.inputModern:focus {
    border: 1.5px solid #4f8cff;
    outline: none;
    background: #fff;
}

.textareaModern {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    padding: 10px 12px;
    font-size: 1rem;
    min-height: 80px;
    transition: border 0.2s;
}
.textareaModern:focus {
    border: 1.5px solid #4f8cff;
    outline: none;
    background: #fff;
}

.buttonModern {
    border-radius: 8px;
    padding: 10px 28px;
    font-size: 1.08rem;
    font-weight: 600;
    border: none;
    background: linear-gradient(90deg, #4f8cff 60%, #6fc3ff 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(79,140,255,0.08);
    transition: background 0.2s, box-shadow 0.2s;
}
.buttonModern:hover {
    background: linear-gradient(90deg, #357ae8 60%, #4f8cff 100%);
    box-shadow: 0 4px 16px rgba(79,140,255,0.16);
}

.selectModern {
    border-radius: 8px !important;
    min-height: 44px !important;
    font-size: 1rem !important;
    background: #f9fafb !important;
    padding: 2px 0 !important;
}

/* Stronger selector for react-select control to prevent double borders */
.selectModern.select__control {
    border-radius: 8px !important;
    min-height: 44px !important;
    font-size: 1rem !important;
    background: #f9fafb !important;
    border: 1px solid #d1d5db !important;
    box-shadow: none !important;
}
.selectModern.select__control--is-focused {
    border: 1.5px solid #4f8cff !important;
    box-shadow: 0 0 0 1px #4f8cff !important;
}

label {
    padding-left: 5px;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2d3a4a;
    font-size: 1rem;
}

.metaDataContainer {
    padding: 5px 0;
}

.metaDataCard {
    padding: 10px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s, border 0.2s;
    height: 100%;
}

.metaDataCard:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.metaDataInputLabel {
    font-size: 0.9rem;
    color: #4f5d75;
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
}

.metaDataInputField {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: #fff;
    padding: 7px 10px;
    font-size: 0.98rem;
    transition: border 0.2s, box-shadow 0.2s;
    width: 100%;
}

.metaDataInputField:focus {
    border: 1.5px solid #4f8cff;
    outline: none;
    background: #fff;
    box-shadow: 0 0 0 2px rgba(79,140,255,0.1);
}

.noMetaDataMessage {
    text-align: center;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px dashed #d1d5db;
    color: #6b7280;
    font-size: 0.95rem;
}

.modalHeaderModern {
    background: linear-gradient(90deg, #4f8cff 60%, #6fc3ff 100%);
    color: #fff !important;
    border-radius: 12px 12px 0 0;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.01em;
}

.modalFooterModern {
    background: #f8fafc;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e0e0e0;
}