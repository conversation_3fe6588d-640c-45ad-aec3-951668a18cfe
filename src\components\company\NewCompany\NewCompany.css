#layoutAuthentication {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

#layoutAuthentication #layoutAuthentication_content {
  min-width: 0;
  flex-grow: 1;
}
#layoutAuthentication #layoutAuthentication_footer {
  min-width: 0;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.border-0 {
  border: 0 !important;
}

.logoutBtn {
  transition: all 0.3s ease;
}

.logoutBtn:hover {
  transform: scale(1.05);
}

/* View Controls */
.view-controls {
  display: flex;
  align-items: center;
}

.view-controls button {
  transition: all 0.2s ease;
}

.view-controls button:hover {
  transform: translateY(-2px);
}

/* List View Styles */
.list-group-item {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.list-group-item:hover {
  border-left: 3px solid #0d6efd;
  transform: translateX(5px);
}

/* Table View Styles */
.table-responsive {
  border-radius: 0.25rem;
  overflow: hidden;
}

.table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

.table tr {
  transition: all 0.2s ease;
}

/* Tile View Styles */
.company-tile {
  cursor: pointer;
  transition: all 0.3s ease;
  border: none !important;
  box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
}

.company-tile:hover {
  transform: translateY(-5px);
  border-color: #0d6efd;
}

.company-icon {
  background-color: rgba(13, 110, 253, 0.1);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-tile {
  transition: box-shadow 0.3s ease;
  cursor: pointer;
}

.company-tile:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(13, 110, 253, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  border-radius: 8px;
}

.card-container {
  background-color: #f1f1f1;
}

.company-tile:hover .overlay {
  opacity: 1;
}

.eye-icon {
  display: none; /* Hide the eye icon */
  font-size: 24px;
  color: #0d6efd;
}

/* Avatar for company first letter */
.company-avatar {
  min-width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: #fff;
}
.avatar-text {
  font-weight: bold;
  color: #fff;
  font-size: 2rem;
}
/* Example color backgrounds for avatars */
.bg-color-0 { background: #007bff; }
.bg-color-1 { background: #6f42c1; }
.bg-color-2 { background: #e83e8c; }
.bg-color-3 { background: #fd7e14; }
.bg-color-4 { background: #20c997; }
.bg-color-5 { background: #17a2b8; }
.bg-color-6 { background: #ffc107; color: #212529; }
.bg-color-7 { background: #dc3545; }

.card-title {
  font-weight: bold;
  color: #212529;
  margin-top: 0.5rem;
  margin-bottom: 0;
  text-align: center;
  word-break: break-word;
}

/* Consistent alignment for card body */
.card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .view-controls {
    margin-top: 1rem;
  }

  .d-flex.justify-content-between.align-items-center {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .row-cols-md-2 {
    --bs-gutter-x: 0.5rem;
  }
}
