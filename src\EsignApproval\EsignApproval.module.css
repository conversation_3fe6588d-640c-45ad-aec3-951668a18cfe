@import url('https://fonts.googleapis.com/css?family=Cedarville+Cursive');

.letterImg
{
    max-width:80px;
    max-height: 80px;
    margin-left: 380px;
    margin-top: 30px;
}
.fileViewerContainer {
    display: grid;
    place-items: center;
    width: 100%; /* Or set a fixed width */
    height: 100%; /* Or set a fixed height */
  }

  .fileViewer {
    width: 100%;
    height: 100%;
  }

.fileName
{
    font-size: 20px;
    border: 1px solid rgb(184, 183, 183);
    width: 80%;
    margin: 0 auto;
    padding: 10px;
    text-align: left;
}

.fileIcon
{
    font-size: 32px;
    margin: 0px 25px;
    color: orangered;
}

.downloadIcon
{
    font-size: 32px;
    margin-left: 270px;
    color: blue;
}

.Note
{
    margin-left: 90px;
    margin-top: 10px;
}


.customNavTab .nav-link {
    background-color: #f8f9fa; /* Default tab background color */
    padding: 15px 20px; /* Adjust padding for a block look */
    border-radius: 5px;
    margin-right: 10px;
    color: black;
    font-weight: bold;
    transition: background-color 0.3s ease;
  }

  .customNavTab .nav-link.active {
    background-color: #007bff; /* Active tab background color */
    color: white;
    border: none;
  }

  .customNavTab .nav-link:hover {
    background-color: #dcdcdc; /* Hover effect */
    color: black;
  }

.cursive
{
    font-family: 'Cedarville Cursive', cursive;
}

@media (max-width: 500px)
{

    .letterImg
    {
        max-width:80px;
        max-height: 80px;
        margin-left: 180px;
        margin-top: 30px;
    }

    .fileName
    {
        font-size: 18px;
        border: 1px solid rgb(184, 183, 183);
        width: 90%;
        margin: 0 auto;
        padding: 10px;
        text-align: left;
    }

    .fileIcon
    {
        font-size: 26px;
        margin: 0px 25px;
        color: orangered;
    }

    .downloadIcon
    {
        font-size: 26px;
        margin-left: 40px;
        color: blue;
    }

    .Note
    {
        margin-left: 30px;
        margin-top: 10px;
    }
}