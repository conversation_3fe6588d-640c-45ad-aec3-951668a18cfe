import React from 'react';
import { Breadcrumb } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const CustomBreadcrumb = ({ companyName, featureName }) => {
    const navigate = useNavigate();
    return (

        <Breadcrumb>
            {/* <Breadcrumb.Item onClick={() => navigate("/newDS/document-management", { replace: true })} style={{ cursor: 'pointer' }}>{companyName}</Breadcrumb.Item>
            <Breadcrumb.Item href="">
                {featureName}
            </Breadcrumb.Item> */}
            <Breadcrumb.Item onClick={() => window.history.back()} style={{ cursor: 'pointer' }}>Back</Breadcrumb.Item>
        </Breadcrumb>
    );
};

export default CustomBreadcrumb;
