import React, { Component } from "react";
import Loader from "../loader/Loader";
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import DownloadReport from '../common/DownloadReport';
import { getList } from "../../services/apiService";
import { DataTable } from "../Table/DataTable";
import CustomBreadcrumb from "../common/CustomBreadcrumb";

class UsersReport extends Component {
  state = {
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    usersList: [],
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    deployedURL: "",
  };

  componentDidMount() {
    this.fetchUsersList();
    const deployedURL = window.location.href.split('/#')[0];
    this.setState({deployedURL: deployedURL});
  }

  fetchUsersList = async (page = 0, itemsPerPage = this.state.itemsPerPage) => {
    this.setState({ isLoading: true });
    const api = `/user/list?page=${page}&size=${itemsPerPage}&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        usersList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (e) {
      console.log(e);
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchUsersList(page);
  };

  handleItemsPerPageChange = (newSize) => {
    this.setState({ itemsPerPage: newSize }, () => {
      this.fetchUsersList(0, newSize);
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true,
      },
    });
  };

  render() {
    const { usersList, deployedURL, itemsPerPage, currentPage, totalItems } = this.state;
    
    const columns = [
      {
        key: 'index',
        header: 'S.No',
        render: (_, row, index) => currentPage * itemsPerPage + index + 1
      },
      {
        key: 'userName',
        header: 'User Name',
        sortable: true
      },
      {
        key: 'firstName',
        header: 'First Name',
        sortable: true
      },
      {
        key: 'lastName',
        header: 'Last Name',
        sortable: true
      },
      {
        key: 'email',
        header: 'Email',
        sortable: true
      }
    ];

    return (
      <>
        <div className="container mt-3">
          {this.state.notification.show && (
            <Notification
              message={this.state.notification.message}
              type={this.state.notification.type}
              onClose={this.closeNotification}
            />
          )}
          <div className="row align-items-center">
            <div className="col-8">
              <h5 className="pt-1">Users Report</h5>
            </div>
            <div className="col-4 text-end">
              <div className="d-inline-flex align-items-center gap-2">
                <DownloadReport
                  excelEndpoint={`${GlobalConstants.globalURL}/documentreports/usersReport/exportAllUsersExcel`}
                  pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/usersReport/exportAllUsersPDF`}
                  reportName="UsersReport"
                  onError={this.handleDownloadError}
                  buttonStyle={{ background: '#fff', color: '#333', border: '1px solid #ccc', fontSize: '16px' }}
                />
                <button
                  className="btn btn-link"
                  style={{ textDecoration: 'underline', color: '#1976d2', fontWeight: 500, fontSize: '16px' }}
                  onClick={() => window.history.back()}
                >
                  Back
                </button>
              </div>
            </div>
          </div>
          
          <div className="row mt-3">
            <div className="col-12">
              <DataTable
                data={usersList.filter(user => user !== null)}
                columns={columns}
                totalItems={totalItems}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                className="font-weight-normal"
              />
            </div>
          </div>
        </div>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default UsersReport;
