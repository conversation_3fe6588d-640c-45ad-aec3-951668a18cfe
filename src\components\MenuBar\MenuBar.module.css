.myMenu {
  background: linear-gradient(to top, #a3d297, #84c673);
  font-size: 16px;
  font-family: "Verdana";
}

.myCustomNavbar {
  padding-top: 5px;
  height: 60px;
}

.myCustomNavbar img {
  max-height: 40px; /* Adjust logo size if needed */
}

.companyContainer {
  display: flex;
  justify-content:center;
  align-items: center;
  /* width: 100%; */
  flex-grow: 1;
}

.company {
  font-size: 16px;
  font-weight: 800;
  text-align: center;
  padding: 0;
}

.positionMenu {
  position: "relative";
  left: -30px;
  top: 5px;
}

@media (max-width: 1400px) {
  .company {
    margin-left: 4rem;
    font-size: 20px;
  }
}

@media (max-width: 1200px) {
  .company {
    margin-left: 2rem;
    font-size: 14px;
  }
}

@media (max-width: 500px) {
  .positionMenu {
    position: "relative";
    left: 0px;
    top: 5px;
  }

  .company {
    margin-left: 0;
    font-size: 14px;
  }

  .myCustomNavbar {
    padding-top: 5px;
    height: auto;
  }
}

@media (max-width: 992px) {
  .positionMenu {
    position: "relative";
    left: 0px;
    top: 5px;
  }

  .company {
    margin-left: 0;
    font-size: 14px;
  }

  .myCustomNavbar {
    padding-top: 5px;
    height: auto;
  }
}
