import React, { useState } from 'react';
import './TopMenu.css';
import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import NavDropdown from 'react-bootstrap/NavDropdown';
import { Modal, Form, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router';
import axios from 'axios';
import { GlobalConstants } from '../../constants/global-constants';
import { NavLink } from 'react-router-dom';


function TopMenu(props) {
  const [showModal, setShowModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword,setShowCurrentPassword]=useState(false);
  const [showNewPassword,setShowNewPassword]=useState(false);
  const [showConfirmPassword,setShowConfirmPassword]=useState(false);
  const [isPasswordMissMatch,setIsPasswordMissMatch] =useState(false);
  const [result,setResult]=useState(false);
  const [role,setRole]=useState(localStorage.getItem("role"));
  const [userName,setUserName]=useState(localStorage.getItem("userName"));

  const [isUserManagementOpen, setIsUserManagementOpen] = useState(false);
  const [isConfigurationOpen, setIsConfigurationOpen] = useState(false);
  const [isReportsOpen, setIsReportsOpen] = useState(false);

  const handleShow = () => setShowModal(true);
  const handleClose = () => {
    setShowModal(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
  };
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Current Password:', currentPassword);
    console.log('New Password:', newPassword);
    console.log('Confirm New Password',confirmPassword)
    if(newPassword!==confirmPassword){
      setIsPasswordMissMatch(true);
      setConfirmPassword('');
      return;
    }
    changePassword();
  };
  const changePassword=async ()=>{
    const existingPassword=currentPassword;
    let api = `${GlobalConstants.globalURL}/user/user_change_password/${localStorage.getItem("id")}`;
    try{
      const data=await axios.get(api,{params:{existingPassword,newPassword}})
      handleClose();
      navigate("/");
    }
    catch(error){
      setResult(true);
    }
  }

  return (<>
   
   <Navbar collapseOnSelect expand="lg" className="myMenu flex-column black-navbar">
      <Container>
        
        {/* Toggle Button for Mobile Devices */}
        <Navbar.Toggle aria-controls="basic-navbar-nav" 
        style={{ backgroundColor: 'white', border: 'none', color: 'black' }} />

        {/* Collapse Menu */}
        <Navbar.Collapse id="basic-navbar-nav">
        {/* User Management Dropdown */}
        <Nav className="flex-column me-auto">

          {/* Document Management */}
          <Nav.Link as={NavLink} to="/newDS/document-management" className="nav-link"  
          style={{color:'white'}}>
              Inbox <i className="fa fa-envelope" aria-hidden="true"></i>&nbsp;<sup>{props.count}</sup>
          </Nav.Link>

          {/* Teams */}
          <Nav.Link as={NavLink} to="/newDS/team" className="nav-link"  
          style={{color:'white'}} >
              Teams
          </Nav.Link>

         { role=='ROLE_SUPER_ADMIN' && <Nav.Link className="nav-link"  
          style={{color:'white'}} >
              Admin tools ----------------
          </Nav.Link>}

          { role=='ROLE_SUPER_ADMIN' &&  <Nav.Link as={NavLink} to="/newDS/home" className="nav-link"  
          style={{color:'white'}}>
              <span className="fa fa-dashboard"></span> Dashboard
          </Nav.Link>}

          { role=='ROLE_SUPER_ADMIN' && <div className="nav-item">
            <a
              className="nav-link d-flex justify-content-between align-items-center"
              onClick={() => setIsReportsOpen(!isReportsOpen)}
              style={{ cursor: 'pointer' }}
            >
              <span className="fa fa-flag"></span> Reports
              <i className={`fa fa-chevron-${isReportsOpen ? 'up' : 'down'}`}></i>
            </a>
            {isReportsOpen && (
              <div>
                <Nav className="flex-column">
                  {/* <Nav.Link href="#">Document Types</Nav.Link>
                  <Nav.Link href="#">Users Report</Nav.Link>
                  <Nav.Link href="#">Specific Doc. Log Report</Nav.Link>
                  <Nav.Link href="#">Workflows Report</Nav.Link> */}
                  <Nav.Link as={NavLink} to="/newDS/archivedReport">Archived Docs Report</Nav.Link>
                </Nav>
              </div>
            )}
          </div>}

          { role=='ROLE_SUPER_ADMIN' && <div className="nav-item">
            <a
              className="nav-link d-flex justify-content-between align-items-center"
              onClick={() => setIsUserManagementOpen(!isUserManagementOpen)}
              style={{ cursor: 'pointer' }}
            >
              <span className="fa fa-users"></span>&nbsp; User Management
              &nbsp;<i className={`fa fa-chevron-${isUserManagementOpen ? 'up' : 'down'}`}></i>
            </a>
            {isUserManagementOpen && (
              <div>
                <Nav className="flex-column">
                  <Nav.Link as={NavLink} to="/newDS/createUser" className="dropdown-item">Manage Users</Nav.Link>
                  <Nav.Link as={NavLink} to="/newDS/password" className="dropdown-item">Password Management</Nav.Link>
                  <Nav.Link as={NavLink} to="/newDS/createRole" className="dropdown-item">Roles Management</Nav.Link>
                  <Nav.Link as={NavLink} to="/newDS/permissionManagement" className="dropdown-item">Permission Management</Nav.Link>
                  <Nav.Link as={NavLink} to="/newDS/teams" className="dropdown-item">Manage User Teams</Nav.Link>
                </Nav>
              </div>
            )}
          </div>}

          { role=='ROLE_SUPER_ADMIN' && <Nav.Link as={NavLink} to="/newDS/recyclebin" className="nav-link"  
          style={{color:'white'}}>
              <span className="fa fa-trash"></span> Recycle Bin
          </Nav.Link>}

          { role=='ROLE_SUPER_ADMIN' && 
          <Nav.Link className="nav-link"  
                    style={{color:'white'}}
                    as={NavLink} to="/newDS/metadata">
              <span className="fa fa-table"></span> Metadata
          </Nav.Link>}

          { role=='ROLE_SUPER_ADMIN' && 
          <Nav.Link className="nav-link"  
                    style={{color:'white'}}
                    as={NavLink} to="/newDS/numbering">
              <span className="fa fa-list-ol"></span> Numbering
          </Nav.Link>}
          
          { role=='ROLE_SUPER_ADMIN' && <Nav.Link as={NavLink} to="/newDS/auditlog" className="nav-link"  
          style={{color:'white'}}>
              <span className="fa fa-asterisk"></span> Audit Log
          </Nav.Link>}

          {/* Configuration Dropdown */}
          { role=='ROLE_SUPER_ADMIN' && <div className="nav-item">
            <a
              className="nav-link d-flex justify-content-between align-items-center"
              onClick={() => setIsConfigurationOpen(!isConfigurationOpen)}
              style={{ cursor: 'pointer' }}
            >
              <span className="fa fa-cogs"></span> Configuration
              <i className={`fa fa-chevron-${isConfigurationOpen ? 'up' : 'down'}`}></i>
            </a>
            {isConfigurationOpen && (
              <div>
                <Nav className="flex-column">
                  <Nav.Link as={NavLink} to="/newDS/company" className="dropdown-item">Company</Nav.Link>
                </Nav>
              </div>
            )}
          </div>}

          {/* Reports Dropdown */}
          
        </Nav>
        </Navbar.Collapse>
      </Container>
   </Navbar>



    <Modal show={showModal} onHide={handleClose} size="md" centered className='p-3'>
      <Modal.Header closeButton className='bg-info'>
        <Modal.Title>Change Password</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showCurrentPassword?'text':'password'}
              placeholder="Enter current password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
            <Button onClick={()=>setShowCurrentPassword(!showCurrentPassword)}>
              {showCurrentPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            </div>
            <br></br>
          </Form.Group>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showNewPassword?'text':'password'}
              placeholder="Enter new password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
            <Button onClick={()=>setShowNewPassword(!showNewPassword)}>
              {showNewPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            </div>
          </Form.Group>
            <br></br>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showConfirmPassword?'text':'password'}
              placeholder="Re-enter new password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
            <Button onClick={()=> setShowConfirmPassword(!showConfirmPassword)} >
              {showConfirmPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            {isPasswordMissMatch && <p className='text-danger'>Please Confirm New Password</p> }
            {result && <p className='text-danger'>Current Password does not match with DB</p> }
          </div>
          </Form.Group><br></br>
          <Button variant="primary" type="submit" >
            Submit
          </Button>
        </Form>
      </Modal.Body>
    </Modal></>
  );
}

export default TopMenu;
