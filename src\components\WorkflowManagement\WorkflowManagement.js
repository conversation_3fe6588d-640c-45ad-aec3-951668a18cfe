import React, { useState, useRef, useEffect } from 'react';
import { Alert } from 'react-bootstrap';
import DiagramEditor from '../DiagramEditor/DiagramEditor';
import Notification from '../Notification/Notification';
import { useLocation } from 'react-router-dom';

const WorkflowManagement = () => {
  const location = useLocation();
  const workflowData = location.state?.workflowData;
  
  // State for alerts
  const [alert, setAlert] = useState({ show: false, message: '', variant: 'info' });

  // State for notifications
  const [notification, setNotification] = useState({ message: '', type: '', show: false });

  // Ref for DiagramEditor to access save functionality
  const diagramEditorRef = useRef(null);

  // Show alert helper
  const showAlert = (message, variant = 'info') => {
    setAlert({ show: true, message, variant });
    setTimeout(() => setAlert({ show: false, message: '', variant: 'info' }), 5000);
  };

  // Helper to show notification (toast)
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type, show: true });
    setTimeout(() => setNotification({ message: '', type: '', show: false }), 3000);
  };

  // Handle save workflow
  const handleSave = () => {
    if (diagramEditorRef.current && diagramEditorRef.current.saveWorkflow) {
      diagramEditorRef.current.saveWorkflow();
    }
  };

  // Handle cancel workflow creation
  const handleCancel = () => {
    showNotification('Workflow creation cancelled', 'success');
  };

  // Handle workflow creation success
  const handleWorkflowSaved = () => {
    showAlert('Workflow saved successfully!', 'success');
    showNotification('Workflow created successfully!', 'success');
  };



  return (
    <div className="">
      {/* Toast Notification */}
      {notification.show && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification({ ...notification, show: false })}
        />
      )}

       {/* Header with workflow name if editing */}
       {/* {workflowData && (
        <div className="row mb-2">
          <div className="col-12">
            <h4 className="mb-0">Editing Workflow: {workflowData.name}</h4>
            <p className="text-muted mb-1">
              Type: {workflowData.workflow_type} | Created: {new Date(workflowData.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      )} */}
      
      {/* Header */}
      {/* <div className="row mb-2">
        <div className="col-12">
          <h4 className="mb-0">Create Workflow</h4>
          <p className="text-muted mb-1">
            Create and design your workflow processes
          </p>
        </div>
      </div> */}

      {/* Alert */}
      {alert.show && (
        <Alert variant={alert.variant} dismissible onClose={() => setAlert({ ...alert, show: false })} className="mb-2">
          {alert.message}
        </Alert>
      )}

      {/* Workflow Editor */}
      <div className="row">
        <div className="col-12 px-0">
        <div style={{ 
      height: 'calc(130vh - 200px)', 
      border: '1px solid #dee2e6', 
      borderRadius: '8px',
      marginLeft: '-15px',  // Counteracts Bootstrap's default row padding
      marginRight: '-15px', // Counteracts Bootstrap's default row padding
      width: 'calc(100% + 30px)' // Extends width to compensate for negative margins
    }}>
            <DiagramEditor 
              ref={diagramEditorRef}
              embedded={true} 
              onWorkflowSaved={handleWorkflowSaved}
              onSave={handleSave}
              onCancel={handleCancel}
              initialWorkflowData={workflowData} 
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowManagement; 