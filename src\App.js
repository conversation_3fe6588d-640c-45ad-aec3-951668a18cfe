import './App.css';
import './components/common/ModalStyles.css';
import Login from './components/login/Login';
import {Routes,Route} from 'react-router-dom';
import SelectCompany from './components/company/select-company/SelectCompany';
import ForgotPassword from './components/forgotPassword/ForgotPassword';
import CreateUser from './components/CreateUser/CreateUser';
import PermissionManagement from './components/permissionManagement/PermissionManagement';
import TeamsManagement from './components/teamsManagement/TeamsManagement';
import CreateRole from './components/CreateRole/CreateRole';
import Home from './components/Home/Home';
import PasswordManagement from './components/PasswordManagement/PasswordManagement';
import CreateCompany from './components/company/CreateCompany/CreateCompany';
import WorkFlow from './components/WorkFlow/WorkFlow';
import LogManagement from './components/LogManagement/LogManagement';
import Sla from './components/Sla/Sla';
import DocumentManagement from './components/DocumentManagement/DocumentManagement';
import EmailApproval from './components/EmailApproval/EmailApproval';
import EmailAcknowledge from './components/EmailAcknowledgement/EmailAcknowledge';
import Teams from './components/Teams/Teams';
import RecyleBin from './components/RecyleBin/RecyleBin';
import AuditLog from './components/AuditLog/AuditLog';
import Numbering from './components/Numbering/Numbering';
import { useState } from 'react';
import EsignApproval from './EsignApproval/EsignApproval';
import ProtectedRoute from './ProtectedRoute';
import Metadata from './components/Metadata/Metadata';
import DuplicatesReport from './components/Reports/DuplicatesReport';
import PendingWorkFlows from './components/Reports/PendingWorkFlows';
import RetentionReport from './components/Reports/RetentionReport';
import DuedateReport from './components/Reports/DuedateReport';
import AllFilesReport from './components/Reports/AllFilesReport';
import ArchivedReport from './components/Reports/ArchivedReport';
import FileUpdate from './components/DocumentManagement/FileUpdate';
import NewDashboard from './components/NewDashboard/NewDashboard';
import NewCompany from './components/company/NewCompany/NewCompany';
import SpecificDocLogReport from './components/Reports/SpecificDocLogReport';
import UsersReport from './components/Reports/UsersReport';
import Email from './components/Notification/Email';
import FileFormate from './components/Notification/FileFormate';
import SMS from './components/Notification/SMS';
import NotificationSettings from './components/Notification/NotificationSettings';
import AdvanceSearch from './components/DocumentManagement/AdvanceSearch';
import Tags from './components/Tags/Tags';
import Archived from './components/Archived/Archived';
import CreateDashboard from './components/CreateDashboard/CreateDashboard';
import DashboardView from './components/DashboardView/DashboardView';
import DashboardManagement from './components/DashboardManagement/DashboardManagement';
import WorkflowManagement from './components/WorkflowManagement/WorkflowManagement';
import Workflows from './components/WorkFlow/Workflows';

function App() {
  const [uploadedFiles, setUploadedFiles] = useState();
  return (
    <div className="App">
     <Routes>      
        <Route path="/" element={<Login />} />        
        <Route path="/forgot" element={<ForgotPassword />}  />          
        <Route path="/select-company" element={<ProtectedRoute><SelectCompany /> </ProtectedRoute>} />   
        <Route path="/new-company" element={<NewCompany />} />
        <Route path="/newDS/" element={<ProtectedRoute><NewDashboard count={uploadedFiles} /></ProtectedRoute>}>
              <Route path="home" element={<Home />}>                 
              </Route>
              <Route path="createUser" element={<CreateUser />}></Route>
              <Route path="password" element={<PasswordManagement />} />
              <Route path="createRole" element={<CreateRole />} />
              <Route path="permissionManagement" element={<PermissionManagement />} />
              <Route path='teams' element={<TeamsManagement />}/> 
              <Route path='company' element={<CreateCompany/>}/>
              <Route path='workflow' element={<WorkFlow />}/>
              <Route path='log' element={<LogManagement />}/>
              <Route path='sla' element={<Sla />} />
              <Route path="document-management" element={<DocumentManagement setUploadedFiles={setUploadedFiles}/>}>
              </Route>
              <Route path="team" element={<Teams />} />
              <Route path="recyclebin" element={<RecyleBin />} />
              <Route path="archived" element={<Archived />} />
              <Route path="auditlog" element={<AuditLog />} />
              <Route path="emailApproval" element={<EmailApproval />} />
              <Route path="emailAcknowledge" element={<EmailAcknowledge />} />
              <Route path="esignApproval" element={<EsignApproval />} />
              <Route path="numbering" element={<Numbering />} />
              <Route path="metadata" element={<Metadata />} />
              <Route path="tags" element={<Tags/>} />
              <Route path="workflow-management" element={<WorkflowManagement />} />
              <Route path="workflows" element={<Workflows />} />

              <Route path="duplicatesReport" element={<DuplicatesReport />} />
              <Route path="pendingWorkFlows" element={<PendingWorkFlows />} />
              <Route path="retentionReport" element={<RetentionReport />} />
              <Route path="duedateReport" element={<DuedateReport />} />
              <Route path="allfilesReport" element={<AllFilesReport />} />
              <Route path="archivedReport" element={<ArchivedReport />} />
              <Route path="usersReport" element={<UsersReport />} />
              <Route path="specificDocLogReport" element={<SpecificDocLogReport />} />
              <Route path="fileUpdate" element={<FileUpdate />} />
              <Route path="email" element={<Email />} />
              <Route path="fileformate" element={<FileFormate />} />
              <Route path="SMS" element={<SMS />} />
              <Route path="notificationSettings" element={<NotificationSettings />} />
              <Route path="advancedSearch" element={<AdvanceSearch />}></Route>
              <Route path="createDashboard" element={<CreateDashboard />} />
              <Route path="dashboardView" element={<DashboardView />} />
              <Route path="dashboardManagement" element={<DashboardManagement />} />
        </Route> 
        {/* <Route path="/dashboard/" element={<ProtectedRoute><Dashboard count={uploadedFiles} /></ProtectedRoute>}>
              <Route path="home" element={<Home />}>                 
              </Route>
              <Route path="createUser" element={<CreateUser />}></Route>
              <Route path="password" element={<PasswordManagement />} />
              <Route path="createRole" element={<CreateRole />} />
              <Route path="permissionManagement" element={<PermissionManagement />} />
              <Route path='teams' element={<TeamsManagement />}/> 
              <Route path='company' element={<CreateCompany/>}/>
              <Route path='workflow' element={<WorkFlow />}/>
              <Route path='log' element={<LogManagement />}/>
              <Route path='sla' element={<Sla />} />
              <Route path="document-management" element={<DocumentManagement setUploadedFiles={setUploadedFiles}/>}>
              </Route>
              <Route path="team" element={<Teams />} />
              <Route path="recyclebin" element={<RecyleBin />} />
              <Route path="auditlog" element={<AuditLog />} />
              <Route path="emailApproval" element={<EmailApproval />} />
              <Route path="emailAcknowledge" element={<EmailAcknowledge />} />
              <Route path="esignApproval" element={<EsignApproval />} />
              <Route path="numbering" element={<Numbering />} />
              <Route path="metadata" element={<Metadata />} />

              <Route path="duplicatesReport" element={<DuplicatesReport />} />
              <Route path="pendingWorkFlows" element={<PendingWorkFlows />} />
              <Route path="retentionReport" element={<RetentionReport />} />
              <Route path="duedateReport" element={<DuedateReport />} />
              <Route path="allfilesReport" element={<AllFilesReport />} />
              <Route path="archivedReport" element={<ArchivedReport />} />
              <Route path="usersReport" element={<AllFilesReport />} />
              <Route path="specificDocLogReport" element={<AllFilesReport />} />
              <Route path="fileUpdate" element={<FileUpdate />} />
        </Route>  */}
     </Routes>        
    </div>
  );
}

export default App;
