.linkItems {
    display: flex;
    width: 100%;
    height: 100%;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    align-items: center;
    gap: 10px;
}

.linkItems:hover {
    text-decoration: none;
    color: var(--primary-color);
}

.cardLink {
    display: inline-block;
    margin-top: 0.75rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 2px;
    transition: all 0.2s ease;
}

.cardLink::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.2s ease;
}

.cardLink:hover {
    color: var(--secondary-color);
}

.cardLink:hover::after {
    width: 100%;
}

/* Tab styles */
.tabContent {
    padding: 1.5rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 0.75rem 0.75rem;
}

/* Table styles */
.tableContainer {
    overflow-x: auto;
    margin-bottom: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.tableActions {
    display: flex;
    gap: 0.5rem;
}

/* Status indicators */
.statusBadge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
}

.statusBadge i {
    margin-right: 0.35rem;
    font-size: 0.7rem;
}

.statusPending {
    background-color: rgba(251, 191, 36, 0.15);
    color: var(--warning-color);
}

.statusApproved {
    background-color: rgba(74, 222, 128, 0.15);
    color: var(--success-color);
}

.statusRejected {
    background-color: rgba(248, 113, 113, 0.15);
    color: var(--danger-color);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

.iconContainer {
    position: absolute;
    top: 15px;  /* Changed from -20px to ensure icons are fully visible */
    right: 20px;
    padding: 12px;  /* Increased padding to give more space for the icon */
    border-radius: 6px;
    display: flex;  /* Ensure proper centering of the icon */
    align-items: center;
    justify-content: center;
    width: 45px;  /* Set fixed width */
    height: 45px;  /* Set fixed height */
}

.dashboardCard {
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
    overflow: hidden;
    height: 100%;
}

.dashboardCard:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.statLabel {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.statValue {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.chartContainer {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    height: 100%;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.chartHeader {
    margin-bottom: 1.25rem;
    text-align: center;
}

.chartTitle {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}
