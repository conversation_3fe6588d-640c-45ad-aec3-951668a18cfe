.myMenu {
  min-width: 100%;
  min-height: 100vh;
  position: fixed;
  background-color: #f1f1f1;
  padding-top: 20px;
}

.nav-item {
  margin-bottom: 10px;
  color: #fff !important;
}

.nav-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  color: #000;
  text-decoration: none;
  font-weight: 600;
  white-space: normal;
  overflow-wrap: break-word;
}

.nav-link:hover {
  /* background-color: #5c98d4; */
}

.nav-item .dropdown-item {
  padding-left: 40px;
}

.fa-chevron-down, .fa-chevron-up {
  transition: transform 0.3s ease;
}

.dropdown-item {
  padding-left: 40px;  /* Indent the dropdown items */
}


.black-navbar {
  background-color: #242424;
  color: white;
}

.black-navbar .navbar-brand,
.black-navbar .nav-link,
.black-navbar .nav-item a,
.black-navbar .dropdown-item {
  color: white !important;
}


/* .black-navbar .navbar-brand:hover,
.black-navbar .nav-link:hover,
.black-navbar .nav-item a:hover,
.black-navbar .dropdown-item:hover
{
  color: #5fb1dd !important; 
} */

@media (max-width: 500px) {
  .myMenu {
    min-width: 100%;  /* Sidebar width for larger screens */
    min-height: auto;
  }
}

@media (max-width: 600px) {
  .myMenu {
    min-width: 100%;  /* Sidebar width for larger screens */
    min-height: auto;
  }
}