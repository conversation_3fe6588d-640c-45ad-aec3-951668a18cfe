import React, { Component } from "react";
import { uploadDocument, getList } from "../../services/apiService";
import { Button, Dropdown, Modal } from "react-bootstrap";
import classes from "./MainSection.module.css";
import Notification from "../Notification/Notification";
import { GlobalConstants } from "../../constants/global-constants";
import axios from '../../services/api'

class MainSection extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showDropdown: false,
      notification: {
        message: "",
        type: "",
        show: false,
      },
      fileId: null,
      isDeleteModalOpen: false,
      uploadedFileName:'',
    };
  }

  toggleDropdown = () => {
    this.setState((prevState) => ({ showDropdown: !prevState.showDropdown }));
  };

  fileInputRef = React.createRef();

  upload = () => {
    this.fileInputRef.current.click();
  };

  handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // const fileData = {
      // slno: this.state.uploadedFiles.length + 1,
      // name: file.name,
      // date: new Date().toLocaleString(),
      // url: URL.createObjectURL(file),
      // };
      // this.setState((prevState) => ({
      // uploadedFiles: [...prevState.uploadedFiles, fileData],
      // }));
      // event.target.value = null;
      this.setState({ uploadedFileName: file.name }); // Store the filename in state
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentName", file.name);
      formData.append("nodeId", 0);
      // formData.append('documentId', this.documentForm.value.documentType);
      formData.append("employeeId", localStorage.getItem("id"));
      this.uploadDocument(formData);
    }
  };

  uploadDocument = async (docData) => {
    const api = "/documentsattachmentdetail/saveDocument";
    try {
     
      const response = await uploadDocument(api, docData);
      this.setState((prevState)=>({
        notification: {
          message: `${prevState.uploadedFileName} uploaded successfully!`,
          type: "success",
          show: true,
        },
      }));
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      //throw error;
    }
  };

  handleSelect = (eventKey) => {
    console.log(`Selected: ${eventKey}`);
    this.setState({ showDropdown: false });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openDeleteModal = () => {
    this.setState({ isDeleteModalOpen: true });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false });
  };

  componentDidMount() {
    this.setState({ fileId: sessionStorage.getItem("fileIdToDelete") });
  }

  componentWillUnmount() {
    sessionStorage.setItem("fileIdToDelete", null);
  }

  deleteFile = async () => {
    let api = `${
      GlobalConstants.globalURL
    }/documentsattachmentdetail/${sessionStorage.getItem("fileIdToDelete")}`;
    try {
      const response = axios.delete(api);
      this.setState({
        notification: {
          message: "File Deleted Successfully",
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
    this.closeDeleteModal();
  };

  permanentDeleteFile = async () => {
    let api = `${
      GlobalConstants.globalURL
    }/documentsattachmentdetail/perminentdelete/${sessionStorage.getItem("fileIdToDelete")}`;
    try {
      const response = axios.delete(api);
      this.setState({
        notification: {
          message: "File Deleted Successfully",
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
    this.closeDeleteModal();
  };

  openWordDocument = () => {
    const wordUrl = 'https://office.live.com/start/Word.aspx';
    window.open(wordUrl, '_blank');
  };

  render() {
    return (
      <div className={`${classes.bgTop}`}>
        <div className="container-fluid py-1">
          <div className="row">
            <div className="col-sm-2 d-sm-block d-none">
              <h4 style={{ paddingTop: "10px" }}> Main Sections</h4>
            </div>
            <div className="col-sm-4 offset-sm-1 text-start">
              <div className="d-flex justify-content-between">
                <div className="text-center" title="folder">
                  <i title="folder" className="fa fa-folder-o" style={{fontSize:'24px'}} aria-hidden="true"></i>
                  <br />
                  <span style={{fontSize:'12px'}}>Folder</span>
                </div>
                <div className="text-center" title="audit log">
                  <i className="fa fa-file-text-o"  style={{fontSize:'24px'}} aria-hidden="true"></i>
                  <br />
                    <span style={{fontSize:'12px'}}>Audit Log</span>
                </div>
                {this.state.fileId && (
                  <div title="delete file"
                    className="text-center"
                    style={{ cursor: "pointer" }}
                    onClick={this.openDeleteModal}
                  >
                    <i className="fa fa-trash"  style={{fontSize:'24px'}} aria-hidden="true"></i>
                    <br />
                      <span style={{fontSize:'12px'}}>Delete File</span>
                  </div>
                )}
              </div>
            </div>

            <div className="col-sm-4 offset-sm-1">
              <div className={`${classes.uploadDiv} pt-2`} >
                <Button variant="primary" onClick={this.upload} title="upload">
                  Upload
                </Button>
                <input
                  type="file"
                  ref={this.fileInputRef}
                  style={{ display: "none" }}
                  onChange={this.handleFileChange}
                />
                <Dropdown
                  show={this.state.showDropdown}
                  onToggle={this.toggleDropdown}
                >
                  <Dropdown.Toggle
                    variant="secondary"
                    id="dropdown-basic"
                    className="bg-primary"
                  ></Dropdown.Toggle>
                  <Dropdown.Menu className="modal-header-modern" align="end">
                    <Dropdown.Item eventKey="1" onSelect={this.handleSelect} onClick={this.upload}>
                      Upload a File
                    </Dropdown.Item>
                    <Dropdown.Item eventKey="2" onSelect={this.handleSelect} onClick={this.upload}>
                      Add a Link
                    </Dropdown.Item>
                    <Dropdown.Item eventKey="3" onSelect={this.handleSelect} onClick={this.openWordDocument}>
                      New Word Document
                    </Dropdown.Item>
                    <Dropdown.Item eventKey="4" onSelect={this.handleSelect} onClick={this.upload}>
                      New Excel Spreadsheet
                    </Dropdown.Item>
                    <Dropdown.Item eventKey="5" onSelect={this.handleSelect} onClick={this.upload}>
                      New PowerPoint Presentation
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
                <input
                  title="search"
                  type="search"
                  className="form-control ms-2"
                  placeholder="Search docs,tags,etc.."
                  style={{ height: "40px"}}
                />
                <i
                  className={`fa fa-search ${classes.searchIcon}`}
                  aria-hidden="true"
                ></i>
              </div>
            </div>
          </div>
        </div>
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        {/* {Delete File Modal} */}
        <Modal
          show={this.state.isDeleteModalOpen}
          onHide={this.closeDeleteModal}
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>Delete File</Modal.Title>
          </Modal.Header>
          <Modal.Body>Do you want to delete the file?</Modal.Body>
          <Modal.Footer>
            <Button variant="danger" onClick={this.deleteFile}>
              Delete
            </Button>&nbsp;
            <Button variant="danger" onClick={this.permanentDeleteFile}>
              Permenent Delete
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

export default MainSection;
