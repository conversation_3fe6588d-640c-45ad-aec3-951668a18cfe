import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import classes from "./CreateRole.module.css";
import {
  getList,
  addNew,
  editById,
  deleteById,
} from "../../services/apiService";
import axios from "../../services/api";
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import { DataTable } from "../Table/DataTable";
import Loader from "../loader/Loader";

export class CreateRole extends Component {
  state = {
    isLoading: false,
    isOpen: false,
    isEditing: false,
    isRoleExist: false,
    rolesList: [],
    newRole: {
      roleName: "",
      roleDescreption: "",
    },
    showDeleteConfirm: false,
    roleIdToDelete: null,
    showDetailsModal: false,
    roleDetails: null,
    permissions: [],
    assignedPermissions: [],
    selectedPermissions: [],
    selectedAvailablePermissions: [],
    selectedAssignedPermissions: [],
    selectAllAvailable: false,
    selectAllAssigned: false,
    roleId: null,
    searchQuery: "",
    searchAssignedQuery: "",
    notification: {
      message: "",
      type: "",
      show: false,
    },
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    searchParam: "",
  };
  timer=null;

  componentDidMount() {
    this.fetchRolesList();
  }
  handleSearchChange = (e) => {
    const { value } = e.target;
    this.setState({ searchQuery: value });
  };
  handleAssignedSearchChange = (e) => {
    const { value } = e.target;
    this.setState({ searchAssignedQuery: value });
  };

  getFilteredPermissions = () => {
    const { permissions, searchQuery } = this.state;
    if (!searchQuery) return permissions;
    return permissions.filter((permission) =>
      permission.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  getFilteredAssignedPermissions = () => {
    const { assignedPermissions, searchAssignedQuery } = this.state;
    if (!searchAssignedQuery) return assignedPermissions;
    return assignedPermissions.filter((permission) =>
      permission.toLowerCase().includes(searchAssignedQuery.toLowerCase())
    );
  };

  fetchRolesList = async (page = 0) => {
    const { itemsPerPage } = this.state;
    const api = `/roles/list?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        rolesList: data.content,
        currentPage: page,
        totalItems: data.totalElements,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  check = async (roleName) => {
    if (!roleName.trim()) return; 
    this.setState({ isRoleExist: false });
    const api = `/roles/validateRole?roleName=${roleName}`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({ isRoleExist: response });
    } catch (error) {}
  };
  handleRoleChange = (e) => {
    const roleValue = e.target.value;
    this.setState((prevState) => ({
      newRole: {
        ...prevState.newRole,
        roleName: roleValue,
      },
    }));
    if(this.timer) clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.check(roleValue);
    }, 500); 
  };

  handleInputChange = (e) => {
    const { name, value } = e.target;
    this.setState((prevState) => ({
      newRole: {
        ...prevState.newRole,
        [name]: value,
      },
    }));
  };

  createRole = async (newRole) => {
    this.setState({ isLoading: true });
    const api = "/roles";
    try {
      const response = await addNew(api, newRole);
      this.setState({
        isLoading:false,
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      console.error(
        "Error creating role:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        isLoading:false,
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  editRole = async (roleId, updatedRole) => {
    this.setState({ isLoading: true });
    const api = `/roles/${roleId}`;
    try {
      const response = await editById(api, updatedRole);
      this.setState({
        isLoading:false,
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      console.error(
        "Error updating role:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        isLoading:false,
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();
    const { newRole, isEditing } = this.state;

    try {
      if (isEditing) {
        await this.editRole(newRole.roleId, newRole);
      } else {
        await this.createRole(newRole);
      }
      this.fetchRolesList();
      this.closeModal();
    } catch (error) {
      console.log(error);
    }
  };

  openDeleteConfirm = (roleId) => {
    this.setState({ showDeleteConfirm: true, roleIdToDelete: roleId });
  };

  closeDeleteConfirm = () => {
    this.setState({ showDeleteConfirm: false, roleIdToDelete: null });
  };

  confirmDeleteRole = async () => {
    const { roleIdToDelete } = this.state;
    const api = `/roles/${roleIdToDelete}`;
    try {
      const response = await deleteById(api);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.fetchRolesList();
    } catch (error) {
      console.error(
        "Error deleting role:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    } finally {
      this.closeDeleteConfirm();
    }
  };

  openModal = (role = null) => {
    if (role) {
      this.setState({
        isOpen: true,
        isEditing: true,
        newRole: { ...role },
      });
    } else {
      this.setState({
        isOpen: true,
        isEditing: false,
        newRole: {
          roleName: "",
          roleDescreption: "",
        },
      });
    }
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      isEditing: false,
      newRole: {
        roleName: "",
        roleDescreption: "",
      },
    });
  };
  viewPermissions = async (role) => {
    let api = `${GlobalConstants.globalURL}/roles/${role.roleId}`;
    try {
      const data = await axios.get(api);
      const roleData = data.data.data;

      this.setState({
        permissions: roleData.restircted || [], // Update available permissions if any
        assignedPermissions: roleData.permissions || [], // Update assigned permissions
        selectedAvailablePermissions: [],
        selectedAssignedPermissions: [],
        selectAllAvailable: false,
        selectAllAssigned: false,
        roleId: role.roleId,
      });
    } catch (err) {
      console.log(err);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  viewModal = (role) => {
    this.viewPermissions(role);
    this.setState({
      showDetailsModal: true,
      roleDetails: role,
    });
  };

  closeDetailsModal = () => {
    this.setState({ showDetailsModal: false, roleDetails: null });
  };

  updatePermissions = async () => {
    console.log("Update Permissions called");
    const { assignedPermissions, permissions, roleId } = this.state;
    console.log("Role ID:", roleId);
    console.log("Available Permissions:", permissions);
    console.log("Assigned Permissions:", assignedPermissions);

    let api = `${GlobalConstants.globalURL}/roles/${roleId}`;

    try {
      const body = {
        roleId: roleId,
        rolePermissions: assignedPermissions.join(","), // Send assignedPermissions as a comma-separated string
        permissions: assignedPermissions, // This should ideally be the list of available permissions
        restircted: permissions, // Sending assignedPermissions as restricted
      };

      console.log(body);
      const response = await axios.put(api, body);
      console.log("Response from API:", response.data);

      // Fetch updated permissions after the update
      await this.viewPermissions({ roleId }); // You can pass a mock role object or create a function to get it

      this.setState({
        notification: {
          message: response.data.message,
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
    this.closeDetailsModal();
  };

  handleAvailableCheckboxChange = (permissionName) => {
    this.setState((prevState) => {
      const selectedAvailablePermissions =
        prevState.selectedAvailablePermissions.includes(permissionName)
          ? prevState.selectedAvailablePermissions.filter(
              (name) => name !== permissionName
            )
          : [...prevState.selectedAvailablePermissions, permissionName];

      return { selectedAvailablePermissions };
    });
  };

  handleAssignedCheckboxChange = (permissionName) => {
    this.setState((prevState) => {
      const selectedAssignedPermissions =
        prevState.selectedAssignedPermissions.includes(permissionName)
          ? prevState.selectedAssignedPermissions.filter(
              (name) => name !== permissionName
            )
          : [...prevState.selectedAssignedPermissions, permissionName];

      return { selectedAssignedPermissions };
    });
  };

  moveSelectedToAssigned = () => {
    this.setState((prevState) => {
      const newAssigned = [
        ...prevState.assignedPermissions,
        ...prevState.selectedAvailablePermissions,
      ];
      const newPermissions = prevState.permissions.filter(
        (permission) =>
          !prevState.selectedAvailablePermissions.includes(permission)
      );
      return {
        permissions: newPermissions,
        assignedPermissions: newAssigned,
        selectedAvailablePermissions: [],
      };
    });
  };

  moveSelectedToAvailable = () => {
    this.setState((prevState) => {
      const newPermissions = [
        ...prevState.permissions,
        ...prevState.selectedAssignedPermissions,
      ];
      const newAssigned = prevState.assignedPermissions.filter(
        (permission) =>
          !prevState.selectedAssignedPermissions.includes(permission)
      );
      return {
        permissions: newPermissions,
        assignedPermissions: newAssigned,
        selectedAssignedPermissions: [],
      };
    });
  };

  toggleSelectAllAvailable = () => {
    const { selectAllAvailable, permissions } = this.state;
    const newSelected = selectAllAvailable ? [] : permissions;

    this.setState({
      selectedAvailablePermissions: newSelected,
      selectAllAvailable: !selectAllAvailable,
    });
  };

  toggleSelectAllAssigned = () => {
    const { selectAllAssigned, assignedPermissions } = this.state;
    const newSelected = selectAllAssigned ? [] : assignedPermissions;

    this.setState({
      selectedAssignedPermissions: newSelected,
      selectAllAssigned: !selectAllAssigned,
    });
  };
  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };
  render() {
    const {
      permissions,
      assignedPermissions,
      selectedAvailablePermissions,
      selectedAssignedPermissions,
      selectAllAvailable,
      selectAllAssigned,
    } = this.state;

    const { itemsPerPage, totalItems, currentPage } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Create Role</h4>
            <hr />
            {/* <Button variant="primary" onClick={() => this.openModal()}>
              <i className="fa fa-user-plus"></i>&nbsp; Add Role
            </Button> */}
          </div>
        </div>
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row mt-2 mb-3">
          <div className="col-12 d-flex align-items-center">
            <h4>
              <i className="fa fa-user"></i>&nbsp;List of Roles
            </h4>
            <Button className="ms-3" variant="primary" onClick={() => this.openModal()}>
              <i className="fa fa-user-plus"></i>&nbsp; Add
            </Button>
          </div>
        </div>

        <DataTable
          data={this.state.rolesList.map((role, i) => ({
            ...role,
            serialNo: start + i,
            actions: role
          }))}
          columns={[
            {
              key: 'serialNo',
              header: 'S.No',
              width: '10%',
              sortable: true
            },
            {
              key: 'roleName',
              header: 'Role Name',
              // width: '30%',
              sortable: true
            },
            {
              key: 'roleDescreption',
              header: 'Description',
              // width: '40%',
              sortable: true
            },
            {
              key: 'actions',
              header: 'Actions',
              width: '120px',
              sortable: false,
              render: (_, role) => (
                <div className="d-flex justify-content-center align-items-center gap-2">
                  <button
                    title="edit role"
                    className="btn btn-primary"
                    onClick={() => this.openModal(role)}
                  >
                    <i className="fa fa-edit"></i>
                  </button>
                  <button
                    title="delete role"
                    className="btn btn-danger"
                    onClick={() => this.openDeleteConfirm(role.roleId)}
                  >
                    <i className="fa fa-trash"></i>
                  </button>
                  <button
                    title="view role permissions"
                    className="btn btn-gray"
                    onClick={() => this.viewModal(role)}
                  >
                    <i className="fa fa-eye" aria-hidden="true"></i>
                  </button>
                </div>
              )
            }
          ]}
          className="table-hover"
          searchable={true}
          itemsPerPage={this.state.itemsPerPage}
          totalItems={this.state.totalItems}
          currentPage={this.state.currentPage}
          onPageChange={(page) => this.fetchRolesList(page)}
          onItemsPerPageChange={(size) => {
            this.setState({ itemsPerPage: size }, () => {
              this.fetchRolesList(0);
            });
          }}
        />





        <Modal
          show={this.state.isOpen}
          onHide={this.closeModal}
          size="md"
          centered
        >
          <Modal.Header
            closeButton
            className="modal-header-modern"
          >
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              {this.state.isEditing ? "Edit Role" : "New Role"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <form name="createRole" onSubmit={this.handleSubmit}>
              <div className="row">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="roleName"
                >
                  Role Name <span style={{color:"red"}}>*</span>
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="roleName"
                    id="roleName"
                    className="form-control"
                    required
                    value={this.state.newRole.roleName}
                    onChange={this.handleRoleChange}
                  />
                  {this.state.isRoleExist && (
                    <span style={{ color: "red" }}>
                      Role already exists in DB
                    </span>
                  )}
                </div>
              </div>
              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="desc"
                >
                  Description <span style={{color:"red"}}>*</span>
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="roleDescreption"
                    id="desc"
                    className="form-control"
                    value={this.state.newRole.roleDescreption}
                    onChange={this.handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className="row m-3">
                <div className="col-12 text-end">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={this.state.isRoleExist}
                  >
                    {this.state.isEditing ? "Update" : "Submit"}
                  </Button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>
        <Modal
          show={this.state.showDeleteConfirm}
          onHide={this.closeDeleteConfirm}
          centered
        >
          <Modal.Header
            closeButton
            className="modal-header-modern"
          >
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              Confirm Deletion
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>Are you sure you want to delete this role?</Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={this.closeDeleteConfirm}>
              Cancel
            </Button>
            <Button variant="danger" onClick={this.confirmDeleteRole}>
              Delete
            </Button>
          </Modal.Footer>
        </Modal>
        <Modal
          show={this.state.showDetailsModal}
          onHide={this.closeDetailsModal}
          centered
          size="xl"
        >
          <Modal.Header
            closeButton
            className="modal-header-modern"
          >
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              Role Permissions Details
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            Permissions Assignment to Role:
            {this.state.roleDetails && <p>{this.state.roleDetails.roleName}</p>}
            <div className="row mt-3">
              <div className="col-5">
                <h5 className="text-center">List of Permissions</h5>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search...."
                  value={this.state.searchQuery}
                  onChange={this.handleSearchChange}
                />

                <div className={classes.tableBorder}>
                  <div className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="selectAllAvailable"
                      checked={selectAllAvailable}
                      onChange={this.toggleSelectAllAvailable}
                    />
                    <label
                      className="form-check-label"
                      htmlFor="selectAllAvailable"
                    >
                      <strong>Permissions</strong>
                    </label>
                  </div>
                  {this.getFilteredPermissions().map((permission) => (
                    <div key={permission} className="form-check">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id={permission}
                        checked={selectedAvailablePermissions.includes(
                          permission
                        )}
                        onChange={() =>
                          this.handleAvailableCheckboxChange(permission)
                        }
                      />
                      <label className="form-check-label" htmlFor={permission}>
                        {permission}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="col-2 text-center" style={{ marginTop: "160px" }}>
                <div className="mb-2">
                  <Button
                    variant="primary"
                    onClick={this.moveSelectedToAssigned}
                    disabled={selectedAvailablePermissions.length === 0}
                  >
                    <i className="fa fa-angle-double-right fa-lg"></i>
                  </Button>
                </div>
                <div>
                  <Button
                    variant="danger"
                    onClick={this.moveSelectedToAvailable}
                    disabled={selectedAssignedPermissions.length === 0}
                  >
                    <i className="fa fa-angle-double-left fa-lg"></i>
                  </Button>
                </div>
              </div>

              <div className="col-5">
                <h5 className="text-center">Assigned Permissions</h5>

                <input
                  type="text"
                  className="form-control"
                  placeholder="Search assigned permissions..."
                  value={this.state.searchAssignedQuery}
                  onChange={this.handleAssignedSearchChange}
                />
                <div className={classes.tableBorder}>
                  <div className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      id="selectAllAssigned"
                      checked={selectAllAssigned}
                      onChange={this.toggleSelectAllAssigned}
                    />

                    <label
                      className="form-check-label"
                      htmlFor="selectAllAssigned"
                    >
                      <strong>Assigned Permissions</strong>
                    </label>
                  </div>
                  {this.getFilteredAssignedPermissions().map((permission) => (
                    <div key={permission} className="form-check">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id={permission}
                        checked={selectedAssignedPermissions.includes(
                          permission
                        )}
                        onChange={() =>
                          this.handleAssignedCheckboxChange(permission)
                        }
                      />
                      <label className="form-check-label" htmlFor={permission}>
                        {permission}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={this.updatePermissions}>
              Update
            </Button>
            <Button variant="danger" onClick={this.closeDetailsModal}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default CreateRole;
