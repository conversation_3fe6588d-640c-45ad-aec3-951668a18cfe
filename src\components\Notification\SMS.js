import React, { Component } from "react";
import classes from "./Email.module.css";
import Notification from "./Notification";
import { addNew , getList ,editById } from "../../services/apiService";

class SMS extends Component {
  constructor(props) {
    super(props);
    this.state = {
      smsId:null,
      provider: "",
      description: "",
      apiUsername: "",
      apiPassword: "",
      showPassword: false,
      errors: {},
      notification: {
        message: "",
        type: "",
        show: false,
      },
      isEditMode:false,
    };
  }

  componentDidMount(){
    this.fetchSMSList();
  }

  fetchSMSList=async()=>{
    const api = `/managesms/list`;
    try {
      const response = await getList(api);
      const data=response.data
      if(data){
        this.setState({
          provider:data.provider,
          description:data.description,
          apiUsername:data.apiUsername,
          apiPassword:data.apiPassword,
          smsId:data.id,
          isEditMode:true,
        })
      }
      
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  }

  handleChange = (event) => {
    this.setState({
      [event.target.name]: event.target.value,
      errors: { ...this.state.errors, [event.target.name]: "" },
    });
  };

  handleSave = () => {
    const { provider, description, apiUsername, apiPassword } = this.state;
    let errors = {};
    if (!provider) errors.provider = "SMS API is required";
    if (!description) errors.description = "Description is required";
    if (!apiUsername) errors.apiUsername = "SMS API Username is required";
    if (!apiPassword) errors.apiPassword = "SMS API Password is required";

    if (Object.keys(errors).length > 0) {
      this.setState({ errors });
      return;
    }

    let obj = { provider, description, apiUsername, apiPassword };
    console.log("Saved Data:", obj);
    if (this.state.isEditMode) {
      this.updateData(obj); 
    } else {
      this.submitData(obj); 
    }
  };

  submitData=async(obj)=>{
    const api = "/managesms";
    try {
      const response = await addNew(api, obj);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  }

  updateData = async (obj) => {
    const api = `/managesms/${this.state.smsId}`; 
    try {
      const response = await editById(api, obj); 
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };
  
  handleClear = () => {
    this.setState({
      provider: "",
      description: "",
      apiUsername: "",
      apiPassword: "",
      showPassword: false,
      errors: {},
    });
  };

  togglePasswordVisibility = () => {
    this.setState((prevState) => ({ showPassword: !prevState.showPassword }));
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  render() {
    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>SMS Settings</h4>
            <hr />
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 p-3 border rounded">
            <h4>
              <i className="fa fa-gear"></i>&nbsp;Add SMS API Credentials
            </h4>
            <hr />
            <div className="row mt-3">
              <div className="col-md-6">
                <label>SMS API <span style={{ color: "red" }}>*</span></label>
                <input
                  type="text"
                  name="provider"
                  value={this.state.provider}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Enter SMS API"
                />
                {this.state.errors.provider && (
                  <span className="text-danger">{this.state.errors.provider}</span>
                )}
              </div>
              <div className="col-md-6">
                <label>Description <span style={{ color: "red" }}>*</span></label>
                <input
                  type="text"
                  name="description"
                  value={this.state.description}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Enter description"
                />
                {this.state.errors.description && (
                  <span className="text-danger">{this.state.errors.description}</span>
                )}
              </div>
              <div className="col-md-6 mt-3">
                <label>SMS API Username <span style={{ color: "red" }}>*</span></label>
                <input
                  type="text"
                  name="apiUsername"
                  value={this.state.apiUsername}
                  onChange={this.handleChange}
                  className="form-control w-100"
                  placeholder="Enter SMS API Username"
                />
                {this.state.errors.apiUsername && (
                  <span className="text-danger">{this.state.errors.apiUsername}</span>
                )}
              </div>
              <div className="col-md-6 mt-3">
                <label>SMS API Password <span style={{ color: "red" }}>*</span></label>
                <div className="input-group">
                  <input
                    type={this.state.showPassword ? "text" : "password"}
                    name="apiPassword"
                    value={this.state.apiPassword}
                    onChange={this.handleChange}
                    className="form-control"
                    placeholder="Enter SMS API Password"
                  />
                  <div className="input-group-append">
                    <span
                      className="btn btn-outline-gray"
                      type="button"
                      onClick={this.togglePasswordVisibility}
                    >
                      {this.state.showPassword ? (
                        <i className="fa fa-eye-slash"></i>
                      ) : (
                        <i className="fa fa-eye"></i>
                      )}
                    </span>
                  </div>
                </div>
                {this.state.errors.apiPassword && (
                  <span className="text-danger">{this.state.errors.apiPassword}</span>
                )}
              </div>
            </div>
            <button className="btn btn-primary mt-3" onClick={this.handleSave}>
              {this.state.isEditMode ? "Update" : "Save"}
            </button>
            &nbsp;&nbsp;
            <button className="btn btn-danger mt-3" onClick={this.handleClear}>
              Clear
            </button>
          </div>
        </div>
      </div>
    );
  }
}

export default SMS;
//toMail
//password