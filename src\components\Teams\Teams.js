import React, { Component } from "react";
import { Button, Dropdown, Modal, Table,Card } from "react-bootstrap";
import FileDetail from "../DocumentManagement/FileDetail";
import classes from "../DocumentManagement/DocumentManagement.module.css";
import folder from "../images/folder.png";
import FileUpdate from "../DocumentManagement/FileUpdate";
import {
  uploadDocument,
  getList,
  formatDate,
  addNew,
  deleteById,
  findById,getListOnly
} from "../../services/apiService";
import { Navigate } from "react-router-dom";
import Notification from "../Notification/Notification";
import ReactPaginate from "react-paginate";
import NumberingModal from "../Numbering/NumberingModal/NumberingModal";
import NestedObjectTable from "../DocumentManagement/NestedObjectTable/NestedObjectTable";
import Loader from "../loader/Loader";
import FileDropZone from "../DocumentManagement/FileDropZone";
import CustomBreadcrumb from "../common/CustomBreadcrumb";
// import { content } from "html2canvas/dist/types/css/property-descriptors/content";

class Teams extends Component {
  state = {
    showFileUpdate: false,
    isLoading: false,
    uploadType: "",
    showDropdown: false,
    uploadedFiles: [],
    approveUploadedFiles: [],
    acknowledgeUploadedFiles: [],
    esignUploadedFiles: [],
    showFileDetail: false,
    selectedFile: null,
    navigate: false,
    navigateToESign: false,
    toBeApprovedFile: null,
    isUploaded: false, // Track file upload completion
    notification: {
      message: "",
      type: "",
      show: false,
    },
    fileId: null,
    isDeleteModalOpen: false,
    uploadedFileName: "",
    fileUrl: null,
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    modalOpen: false,
    numberingListId: null,
    auditnavigate: false,
    searchParam: "",
    addLinkModalOpen: false,
    linkName: "",
    linkUrl: "",
    folderName: "",
    isFolderOpen: false,
    advSearchNavigate: false,
  };

  toggleDropdown = () => {
    this.setState((prevState) => ({ showDropdown: !prevState.showDropdown }));
  };

  fileInputRef = React.createRef();
  folderInputRef = React.createRef();

  upload = () => {
    this.fileInputRef.current.click();
    this.setState({ uploadType: "file" });
  };

  triggerFileInput = () => {
    this.folderInputRef.current.click();
    this.setState({ uploadType: "folder" });
  };

  componentDidMount() {
    sessionStorage.setItem("from","teams");
    this.fetchNumberingList();
    this.fetchDocumentList();
    //this.fetchApproveDocumentList();
    //this.fetchAcknowledgeDocumentList();
    //this.fetchEsignDocumentList();
    //this.setState({ fileId: sessionStorage.getItem("fileIdToDelete") });
  }

  componentWillUnmount() {
    //this.setState({numberingListId:null})
    //sessionStorage.setItem("fileIdToDelete", null);
  }

  // deleteFile = async () => {
  //   let api = `${
  //     GlobalConstants.globalURL
  //   }/documentsattachmentdetail/${sessionStorage.getItem("fileIdToDelete")}`;
  //   try {
  //     const response = axios.delete(api);
  //     this.setState({
  //       notification: {
  //         message: "File Deleted Successfully",
  //         type: "success",
  //         show: true,
  //       },
  //     });
  //   } catch (error) {
  //     this.setState({
  //       notification: {
  //         message: "Something went wrong",
  //         type: "error",
  //         show: true,
  //       },
  //     });
  //   }
  //   this.closeDeleteModal();
  // };

  // permanentDeleteFile = async () => {
  //   let api = `${
  //     GlobalConstants.globalURL
  //   }/documentsattachmentdetail/perminentdelete/${sessionStorage.getItem(
  //     "fileIdToDelete"
  //   )}`;
  //   try {
  //     const response = axios.delete(api);
  //     this.setState({
  //       notification: {
  //         message: "File Deleted Successfully",
  //         type: "success",
  //         show: true,
  //       },
  //     });
  //   } catch (error) {
  //     this.setState({
  //       notification: {
  //         message: "Something went wrong",
  //         type: "error",
  //         show: true,
  //       },
  //     });
  //   }
  //   this.closeDeleteModal();
  // };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openDeleteModal = () => {
    this.setState({ isDeleteModalOpen: true });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false });
  };

  componentDidUpdate(prevProps, prevState) {
    if (
      this.state.isUploaded &&
      this.state.isUploaded !== prevState.isUploaded
    ) {
      this.fetchDocumentList();
    }
  }

  handleButtonClick = (file) => {
    this.setState({ navigate: true });
    //console.log(file)
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
  };

  handleAckButtonClick = (file) => {
    this.setState({ acknavigate: true });
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
  };

  handleFileChange = (event) => {
    console.log(event)
    const files = event;

    //const file = event.target.files[0];

    Array.from(files).forEach((file, index) => {
      if (file) {
        const fileData = {
          slno: this.state.uploadedFiles.length + 1,
          name: file.name,
          date: new Date().toLocaleString(),
          url: URL.createObjectURL(file),
        };
        this.setState((prevState) => ({
          uploadedFiles: [...prevState.uploadedFiles, fileData],
        }));
        //event.target.value = null;

        const path = sessionStorage.getItem("currentPath");
        console.log("current path : ", path);
        //alert(path)

        const formData = new FormData();
        formData.append("file", file);
        formData.append("documentName", file.name);
        formData.append("nodeId", 0);
        formData.append("folderPath", path || "");
        this.setState({ uploadedFileName: file.name });
        // formData.append('documentId', this.documentForm.value.documentType);
        formData.append("ownerName", localStorage.getItem("userName"));
        formData.append("employeeId", localStorage.getItem("id"));
        formData.append("uploadType", "file");
        formData.append("type", "file");
        formData.append("status", "teams");
        this.uploadDocument(formData);
        if (path) {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      }
    });
  };

  handleSearchInput = (event) => {
    const searchParam = event.target.value;
    this.setState({ searchParam }, () => {
      //console.log(searchParam);
      this.fetchDocumentList();
    });
  };

  fetchDocumentList = async (
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_team/" +
      userId +
      `?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api);
      const data = response.data;

      const filteredList = data.content.filter((item) => item.fileSize > 2);
      //console.log(filteredList);

      this.setState(
        {
          uploadedFiles: data.content,
          totalItems: data.totalElements,
          currentPage: page,
          itemsPerPage,
          isLoading: false,
        },
        () => {
          // alert("sucess");
          // this.props.setUploadedFiles(filteredList.length);
        }
      );
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      // alert("error");
    }
  };

  handlePageChange = (selectedPage) => {
    this.fetchDocumentList(selectedPage.selected);
  };

  handleItemsPerPageChange = (event) => {
    const selectedItemsPerPage = parseInt(event.target.value, 10);
    this.setState({ itemsPerPage: selectedItemsPerPage }, () => {
      this.fetchDocumentList(0, selectedItemsPerPage);
    });
  };

  getApprovalWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc/approve/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;
      //console.log("Approval Level Details:", approvalLevelDTO);

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchApproveDocumentList = async () => {
    this.setState({ isLoading: true });
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/approve/" +
      userId;
    try {
      const data = await getList(api);
      //console.log(data);
      // approveUploadedFiles = data.data;
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getApprovalWorkFlowById(
            file.documentsAttachmentId
          );
          return {
            ...file,
            levelStatus: approvalData.levelStatus, // Attach levelStatus
            createdAt: approvalData.createdAt, // Attach createdAt
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );

      this.setState({ approveUploadedFiles: updatedFiles });
      this.setState({ isLoading: false });

      //this.setState({ approveUploadedFiles: data.data });
      // this.setState({ uploadedFiles: data.data });
    } catch (error) {
      this.setState({ isLoading: false });
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
    }
  };

  getEsignApprovalWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc/eSign/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;
      //console.log("Approval Level Details:", approvalLevelDTO);

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        //console.log("Matching levelStatus:", matchingLevel.levelStatus);
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchEsignDocumentList = async () => {
    this.setState({ isLoading: true });
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/eSign/" + userId;
    try {
      const data = await getList(api);
      //alert(JSON.stringify(data.data));
      //console.log(data)
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      // Use Promise.all to wait for all levelStatus and createdAt to be fetched and attached
      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getEsignApprovalWorkFlowById(
            file.documentsAttachmentId
          );

          return {
            ...file,
            levelStatus: approvalData.levelStatus, // Attach levelStatus
            createdAt: approvalData.createdAt, // Attach createdAt
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );
      // alert(JSON.stringify(updatedFiles))
      // Update the state with the files that now include levelStatus and createdAt
      this.setState({ esignUploadedFiles: updatedFiles });
      this.setState({ isLoading: false });
    } catch (error) {
      this.setState({ isLoading: false });
      //console.log("Error fetching document list:", error);
      // Optionally, you can update the state with an error message or notification
    }
  };

  getAckWorkFlowById = async (id) => {
    const api = `/approvalworkflow/doc/acknowledgement/${id}`;
    try {
      const response = await findById(api);
      const approvalLevelDTO = response.data.approvalLevelDTO;

      const assignedAt = response.data.createdAt;
      const userId = localStorage.getItem("id");

      const matchingLevel = approvalLevelDTO.find(
        (item) => item.usersId.toString() === userId
      );

      if (matchingLevel) {
        //console.log("Matching levelStatus:", matchingLevel.levelStatus);
        return {
          levelStatus: matchingLevel.levelStatus,
          createdAt: assignedAt, // Return createdAt along with levelStatus
          rejectStatus: matchingLevel.rejectStatus,
        };
      } else {
        //console.log("No matching userId found.");
        return {
          levelStatus: null,
          createdAt: assignedAt,
          rejectStatus: false,
        }; // Return null for levelStatus if no match
      }
    } catch (error) {
      //console.log("Error fetching approval workflow:", error);
      this.setState({});
      return { levelStatus: null, createdAt: null, rejectStatus: false }; // Return nulls if there's an error
    }
  };

  fetchAcknowledgeDocumentList = async () => {
    this.setState({ isLoading: true });
    const userId = localStorage.getItem("id");
    const api =
      "/documentsattachmentdetail/list_by_employee_for_approve/acknowledgement/" +
      userId;
    try {
      const data = await getList(api);
      //console.log(data);
      // acknowledgeUploadedFiles = data.data;
      const filteredData = data.data
        ? data.data.filter((file) => file !== null)
        : [];

      const updatedFiles = await Promise.all(
        filteredData.map(async (file) => {
          const approvalData = await this.getAckWorkFlowById(
            file.documentsAttachmentId
          );
          return {
            ...file,
            levelStatus: approvalData.levelStatus,
            createdAt: approvalData.createdAt,
            rejectStatus: approvalData.rejectStatus,
          };
        })
      );

      this.setState({ acknowledgeUploadedFiles: updatedFiles });
      this.setState({ isLoading: false });
      //this.setState({ acknowledgeUploadedFiles: data.data });
      // this.setState({ acknowledgeUploadedFiles: data.data });
    } catch (error) {
      this.setState({ isLoading: false });
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
    }
  };

  checkFileFormat = async (extension) => {
    const api = `/managefileformate/validateFile?fileExtention=${extension}`;
        const response = await getListOnly(api);
        if(!response){
          this.setState({
             notification: {
               message: `${extension} file format not configured`,
               type: "error",
               show: true,
             },
           });
          //throw error;
          //  setTimeout(() => {
          //    window.location.reload();
          //  }, 1000);
          return;
        }
    
  }

  uploadDocument = async (docData) => {
    const file = docData.get('file');
    let extension=null;
    if (file instanceof File) {
      const filename= file.name;
      const parts = filename.split('.');
      if (parts.length > 1) {
        extension = parts[parts.length - 1]?.toLowerCase() || null;
      }
      const filesize=file.size;
      if(filesize > 20971520){
        this.setState({
          notification: {
            message: `${filename} exceeds the file size`,
            type: "error",
            show: true,
          },
        });
        return;
      }
    } 
    this.checkFileFormat(extension);
    this.setState({ isLoading: true });
    const api = "/documentsattachmentdetail/saveDocument";
    try {
      
      const response = await uploadDocument(api, docData);
      console.log("After Uploading : ", response);
      // Add the new document directly to `uploadedFiles` instead of refetching
      const fileName = this.state.uploadedFileName;
      this.setState({ isLoading: false });
      this.setState((prevState) => ({
        // uploadedFiles: [...prevState.uploadedFiles, response.data],
        notification: {
          message: `${fileName} uploaded successfully!`,
          type: "success",
          show: true,
        },
        isUploaded: true, // Triggers refetch if necessary
      }));
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
      return response.data;
    } catch (error) {
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
      //throw error;
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
    }
    this.fetchDocumentList();
  };

  handleSelect = (eventKey) => {
    console.log(`Selected: ${eventKey}`);
    this.setState({ showDropdown: false });
  };

  toggleDetailView = (file) => {
    console.log("Toggle Detail View", file);
    this.openFileDetail(file);
  };

  openFileDetail = (file) => {
    //sessionStorage.setItem("fileIdToDelete", file.documentsAttachmentId);
    //alert(JSON.stringify(file));
    this.setState({ showFileDetail: true, selectedFile: file });
  };

  closeFileDetail = () => {
    this.setState({ showFileDetail: false, selectedFile: null });
  };

  openSignDocument = (file) => {
    // alert(JSON.stringify(file));
    sessionStorage.setItem("fileId", file.documentsAttachmentId);
    sessionStorage.setItem("fileName", file.documentName);
    sessionStorage.setItem("filePath", file.filePath);
    sessionStorage.setItem("assignedBy", file.createdBy);
    this.setState({ navigateToESign: true });
  };

  fetchNumberingList = async () => {
    this.setState({ isLoading: true });
    const api = `/numbering/list`;
    try {
      const response = await getList(api);
      if (response.data.length > 0) {
        //console.log(response.data[0].id)
        this.setState({
          numberingListId: response.data[0].id,
          isLoading: false,
        });
      }
    } catch (error) {
      this.setState({ isLoading: false });
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
      //throw error;
    }
  };

  handleOpenModal = () => {
    this.setState({ modalOpen: true });
  };

  handleCloseModal = () => {
    setTimeout(() => {
      this.setState({ modalOpen: false }, () => {
        console.log("State after update:", this.state.modalOpen);
      });
    }, 0);
  };

  handleSubmitModal = async (data) => {
    //console.log("Numbering Data Submitted:", data);
    if (data.isDeleted === true) {
      const id = data.id;
      const api = `/numbering/${id}`;
      //alert(api);
      try {
        const response = await deleteById(api);
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          modalOpen: false,
        });
        return response.data;
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
        });
        //throw error;
      }
    } else if (data.auto === true) {
      console.log("Auto selected");
      this.setState({ modalOpen: false });
    } else {
      const api = `/numbering`;
      try {
        const response = await addNew(api, data);
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          modalOpen: false,
        });
        return response.data;
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
          modalOpen: false,
        });
        //throw error;
      }
    }
  };

  openWordDocument = () => {
    const wordUrl = "https://1drv.ms/w/s!AhVV6rIULp02b1p6aU4JwSota7U?e=NhDPgh";
    window.open(wordUrl, "_blank");
  };

  openExcelDocument = () => {
    const wordUrl = "https://1drv.ms/x/s!AhVV6rIULp02cR73Bxa7g6B_rZk?e=w12537";
    window.open(wordUrl, "_blank");
  };

  openpowerpointDocument = () => {
    const wordUrl = "https://1drv.ms/p/s!AhVV6rIULp02dV5D5g4Kg0SYh9E?e=XuKIsu";
    window.open(wordUrl, "_blank");
  };

  navigateAuditLog = () => {
    this.setState({ auditnavigate: true });
  };

  navigateAdvSearch = () => {
    this.setState({ advSearchNavigate: true });
  };

  handleAddLink = () => {
    this.setState({ addLinkModalOpen: true });
  };

  closeAddLinkModal = () => {
    this.setState({ addLinkModalOpen: false });
  };

  handleLinkSubmit = async () => {
    const { linkName } = this.state;
    const path = sessionStorage.getItem("currentPath") || "";
    const fullPath = path ? `${path}/${linkName}` : linkName;

    console.log("New link Path:", fullPath);

    const data = {
      documentName: this.state.linkName,
      filePath: fullPath,
      linkPath: this.state.linkUrl,
      fileType: "link",
      status:"teams",
      employee: {
        id: localStorage.getItem("id"),
      },
    };
    //console.log("Data submitted : ", data);
    const api = `/documentsattachmentdetail`;
    try {
      const response = await addNew(api, data);
      //console.log("After Uploading : " + response);
      // Add the new document directly to `uploadedFiles` instead of refetching
      const fileName = this.state.uploadedFileName;
      this.setState((prevState) => ({
        uploadedFiles: [...prevState.uploadedFiles, response.data],
        notification: {
          message: `${fileName} uploaded successfully!`,
          type: "success",
          show: true,
        },
        //isUploaded: true, // Triggers refetch if necessary
      }));
      window.location.reload();
      this.closeAddLinkModal();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      //throw error;
    }
  };

  handleFolderOpen = () => {
    this.setState({ isFolderOpen: true });
  };

  handleFolderCloseModal = () => {
    this.setState({ isFolderOpen: false, folderName: "" });
  };

  getSampleFile = () => {
    return new File(["a"], this.state.folderName + ".txt", {
      type: "text/plain",
    });
  };

  handleSubmitFolderModal = async () => {
    const { folderName } = this.state;
    const path = sessionStorage.getItem("currentPath") || "";
    const fullPath = path ? `${path}/${folderName}` : folderName;

    console.log("New Folder Path:", fullPath);

    const file = this.getSampleFile();

    //console.log(file)

    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentName", folderName);
      formData.append("folderPath", fullPath); // Set correct folder path
      formData.append("ownerName", localStorage.getItem("userName"));
      formData.append("employeeId", localStorage.getItem("id"));
      formData.append("uploadType", "file");
      formData.append("type", "folder");
      formData.append("status", "teams");
      this.setState({ uploadedFileName: file.name });

      // Call upload function
      this.uploadDocuments(formData);
      if (path) {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }

      // Update uploaded files list
      const fileData = {
        slno: this.state.uploadedFiles.length + 1,
        name: file.name,
        date: new Date().toLocaleString(),
        url: URL.createObjectURL(file),
        folderPath: fullPath,
      };

      this.setState((prevState) => ({
        uploadedFiles: [...prevState.uploadedFiles, fileData],
      }));
    }

    this.handleFolderCloseModal();
  };

  uploadDocuments = async (docData) => {
    this.setState({ isLoading: true });
    const api = "/documentsattachmentdetail/saveDocument";
    try {
      
      const response = await uploadDocument(api, docData);
      console.log("After Uploading : ", response);
      this.setState({ isLoading: false });
      const folderName = this.state.folderName;
      this.setState((prevState) => ({
        notification: {
          message: `${folderName} folder created successfully!`,
          type: "success",
          show: true,
        },
        isUploaded: true, // Triggers refetch if necessary
      }));
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
      return response.data;
    } catch (error) {
      // setTimeout(() => {
      //   window.location.reload();
      // }, 1000);
    }
    this.fetchDocumentList();
  };

  removeLastWord(folderPath) {
    // Split the string by spaces and store the words in an array
    const words = folderPath.split("/");

    // If there is more than one word, remove the last one and join the rest back into a string
    if (words.length > 1) {
      words.pop(); // Remove the last word
      return words.join("/"); // Join the remaining words with a space
    }

    // If the string contains only one word, return an empty string
    return "";
  }

  handleFolderUpload = (event) => {
    const files = event.target.files;
    const filesArray = Array.from(files);
    filesArray.forEach((file) => {
      const folderPath = this.removeLastWord(file.webkitRelativePath);
      //console.log(`Folder File: ${file.name}, Path: ${folderPath}`);
      console.log(folderPath);
      const currentPath = sessionStorage.getItem("currentPath");
      const fullPath = currentPath
        ? currentPath + "/" + folderPath
        : folderPath;
      console.log("=====", fullPath);
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentName", file.name);
      formData.append("nodeId", 0);
      formData.append("folderPath", fullPath);
      formData.append("ownerName", localStorage.getItem("userName"));
      formData.append("employeeId", localStorage.getItem("id"));
      formData.append("uploadType", "folder");
      formData.append("type", "folder");
      formData.append("status", "teams");

      this.uploadDocument(formData);
      if (currentPath) {
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
      for (const [key, value] of formData.entries()) {
        // console.log(`${key}: ${value}`);
      }
    });
  };

  handleFilesDropped = (files) => {
    console.log("Files received in parent component:");
    files.forEach((file, index) => {
      console.log(file);
      if (file) {
        const fileData = {
          slno: this.state.uploadedFiles.length + 1,
          name: file.name,
          date: new Date().toLocaleString(),
          url: URL.createObjectURL(file),
        };
        this.setState((prevState) => ({
          uploadedFiles: [...prevState.uploadedFiles, fileData],
        }));

        if (file.webkitRelativePath !== file.name) {
          const folderPath = this.removeLastWord(file.webkitRelativePath);
          //console.log(`Folder File: ${file.name}, Path: ${folderPath}`);
          //console.log(folderPath);
          console.log("Folder=========");
          const currentPath = sessionStorage.getItem("currentPath");
          const fullPath = currentPath
            ? currentPath + "/" + folderPath
            : folderPath;
          console.log("=====", fullPath);
          const formData = new FormData();
          formData.append("file", file);
          formData.append("documentName", file.name);
          formData.append("nodeId", 0);
          formData.append("folderPath", fullPath || "");
          this.setState({ uploadedFileName: file.name });
          // formData.append('documentId', this.documentForm.value.documentType);
          formData.append("ownerName", localStorage.getItem("userName"));
          formData.append("employeeId", localStorage.getItem("id"));
          formData.append("uploadType", "file");
          formData.append("type", "folder");
          formData.append("status", "teams");
          this.uploadDocument(formData);
          if (currentPath) {
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } else {
          console.log("File=========");
          const currentPath = sessionStorage.getItem("currentPath");
          const formData = new FormData();
          formData.append("file", file);
          formData.append("documentName", file.name);
          formData.append("nodeId", 0);
          formData.append("folderPath", currentPath || "");
          this.setState({ uploadedFileName: file.name });
          // formData.append('documentId', this.documentForm.value.documentType);
          formData.append("ownerName", localStorage.getItem("userName"));
          formData.append("employeeId", localStorage.getItem("id"));
          formData.append("uploadType", "file");
          formData.append("type", "file");
          formData.append("status", "teams");
          this.uploadDocument(formData);
          if (currentPath) {
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        }
      }
    });
  };

  render() {
    const {
      showFileDetail,
      selectedFile,
      uploadedFiles,
      approveUploadedFiles,
      acknowledgeUploadedFiles,
      esignUploadedFiles,
    } = this.state;

    const { itemsPerPage, currentPage, totalItems } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    const splitFilePaths = uploadedFiles
      .map((file) => file?.filePath) // Ensure `filePath` exists
      .filter((filePath) => typeof filePath === "string") // Exclude undefined/null values
      .map((filePath) => filePath.split("/").slice(5).filter(Boolean)); // Split and clean paths

    const routeTree = {};

    splitFilePaths.forEach((pathParts) => {
      let current = routeTree;

      pathParts.forEach((segment) => {
        if (!current[segment]) {
          current[segment] = {};
        }
        current = current[segment];
      });
    });

    // console.log(routeTree);

    if (this.state.navigate) {
      return <Navigate to="/newDS/emailApproval" />;
    }
    if (this.state.auditnavigate) {
      return <Navigate to="/newDS/auditlog" />;
    }
    if (this.state.advSearchNavigate) {
      return <Navigate to="/newDS/advancedSearch" />;
    }
    if (this.state.acknavigate) {
      return <Navigate to="/newDS/emailAcknowledge" />;
    }

    if (this.state.navigateToESign) {
      return <Navigate to="/newDS/esignApproval" />;
    }

    if (this.state.reload) {
      return <Navigate to={this.props.location.pathname} />;
    }

    return (
      <>
        {!showFileDetail ? (
          <>
            <div className="container-fluid py-1">
              <div className="row align-items-center justify-content-between">
                <div className="col-12 d-flex align-items-center justify-content-between flex-wrap">
                  <div className="d-flex align-items-center gap-2 flex-wrap">
                    <Dropdown
                      show={this.state.showDropdown}
                      onToggle={this.toggleDropdown}
                    >
                      <Dropdown.Toggle
                        variant="primary"
                        className="d-flex align-items-center gap-2"
                      >
                        Add New
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <Dropdown.Item onClick={this.triggerFileInput}>
                          <i className="fa fa-folder-open"></i> Upload a Folder
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.handleAddLink}>
                          <i className="fa fa-link"></i> Add a Link
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openWordDocument}>
                          <i className="fa fa-file-word-o"></i> New Word
                          Document
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openExcelDocument}>
                          <i className="fa fa-file-excel-o"></i> New Excel
                          Spreadsheet
                        </Dropdown.Item>
                        <Dropdown.Item onClick={this.openpowerpointDocument}>
                          <i className="fa fa-file-powerpoint-o"></i> New
                          PowerPoint Presentation
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                    <Button
                      variant="dark"
                      className="d-flex align-items-center gap-2"
                      onClick={this.handleFolderOpen}
                    >
                      <i className="fa fa-folder"></i> Create Folder
                    </Button>
                    <Button
                      variant="success"
                      className="d-flex align-items-center gap-2"
                      onClick={this.navigateAuditLog}
                    >
                      <i className="fa fa-file-text-o"></i> Audit Log
                    </Button>
                    <Button
                      style={{
                        background: "#ffc107",
                        border: "none",
                        color: "#222",
                      }}
                      className="d-flex align-items-center gap-2"
                      onClick={this.handleOpenModal}
                    >
                      <i className="fa fa-list-ol"></i> Numbering
                    </Button>
                    <Button
                      style={{
                        background: "#17c9f7",
                        border: "none",
                        color: "#fff",
                      }}
                      className="d-flex align-items-center gap-2"
                      onClick={this.navigateAdvSearch}
                    >
                      <i className="fa fa-search"></i> Adv. Search
                    </Button>
                    <input
                      title="search"
                      type="search"
                      name="searchParam"
                      className="form-control"
                      placeholder="Search docs, tags, etc.."
                      style={{ height: "40px", maxWidth: "205px" }}
                      value={this.state.searchParam}
                      onChange={this.handleSearchInput}
                    />
                  </div>
                  <div
                    className="d-flex align-items-center gap-2"
                    style={{ minWidth: 0 }}
                  >
                    {/* <CustomBreadcrumb /> */}
                  </div>
                </div>
              </div>
            </div>

            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}

            <input
              ref={this.folderInputRef}
              style={{ display: "none" }}
              type="file"
              webkitdirectory="true"
              directory=""
              onChange={this.handleFolderUpload}
            />

            {/* <h5 style={{ marginTop: 32, marginBottom: 12 }}>My Docs</h5> */}
            <FileDropZone onFilesDropped={this.handleFilesDropped} onFilesSelected={this.handleFileChange} style={{ minHeight: '80px' }} />

            <div className="py-1">
              <Card className="shadow-sm border-1 mb-2">
                <Card.Body className="col-sm-12 col-12 d-flex align-items-center justify-content-between p-2">
                <NestedObjectTable
                  from={"teams"}
                  data={routeTree}
                  fileSelected={selectedFile}
                  uploadedFiles={this.state.uploadedFiles}
                  toggleDetailView={this.toggleDetailView}
                />     
                </Card.Body>
              </Card>      
            </div>
      
            {/* <div className="table table-hover table-striped mt-5"> */}
              {/* {sessionStorage.getItem("role") !== "SUPER_ADMIN" && (
                <h5>My Docs</h5>
              )} */}
              {/* {uploadedFiles.length > 0 && (
                <div>
                  <Table striped bordered hover className={classes.fileTable}>
                    <thead>
                      <tr>
                        <th>SNo</th>
                        <th>File Name</th>
                        <th>Upload Date</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.isArray(uploadedFiles) &&
                        uploadedFiles
                          .filter((file) => file !== null)
                          .map((file, i) => (
                            <tr key={file.documentsAttachmentId}>
                              <td>{start + i}</td>
                              <td>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    this.openFileDetail(file);
                                  }}
                                  style={{
                                    color: "blue",
                                    textDecoration: "underline",
                                    cursor: "pointer",
                                    background: "none",
                                    border: "none",
                                  }}
                                >
                                  {file.documentName}
                                </button>
                              </td>
                              <td>{formatDate(file.createdDate)}</td>
                              <td>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    this.openFileDetail(file);
                                  }}
                                  style={{
                                    background: "none",
                                    border: "none",
                                    cursor: "pointer",
                                  }}
                                >
                                  <i className="fa fa-eye"></i>
                                </button>
                              </td>
                            </tr>
                          ))}
                    </tbody>
                  </Table>
                  {totalItems > 0 && (
                    <div className="my-3">
                      Showing {start} - {end} of {totalItems} items
                    </div>
                  )}
                  <div>
                    Showing&nbsp;&nbsp;
                    <select
                      value={itemsPerPage}
                      onChange={this.handleItemsPerPageChange}
                    >
                      <option value={50}>50</option>
                      <option value={75}>75</option>
                      <option value={100}>100</option>
                    </select>
                    &nbsp;&nbsp; items per page
                    <ReactPaginate
                      previousLabel={
                        <>
                          <i className="fa fa-angle-double-left"></i> Previous
                        </>
                      }
                      nextLabel={
                        <>
                          Next <i className="fa fa-angle-double-right"></i>
                        </>
                      }
                      breakLabel={"..."}
                      pageCount={Math.ceil(totalItems / itemsPerPage)}
                      onPageChange={this.handlePageChange}
                      containerClassName={"pagination justify-content-end"}
                      pageClassName={"page-item"}
                      pageLinkClassName={"page-link"}
                      activeClassName={"active"}
                      previousClassName={"page-item"}
                      nextClassName={"page-item"}
                      previousLinkClassName={"page-link"}
                      nextLinkClassName={"page-link"}
                    />
                  </div>
                </div>
              )} */}
              {/* <h5>Files that need my approval</h5> */}
              {/* <Table striped bordered hover className={classes.fileTable}>
                <thead>
                  <tr>
                    <th>SNo</th>
                    <th>File Name</th>
                    <th>Assigned At</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {Array.isArray(approveUploadedFiles) &&
                    approveUploadedFiles.map((file, i) => (
                      <tr key={file.documentsAttachmentId}>
                        <td>{i + 1}</td>
                        <td>
                          {file.rejectStatus ? (
                            <span className="fa fa-circle text-danger"></span>
                          ) : file.levelStatus ? (
                            <span className="fa fa-circle text-success"></span>
                          ) : (
                            <span className="fa fa-circle text-warning"></span>
                          )}
                          <button
                            onClick={() => this.handleButtonClick(file)}
                            style={{
                              color: "blue",
                              textDecoration: "underline",
                              cursor: "pointer",
                              background: "none",
                              border: "none",
                            }}
                          >
                            {file.documentName}
                          </button>
                        </td>
                        <td>{formatDate(file.createdAt)}</td>
                        {}
                        </div><td
                          onClick={() => this.handleButtonClick(file)}
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                          }}
                        >
                          {file.rejectStatus
                            ? "Rejected"
                            : file.levelStatus
                            ? "Approved"
                            : "Yet to Approve"}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table> */}
              {/* <h5>Files that need my electronic signature</h5> */}
              {/* <Table striped bordered hover className={classes.fileTable}>
                <thead>
                  <tr>
                    <th>SNo</th>
                    <th>File Name</th>
                    <th>Assigned At</th>
                    {}
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {Array.isArray(esignUploadedFiles) &&
                    esignUploadedFiles.map((file, i) => (
                      <tr key={file.documentsAttachmentId}>
                        <td>{i + 1}</td>
                        <td>
                          {file.rejectStatus ? (
                            <span className="fa fa-circle text-danger"></span>
                          ) : file.levelStatus ? (
                            <span className="fa fa-circle text-success"></span>
                          ) : (
                            <span className="fa fa-circle text-warning"></span>
                          )}
                          <button
                            onClick={() => this.openSignDocument(file)}
                            style={{
                              color: "blue",
                              textDecoration: "underline",
                              cursor: "pointer",
                              background: "none",
                              border: "none",
                            }}
                          >
                            
                            {file.documentName}
                          </button>
                        </td>
                        <td>{formatDate(file.createdAt)}</td>
                        {}
                        <td
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => this.openSignDocument(file)}
                        >
                          {file.rejectStatus
                            ? "Rejected"
                            : file.levelStatus
                            ? "Signed"
                            : "Yet to Sign"}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table> */}
              {/* <h5>Files that need my acknowledge</h5> */}
              {/* <Table striped bordered hover className={classes.fileTable}>
                <thead>
                  <tr>
                    <th>SNo</th>
                    <th>File Name</th>
                    <th>Assigned At</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {Array.isArray(acknowledgeUploadedFiles) &&
                    acknowledgeUploadedFiles.map((file, i) => (
                      <tr key={file.documentsAttachmentId}>
                        <td>{i + 1}</td>
                        <td>
                          {file.rejectStatus ? (
                            <span className="fa fa-circle text-danger"></span>
                          ) : file.levelStatus ? (
                            <span className="fa fa-circle text-success"></span>
                          ) : (
                            <span className="fa fa-circle text-warning"></span>
                          )}
                          <button
                            onClick={() => this.handleAckButtonClick(file)}
                            style={{
                              color: "blue",
                              textDecoration: "underline",
                              cursor: "pointer",
                              background: "none",
                              border: "none",
                            }}
                          >
                            {file.documentName}
                          </button>
                        </td>
                        <td>{formatDate(file.createdAt)}</td>
                        {}
                        <td
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                          }}
                          onClick={() => this.handleAckButtonClick(file)}
                        >
                          {file.levelStatus
                            ? "Acknowledged"
                            : "Yet to acknowledge"}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table> */}
            {/* </div> */}
          </>
        ) : (
          this.state.showFileUpdate ? (
            <FileUpdate
              onClose={(file) => this.setState({ showFileUpdate: false, showFileDetail: true, selectedFile: file })}
            />
        ) : (
          <FileDetail
            fileName={selectedFile.documentName}
            fileUrl={selectedFile.filePath}
            fileId={selectedFile.documentsAttachmentId}
            onClose={this.closeFileDetail}
          />
        )
        )}

        {/* Delete confirmation modal */}
        <div>
          <Modal
            show={this.state.isDeleteModalOpen}
            onHide={this.closeDeleteModal}
            centered
          >
            
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title
                style={{ textAlign: "center", width: "100%", margin: 0 }}
              >
                Confirm deletion
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>Are you sure you want to delete this file?</Modal.Body>
            <Modal.Footer className="modal-footer-modern">
              <Button variant="secondary" onClick={this.closeDeleteModal}>
                Cancel
              </Button>
              <Button variant="danger" onClick={this.deleteFile}>
                Delete
              </Button>
            </Modal.Footer>
          </Modal>
        </div>

        {/* Add link modal */}
        <div>
          <Modal
            show={this.state.addLinkModalOpen}
            onHide={this.closeAddLinkModal}
          >
            
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title
                style={{ textAlign: "center", width: "100%", margin: 0 }}
              >
                New Link
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div>
                <div className="row">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="linkName"
                      id="linkName"
                      placeholder="Link Name"
                      className="form-control"
                      required
                      value={this.state.linkName}
                      onChange={(event) => {
                        this.setState({ linkName: event.target.value });
                      }}
                    />
                  </div>
                </div>
                <div className="row mt-2">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="linkUrl"
                      placeholder="Link URL"
                      id="linkUrl"
                      className="form-control"
                      value={this.state.linkUrl}
                      onChange={(event) => {
                        this.setState({ linkUrl: event.target.value });
                      }}
                      required
                    />
                  </div>
                </div>
                <div className="row mt-3">
                  <div className="col-12 text-center">
                    <Button variant="primary" onClick={this.handleLinkSubmit}>
                      Create
                    </Button>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </div>

        {/* folder modal*/}
        <div>
          <Modal
            show={this.state.isFolderOpen}
            onHide={this.handleFolderCloseModal}
          >
            
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title
                style={{ textAlign: "center", width: "100%", margin: 0 }}
              >
                Create Folder
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {/* <h5>Create Folder</h5> */}
              <div>
                <div className="row">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="folderName"
                      id="folderName"
                      placeholder="Folder Name"
                      className="form-control"
                      required
                      value={this.state.folderName}
                      onChange={(event) => {
                        this.setState({ folderName: event.target.value });
                      }}
                    />
                  </div>
                </div>
                <div className="row mt-3">
                  <div className="col-12 text-center">
                    <Button
                      variant="primary"
                      onClick={this.handleSubmitFolderModal}
                      disabled={!this.state.folderName}
                    >
                      Create
                    </Button>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </div>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default Teams;
