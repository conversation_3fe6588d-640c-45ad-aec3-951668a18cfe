import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import classes from "./PasswordManagement.module.css";
import { getList } from "../../services/apiService";
import axios from '../../services/api';
import { GlobalConstants } from "../../constants/global-constants";
import Notification from "../Notification/Notification";
import { DataTable } from "../Table/DataTable";

export class PasswordManagement extends Component {
  state = {
    userList: [],
    showResetConfirm: false,
    userIdToReset: null,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    currentPage:0,
    totalItems:0,
    itemsPerPage:10,
    search:"",
  };
  componentDidMount() {
    this.fetchUserList();
  }
  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleSearchInputChange = (event) => {
    const search = event.target.value;
    this.setState({ search:search }, () => {
      console.log(search);
      this.fetchUserList();
    });
  };

  fetchUserList = async (page=0) => {
    const {itemsPerPage}=this.state
    const api = `/user/list?page=${page}&size=${itemsPerPage}&search=${this.state.search}&sort=`;
    try {
      const response = await getList(api);
      const data=response.data;
      this.setState({
        userList: data.content,
        totalItems:data.totalElements,
        currentPage:page,
       });
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchUserList(page);
  }

  openResetConfirm = (userId) => {
    this.setState({ showResetConfirm: true, userIdToReset: userId });
  };

  closeResetConfirm = () => {
    this.setState({ showResetConfirm: false, userIdToReset: null });
  };

  confirmResetUser = async () => {
    const { userIdToReset } = this.state;
    const api =
      `${GlobalConstants.globalURL}` +
      `/user/user_reset_password/${userIdToReset}`;
    try {
      const data = await axios.get(api);
      console.log(data.data);
      this.setState({
        notification: {
          message: "User Password Reset Success!",
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    } finally {
      this.closeResetConfirm();
    }
  };

  handleItemsPerPageChange = (newItemsPerPage) => {
    this.setState({ itemsPerPage: newItemsPerPage }, () => {
      this.fetchUserList(0);
    });
  };

  render() {
    const {itemsPerPage,currentPage,totalItems}=this.state;
    const start=currentPage*itemsPerPage+1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    return (
      <div className="container mt-3">
        <div className="row text-center">
          <div className="col-12">
          <h4>User Password Reset</h4>
          <hr />
          </div>
        </div>
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row mt-2">
          <div className="col-12 d-flex align-items-center">
              <h4><i className="fa fa-user"></i>&nbsp;List of Users</h4>
          </div>
        </div>

        <DataTable
          data={this.state.userList}
          columns={[
            {
              key: 'index',
              header: 'S.No',
              render: (_, row, index) => start + index,
              width: '80px'
            },
            {
              key: 'userName',
              header: 'User Name',
              sortable: true
            },
            {
              key: 'firstName',
              header: 'First Name',
              sortable: true
            },
            {
              key: 'lastName',
              header: 'Last Name',
              sortable: true
            },
            {
              key: 'email',
              header: 'Email ID',
              sortable: true
            },
            {
              key: 'actions',
              header: 'Actions',
              width: '100px',
              render: (_, user) => (
                <div className="text-center">
                  <i
                    title="reset password"
                    className="fa fa-refresh"
                    style={{ cursor: "pointer" }}
                    onClick={() => this.openResetConfirm(user.id)}
                  ></i>
                </div>
              )
            }
          ]}
          searchable={true}
          className="table-hover"
          itemsPerPage={this.state.itemsPerPage}
          totalItems={this.state.totalItems}
          currentPage={this.state.currentPage}
          onPageChange={this.handlePageChange}
          onItemsPerPageChange={this.handleItemsPerPageChange}
        />



        <Modal
          show={this.state.showResetConfirm}
          onHide={this.closeResetConfirm} size="md"
          centered
        >
          <Modal.Header closeButton className='bg-info'>
            <Modal.Title style={{fontSize:'16px', textAlign:'center', width:'100%', margin:0}}>Confirm Password Reset</Modal.Title>
          </Modal.Header>
          <Modal.Body>Are you sure you want to reset the Password?</Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={this.closeResetConfirm}>
              Cancel
            </Button>
            <Button variant="danger" onClick={this.confirmResetUser}>
              Reset
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

export default PasswordManagement;
