import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import classes from "./TeamsManagement.module.css";
import Notification from "../Notification/Notification";
import { addNew, getList } from "../../services/apiService";
import { deleteById, editById } from "../../services/apiService";
import Loader from "../loader/Loader";
import { DataTable } from "../Table/DataTable";

class TeamsManagement extends Component {
  state = {
    searchParam: "",
    isOpen: false,
    isDeleteModalOpen: false,
    isTeamExist: false,
    teamId: null,
    userList: [],
    filteredUserList: [],
    teamsList: [],
    selectedMembers: [],
    teamName: "",
    teamLeadName: "",
    teamLead: null,
    status: "1",
    description: "",
    teamLeadSelected: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    teamIdToDelete: null,
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    isEditing: false,
    isLoading: false,
    teamMembersModal: false,
    currentTeamMembers: [],
    errors: {},
  };

  openModal = (team = null) => {
    if (team) {
      const filteredMembers = team.teamMemebers
        ? team.teamMemebers
          .map((member) => (typeof member === "object" ? member.id : member))
          .filter((memberId) => memberId !== team.teamLead)
        : [];

      const teamLeadId = team.teamLead;
      const filteredUserList = this.state.userList.filter(user => user.id !== teamLeadId);
      console.log(filteredUserList);
      this.setState({ filteredUserList: filteredUserList });

      this.setState({
        teamId: team.id,
        isOpen: true,
        isEditing: true,
        teamName: team.name || "",
        status: team.status ? "1" : "0",
        teamLead: team.teamLead || "",
        teamLeadName: team.teamLeadName || "",
        description: team.adminNotes || "",
        selectedMembers: filteredMembers,
        teamLeadSelected: !!team.teamLead,
      });
    } else {
      this.setState({
        teamId: null,
        isOpen: true,
        isEditing: false,
        teamName: "",
        status: "1",
        teamLead: "",
        teamLeadName: "",
        description: "",
        selectedMembers: [],
      });
    }
  };

  openDeleteModel = (id) => {
    this.setState({ isDeleteModalOpen: true, teamIdToDelete: id });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false });
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      selectedMembers: [],
      isEditing: false,
      teamName: "",
      teamLeadName: "",
      teamLead: "",
      description: "",
      status: "",
      teamLeadSelected: false,
    });
  };

  componentDidMount() {
    this.fetchUserList();
    this.fetchTeamsList();
  }

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=100&search=&sort=";
    this.setState({ isLoading: true });
    try {
      const data = await getList(api);
      if (data) {
        this.setState({ isLoading: false });
      }
      this.setState({ userList: data.data.content });
    } catch (error) {
      this.setState({ isLoading: false });
      console.log(error);
    }
  };

  handleSearchInputChange = (event) => {
    const searchParam = event.target.value;
    this.setState({ searchParam: searchParam }, () => {
      console.log(searchParam);
      this.fetchTeamsList();
    });
  };

  fetchTeamsList = async (page = 0) => {
    const { itemsPerPage } = this.state;
    let api = `/team/team_list?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        teamsList: data.content,
        currentPage: page,
        totalItems: data.totalElements,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchTeamsList(page);
  };

  handleMemberChange = (userId) => {
    if (userId.toString() === this.state.teamLead) return;

    this.setState((prevState) => {
      const { selectedMembers } = prevState;
      const isSelected = selectedMembers.includes(userId);
      return {
        selectedMembers: isSelected
          ? selectedMembers.filter((id) => id !== userId)
          : [...selectedMembers, userId],
      };
    });
  };

  toggleSelectAllMembers = () => {
    const { selectedMembers, userList, teamLead } = this.state;
    const membersToSelect = userList
      .filter((user) => user.id.toString() !== teamLead)
      .map((user) => user.id);

    if (selectedMembers.length === membersToSelect.length) {
      this.setState({ selectedMembers: [] });
    } else {
      this.setState({ selectedMembers: membersToSelect });
    }
  };

  check = async (teamName) => {
    if(teamName.trim()) return;
    console.log(teamName);
    this.setState({ isTeamExist: false });
    const api = `/team/validateTeam?teamName=${teamName}`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({ isTeamExist: response });
    } catch (error) { }
  };

  timer=null;
  handleTeamNameChange = (e) => {
    const teamValue = e.target.value;
    this.setState({
      teamName: teamValue,
    });
    if(this.timer) clearTimeout(this.timer);
    this.timer=setTimeout(() => {
      this.check(teamValue);
    }, 500);

  };
  handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "teamLead") {
      const selectedUser = this.state.userList.find(
        (user) => user.id.toString() === value
      );

      const filteredUserList = this.state.userList.filter(user => user.id !== selectedUser.id);
      this.setState({ filteredUserList: filteredUserList });

      this.setState((prevState) => ({
        teamLead: value,
        teamLeadName: selectedUser ? selectedUser.userName : "",
        teamLeadSelected: value !== "",
      }));
    } else if (name === "status") {
      this.setState((prevState) => ({
        status: value,
      }));
    } else {
      this.setState((prevState) => ({
        [name]: value,
      }));
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();

    const {
      teamName,
      teamLead,
      selectedMembers,
      status,
      isEditing,
      teamId,
    } = this.state;

    // Validation
    let errors = {};
    if (!teamName || !teamName.trim()) errors.teamName = 'Team name is required.';
    if (!teamLead) errors.teamLead = 'Team lead is required.';
    if (!selectedMembers || selectedMembers.length === 0) errors.selectedMembers = 'At least one team member is required.';
    if (!status) errors.status = 'Status is required.';
    this.setState({ errors });
    if (Object.keys(errors).length > 0) return;

    const finalMembers = selectedMembers.filter(
      (memberId) => memberId.toString() !== teamLead.toString()
    );

    const teamJson = {
      name: teamName,
      teamLead,
      status,
      adminNotes: this.state.description,
      teamMemebers: finalMembers,
    };

    try {
      if (isEditing) {
        await this.editTeam(teamId, teamJson);
      } else {
        await this.createTeam(teamJson);
      }
      this.closeModal();
    } catch (error) {
      this.setState({
        notification: {
          message: 'Something went wrong during submission',
          type: 'error',
          show: true,
        },
      });
    }
  };

  createTeam = async (teamJson) => {
    let api = "/team";
    this.setState({ isLoading: true });
    try {
      const response = await addNew(api, teamJson);
      if (response.status === "success") {
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      this.fetchTeamsList();
      return response.data;
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
    this.fetchTeamsList();
  };

  editTeam = async (teamId, updatedTeam) => {
    const api = `/team/${teamId}`;
    this.setState({ isLoading: true });
    try {
      const response = await editById(api, updatedTeam);
      if (response.status === "success") {
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      this.fetchTeamsList();
      return response.data;
    } catch (error) {
      console.error(
        "Error updating team:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      throw error;
    }
  };

  handleStatusChange = (e) => {
    const newStatus = e.target.value;
    this.setState({
      status: newStatus,
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };
  deleteTeamById = async (id) => {
    let api = `/team/${id}`;
    try {
      const response = await deleteById(api);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.closeDeleteModal();
      this.fetchTeamsList();
    } catch (error) {
      console.log(error);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  teamLeadName = (id) => {
    const user = this.state.userList.find((user) => user.id === id);
    return user ? user.userName : null;
  };

  showTeamMembers = (members) => {
    this.setState({ teamMembersModal: true, currentTeamMembers: members });
  }

  closeTeamMembers = () => {
    this.setState({ teamMembersModal: false, currentTeamMembers: [] });
  }

  getMemberNames = (memberIds) => {
    const { userList } = this.state;
    return (
      <ul>
        {memberIds.map(id => {
          const user = userList.find(user => user.id === id);
          return (
            <li key={id}>
              {user && user.userName}
            </li>
          );
        })}
      </ul>
    );
  };

  render() {
    const { selectedMembers, userList, teamLeadSelected } = this.state;

    const membersWithoutLead = userList.filter(
      (user) => user.id.toString() !== this.state.teamLead
    );

    const isAllSelected =
      selectedMembers.length === membersWithoutLead.length &&
      membersWithoutLead.length > 0;

    const { itemsPerPage, totalItems, currentPage } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    return (
      <div className="container mt-3">
        <div className="row text-center">
          <div className="col-12">
            <h4>Create Team</h4>
            <hr />
            {/* <Button
              type="button"
              className="btn btn-primary"
              onClick={() => this.openModal()}
            >
              Create Team
            </Button> */}
          </div>
        </div>
        <div className="row mt-2">
          <div className="col-4 d-flex align-items-center">
            <h4>
              <i class="fa fa-users"></i>&nbsp;List of Teams
            </h4>
            <Button
              type="button"
              className="ms-3 btn btn-primary"
              onClick={() => this.openModal()}
            >
              <i className="fa fa-user-plus"></i>&nbsp;Create
            </Button>
          </div>
          <div className="col-3 offset-5 d-flex justify-content-end align-items-center">
            <input
              title="search"
              type="search"
              value={this.state.searchParam}
              name="searchParam"
              className="form-control ms-2"
              placeholder="Search"
              style={{ height: "40px" }}
              onChange={this.handleSearchInputChange}
            />
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="mt-4">
          <DataTable
            data={this.state.teamsList.map((team, i) => ({
              ...team,
              serialNo: start + i,
              teamLeadName: this.teamLeadName(team.teamLead),
              statusText: team.status ? "Active" : "Inactive",
              membersCount: team.teamMemebers.length,
              description: team.adminNotes || "",
            }))}
            columns={[
              {
                key: "serialNo",
                header: "S.No",
                sortable: true,
                width: "80px"
              },
              {
                key: "name",
                header: "Name",
                sortable: true
              },
              {
                key: "teamLeadName",
                header: "Team Lead Name",
                sortable: true
              },
              {
                key: "statusText",
                header: "Status",
                sortable: true,
                render: (value) => (
                  <Badge bg={value === "Active" ? "success" : "danger"}>
                    {value}
                  </Badge>
                )
              },
              {
                key: "membersCount",
                header: "Team Members",
                sortable: true,
                render: (value, row) => (
                  <span
                    className="text-primary"
                    style={{ cursor: "pointer" }}
                    onClick={() => this.showTeamMembers(row.teamMemebers)}
                  >
                    {value}
                  </span>
                )
              },
              {
                key: "description",
                header: "Description",
                sortable: true
              },
              {
                key: "actions",
                header: "Action",
                sortable: false,
                render: (_, row) => (
                  <div>
                    <Button
                      title="edit team"
                      variant="primary"
                      size="sm"
                      onClick={() => this.openModal(row)}
                      className="me-2"
                    >
                      <i className="fa fa-edit"></i>
                    </Button>
                    <Button
                      title="delete team"
                      variant="danger"
                      size="sm"
                      onClick={() => this.openDeleteModel(row.id)}
                    >
                      <i className="fa fa-trash"></i>
                    </Button>
                  </div>
                )
              }
            ]}
            searchable={false}
            className="table-hover"
            itemsPerPage={this.state.itemsPerPage}
            totalItems={this.state.totalItems}
            currentPage={this.state.currentPage}
            onPageChange={(page) => this.fetchTeamsList(page)}
            onItemsPerPageChange={(size) => {
              this.setState({ itemsPerPage: size }, () => {
                this.fetchTeamsList(0);
              });
            }}
          />
        </div>

        <Modal show={this.state.isOpen} onHide={this.closeModal} size="lg">
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title>
              {this.state.isEditing ? "Edit Team" : "Create Team"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <form name="teamCreate" onSubmit={this.handleSubmit}>
              <div className="row m-3">
                <div className="col-12">
                  <div className="mb-3">
                    <label className="form-label">
                      Name <span style={{ color: "red" }}>*</span>
                    </label>
                    <input
                      type="text"
                      name="teamName"
                      value={this.state.teamName}
                      onChange={this.handleTeamNameChange}
                      placeholder="Enter team name"
                      className="form-control"
                      required
                    />
                    {this.state.isTeamExist && (
                      <div className="text-danger mt-1">
                        Team Name already exists in DB
                      </div>
                    )}
                    {this.state.errors.teamName && (
                      <div className="text-danger mt-1">{this.state.errors.teamName}</div>
                    )}
                  </div>
                </div>
                <div className="col-12">
                  <div className="mb-3">
                    <label className="form-label">
                      Status <span style={{ color: "red" }}>*</span>
                    </label>
                    <div className="mt-2">
                      <div className="form-check form-check-inline">
                        <input
                          type="radio"
                          className="form-check-input"
                          name="status"
                          id="statusActive"
                          value="1"
                          checked={this.state.status === "1"}
                          onChange={this.handleStatusChange}
                          required
                        />
                        <label className="form-check-label" htmlFor="statusActive">
                          Active
                        </label>
                      </div>
                      <div className="form-check form-check-inline">
                        <input
                          type="radio"
                          className="form-check-input"
                          name="status"
                          id="statusInactive"
                          value="0"
                          checked={this.state.status === "0"}
                          onChange={this.handleStatusChange}
                        />
                        <label className="form-check-label" htmlFor="statusInactive">
                          Inactive
                        </label>
                      </div>
                    </div>
                    {this.state.errors.status && (
                      <div className="text-danger mt-1">{this.state.errors.status}</div>
                    )}
                  </div>
                </div>
                <div className="col-12">
                  <div className="mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      name="description"
                      value={this.state.description}
                      onChange={this.handleInputChange}
                      rows="4"
                      placeholder="Enter team description"
                    />
                  </div>
                </div>
              </div>
              
              <div className="row m-3">
                <div className="col-6">
                  <div className="mb-3">
                    <label className="form-label">
                      Team Lead <span style={{ color: "red" }}>*</span>
                    </label>
                    <select
                      className="form-select"
                      name="teamLead"
                      value={this.state.teamLead}
                      onChange={this.handleInputChange}
                      required
                    >
                      <option value="">Select Team Lead</option>
                      {userList.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.firstName} {user.lastName}
                        </option>
                      ))}
                    </select>
                    {this.state.errors.teamLead && (
                      <div className="text-danger mt-1">{this.state.errors.teamLead}</div>
                    )}
                  </div>
                </div>
                <div className="col-6">
                  <div className="mb-3">
                    <label className="form-label">
                      Team Members <span style={{ color: "red" }}>*</span>
                    </label>
                    {teamLeadSelected ? (
                      <div className={`${classes.tableBorder} mt-2`} style={{ maxHeight: "300px", overflowY: "auto" }}>
                        <table className="table table-sm table-striped">
                          <thead className="sticky-top">
                            <tr>
                              <th>
                                <div className="form-check">
                                  <input
                                    type="checkbox"
                                    className="form-check-input"
                                    checked={isAllSelected}
                                    onChange={this.toggleSelectAllMembers}
                                  />
                                  <label className="form-check-label">Select All</label>
                                </div>
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {this.state.filteredUserList
                              .filter(
                                (user) =>
                                  user.id.toString() !== this.state.teamLead
                              )
                              .map((user) => (
                                <tr key={user.id}>
                                  <td>
                                    <div className="form-check">
                                      <input
                                        type="checkbox"
                                        className="form-check-input"
                                        checked={selectedMembers.includes(user.id)}
                                        onChange={() =>
                                          this.handleMemberChange(user.id)
                                        }
                                      />
                                      <label className="form-check-label">
                                        {user.firstName} {user.lastName}
                                      </label>
                                    </div>
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="alert alert-info mt-2">
                        Please select a team lead first to choose team members.
                      </div>
                    )}
                    {this.state.errors.selectedMembers && (
                      <div className="text-danger mt-1">{this.state.errors.selectedMembers}</div>
                    )}
                  </div>
                </div>
              </div>
            </form>
          </Modal.Body>
          <Modal.Footer className="modal-footer-modern">
            <Button
              variant="secondary"
              onClick={this.closeModal}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              form="teamCreate"
              disabled={this.state.isTeamExist}
              onClick={this.handleSubmit}
            >
              {this.state.isEditing ? "Update Team" : "Create Team"}
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal
          show={this.state.isDeleteModalOpen}
          onHide={this.closeDeleteModal}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title className="w-100 text-center">
              Delete Team
            </Modal.Title>
          </Modal.Header>
          <Modal.Body className="text-center">
            <div className="mb-3">
              <i className="fa fa-exclamation-triangle text-warning" style={{ fontSize: "3rem" }}></i>
            </div>
            <h5>Are you sure you want to delete this team?</h5>
            <p className="text-muted">This action cannot be undone.</p>
          </Modal.Body>
          <Modal.Footer className="modal-footer-modern justify-content-center">
            <Button
              variant="secondary"
              onClick={this.closeDeleteModal}
              className="me-2"
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={() => this.deleteTeamById(this.state.teamIdToDelete)}
            >
              <i className="fa fa-trash me-1"></i>
              Delete Team
            </Button>
          </Modal.Footer>
        </Modal>

        {this.state.isLoading && <Loader />}

        <Modal
          show={this.state.teamMembersModal}
          onHide={this.closeTeamMembers}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title className="w-100 text-center">
              Team Members
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div className="mb-2">
              <strong>Team Lead:&nbsp;</strong>
              {(() => {
                const team = this.state.teamsList.find(t => t.teamMemebers && t.teamMemebers.length && t.teamMemebers.every(id => this.state.currentTeamMembers.includes(id)));
                const teamLeadId = team ? team.teamLead : null;
                const lead = this.state.userList.find(user => user.id === teamLeadId);
                return lead ? `${lead.firstName} ${lead.lastName}` : 'Unknown';
              })()}
            </div>
            <div style={{ maxHeight: '360px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: 4 }}>
              <table className="table table-striped table-hover mb-0">
                <thead>
                  <tr>
                    <th style={{width: '40px'}}>#</th>
                    <th>Member Name</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.currentTeamMembers.map((id, index) => {
                    const user = this.state.userList.find(user => user.id === id);
                    return (
                      <tr key={id}>
                        <td>{index + 1}</td>
                        <td>{user ? `${user.firstName} ${user.lastName}` : 'Unknown User'}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              {this.state.currentTeamMembers.length === 0 && (
                <div className="text-center text-muted py-3">
                  No team members found.
                </div>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer className="modal-footer-modern">
            <Button variant="secondary" onClick={this.closeTeamMembers}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      </div>
    );
  }
}

export default TeamsManagement;
