.button{
    border:none;
    padding:10px 14px;
    border-radius: 10px;
    background-color: #ffff;
}
input[type=email]{
    outline: none;
    border-radius: 3px;
    width: 261px;
}
.slider {
    position: relative;
    display: inline-block;
    width: 40px; 
    height: 20px;
  }
  
  .slider input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .sliderTrack {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px; 
  }
  
  .sliderThumb {
    position: absolute;
    height: 16px; 
    width: 16px; 
    left: 2px; 
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
  
  input:checked + .sliderTrack {
    background-color: #2196F3;
  }
  
  input:checked + .sliderTrack .sliderThumb {
    transform: translateX(20px); 
  }
  