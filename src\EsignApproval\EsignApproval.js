import React, { Component } from "react";
import classes from "./EsignApproval.module.css";
import { rgb, StandardFonts } from "pdf-lib";
import Letter from "../components/images/Letter.jpg";
import {
  findById,
  getList,
  downloadeDocument,
  uploadDocument,
} from "../services/apiService";
import Notification from "../components/Notification/Notification";
import { <PERSON><PERSON>, <PERSON>dal, Tabs, Tab } from "react-bootstrap";
import <PERSON>Viewer from "react-file-viewer";
import DrawingPad from "./DrawingPad";
import Draggable from "react-draggable";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { PDFDocument } from "pdf-lib";
import logo from "../components/images/DMSLogo.png";
import { GlobalConstants } from "../constants/global-constants";
import { Navigate } from "react-router-dom";
import axios from "../services/api";
import Loader from "../components/loader/Loader";
import DocumentPreview from "../components/DocumentManagement/DocumentPreview";

class EsignApproval extends Component {
  state = {
    filePath: "",
    fileType: "",
    previewing: false,
    zoomLevel: 1,
    fileName: "",
    name: "",
    description:"",
    navigate: false,
    fileId: null,
    emailFields: [{ id: "", levelStatus: false, rejectStatus: false }],
    userList: [],
    userEmails: [],
    comments: "",
    show: false,
    key: "tab1",
    text: "",
    fontSize: 30, //initial font size
    cursiveState: true,
    selectedFont: "1",
    drawingData: null,
    signedContent: "",
    drawingPosition: { x: 0, y: 0 }, // Initial position
    drawingSignPosition: { x: 0, y: 0 }, // Initial position
    signedContents: [], // Array to hold signed text
    drawingData: [], // Array to hold drawing data
    drawingPositions: [], // Array to store the positions of each drawing
    drawingSignPositions: [], // Array to store the positions of each signature

    isSigned: false,
    isApproved: false,
    isRejected: false,
    levelId: null,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    rejectError: "",
    isLoading: false,
    filePathToPreview:"",
    remarks:"",
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  downloadRef = React.createRef();

  minFontSize = 12; //Minimum Font Size
  maxFontSize = 40; //Maximum Font Size
  textareaRef = React.createRef(); //reference to the textarea

  componentDidUpdate(prevProps, prevState) {
    if (prevState.text !== this.state.text) {
      this.adjustFontSize();
    }
  }

  handleButtons = () => {
    // this.setState({isLoading:true,});
    // const approvalIds = this.state.emailFields.map((field) => ({
    //   id: field.id,
    //   levelStatus: field.levelStatus,
    //   rejectStatus: field.rejectStatus,
    // }));
    // const sessionId = localStorage.getItem("id");
    // const matchedApproval = approvalIds.find(
    //   (approval) => approval.id.toString() === sessionId
    // );
    // if (matchedApproval) {
    //   this.setState({
    //     isApproved: matchedApproval.levelStatus,
    //     isRejected: matchedApproval.rejectStatus,
    //     isLoading:false,
    //   });
    // } else {
    //   this.setState({ isApproved: false,isLoading:false });
    // }
    // console.log(this.state.matchedApprovalLevel);
    // this.setState({
    //     isApproved: this.state.matchedApprovalLevel.levelStatus,
    //     isRejected: this.state.matchedApprovalLevel.rejectStatus,
    //     isLoading:false,
    // });
  };

  // Modify save drawing logic
  handleSaveDrawing = (data) => {
    this.setState({
      drawingData: [...this.state.drawingData, data],
      drawingPositions: [...this.state.drawingPositions, { x: 0, y: 0 }],
    });
    this.setState({ isSigned: true });
    this.handleClose(); // Close the modal after saving the drawing
  };

  // Modify save text logic
  handleSignName = () => {
    this.setState({
      signedContents: [...this.state.signedContents, this.state.text],
      drawingSignPositions: [
        ...this.state.drawingSignPositions,
        { x: 0, y: 0 },
      ],
      text: "", // Optionally reset text
    });
    this.setState({ isSigned: true });
    this.handleClose();
  };

  downloadeDocumentAttachment = async (filePath, fileName) => {
    try {
      // alert("hi");
      const data = await downloadeDocument(filePath);
      let blob = new Blob([data], { type: "application/octet-stream" });
      let url = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = fileName;
      anchor.target = "_blank";
      anchor.click();
      //window.location.href = response.url;
      //fileSaver.saveAs(blob, 'employees.json');
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  previewDocument = async (filePath, fileName) => {
    try {
      const data = await downloadeDocument(filePath);
      const blob = new Blob([data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      const fileType = fileName.split(".").pop().toLowerCase();
      this.setState({
        filePathToPreview: url,
        fileType: fileType,
        previewing: true,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Could not preview the document",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleError = (e) => {
    console.error("Error rendering file:", e);
  };

  adjustFontSize = () => {
    const textarea = this.textareaRef.current;

    // Reset to maximum font size before checking for overflow
    textarea.style.fontSize = `${this.maxFontSize}px`;

    let currentFontSize = this.maxFontSize;

    // Decrease font size if content overflows
    while (
      textarea.scrollHeight > textarea.clientHeight &&
      currentFontSize > this.minFontSize
    ) {
      currentFontSize -= 1;
      textarea.style.fontSize = `${currentFontSize}px`;
    }

    this.setState({ fontSize: currentFontSize });
  };

  handleChange = (e) => {
    this.setState({ text: e.target.value });
  };

  // Function to show the modal
  handleShow = () => {
    if (!this.state.isSigned) {
      this.setState({ show: true });
    }
  };

  // Function to hide the modal
  handleClose = () => {
    this.setState({ show: false });
  };

  // Function to handle tab change
  handleSelect = (key) => {
    this.setState({ key });
  };

  handleFontChange = (font) => {
    if (font === "1") this.setState({ cursiveState: true, selectedFont: "1" });
    if (font === "2") this.setState({ cursiveState: false, selectedFont: "2" });
  };

  componentDidMount() {
    this.previewDocument(
      sessionStorage.getItem("filePath"),
      sessionStorage.getItem("fileName")
    );
    this.setState({
      previewing: true,
      fileName: sessionStorage.getItem("fileName"),
      filePath: sessionStorage.getItem("filePath"),
      fileId: sessionStorage.getItem("documentsAttachmentId"),
    });

    this.getApprovalWorkFlowById();
    this.fetchUserList();
  }

  componentWillUnmount() {
    this.setState({
      fileName: "",
      fileId: null,
    });
  }

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=100&search=&sort=";
    try {
      const data = await getList(api);
      this.setState({ userList: data.data.content });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  getApprovalWorkFlowById = async () => {
    this.setState({ isLoading: true });
    const api = `/approvalworkflow/doc1/eSign/${sessionStorage.getItem(
      "fileId"
    )}`;
    try {
      const response = await findById(api);
      console.log(response.data);
      const sessionId = localStorage.getItem("id");
      const matchedApprovalLevel = response.data.approvalLevelDTO.find(
        (level) => level.usersId.toString() === sessionId
      );
      this.setState({
        isApproved: matchedApprovalLevel.levelStatus,
        isRejected: matchedApprovalLevel.rejectStatus,
      });

      this.setState({
        emailFields: response.data.approvalLevelDTO.map((level) => ({
          id: level.usersId,
          levelStatus: level.levelStatus,
          rejectStatus: level.rejectStatus,
        })),
      });

      if (matchedApprovalLevel) {
        console.log(matchedApprovalLevel);
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
            rejectStatus: level.rejectStatus,
          })),
          name: response.data.name,
          description:response.data.description,
          levelId: matchedApprovalLevel.id, // Storing the level id
          remarks:matchedApprovalLevel.remarks,
          isLoading: false,
        });
      } else {
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
            rejectStatus: level.rejectStatus,
          })),
          name: response.data.name,
          isLoading: false,
        });
      }
    } catch (error) {
      this.setState({ isLoading: false });
    }
    //this.handleButtons();
  };

  handleCommentChange = (event) => {
    this.setState({ rejectError: "", comments: event.target.value });
  };

  handleApprove = () => {
    //     const approvalIds = [...new Set(this.state.emailFields.map(field => field.id))];
    //     console.log("Approval IDs : ",approvalIds)
    //     console.log("Comments : ",this.state.comments)
    //     console.log("Approved Document : ",sessionStorage.getItem("fileId") , " Approved by UserID: ",localStorage.getItem("id"))
    const api = `${GlobalConstants.globalURL}/approvalworkflow/docUpdateLevelStatus/${this.state.levelId}?remarks=${this.state.comments}`;
    //alert(api);
    try {
      const response = axios.put(api);
      this.setState({
        notification: {
          message: `Document ${sessionStorage.getItem(
            "fileName"
          )} signed Successfully`,
          type: "success",
          show: true,
        },
        // navigate: true,
      });
      setTimeout(() => {
        this.setState({ navigate: true });
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleSign = () => {
    console.log("Comments : ", this.state.comments);
    console.log(
      "Approved Document : ",
      sessionStorage.getItem("documentsAttachmentId"),
      " Approved by UserID: ",
      localStorage.getItem("id")
    );
    this.handleApprove();

    const { signedContent, drawingData } = this.state;
    //this.handlenewDownload();
    // const combinedContent = this.combineContent(signedContent, drawingData);
    // this.downloadDocument(combinedContent);
    //this.setState({navigate:true,});
  };

  handleReject = () => {
    if (this.state.comments === "") {
      this.setState({ rejectError: "required" });
      return;
    }
    console.log(
      "Rejected Document : ",
      sessionStorage.getItem("fileId"),
      " Rejected by UserID : ",
      localStorage.getItem("id")
    );
    const api = `${GlobalConstants.globalURL}/approvalworkflow/docRejectLevelStatus/${this.state.levelId}?remarks=${this.state.comments}`;
    try {
      const response = axios.put(api);
      this.setState({
        notification: {
          message: `Document ${this.state.fileName} eSign rejected successfully`,
          type: "success",
          show: true,
        },
      });
      setTimeout(() => {
        this.setState({ navigate: true });
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  resetFormFileds = () => {
    this.setState({ text: "", fontSize: 30, selectedFont: "1" });
  };

  handlenewDownload = async () => {
    // Download the existing PDF file data
    const data = await downloadeDocument(sessionStorage.getItem("filePath"));
    let blob = new Blob([data], { type: "application/octet-stream" });
    const arrayBuffer = await new Response(blob).arrayBuffer();

    // Load the existing PDF document
    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const pages = pdfDoc.getPages();

    // Embed Helvetica font from StandardFonts (Ensure this is correctly loaded)
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    // Add all drawing images (if any)
    for (let i = 0; i < this.state.drawingData.length; i++) {
      const imageBytes = await fetch(this.state.drawingData[i]).then((res) =>
        res.arrayBuffer()
      );
      const image = await pdfDoc.embedPng(imageBytes);
      const { width, height } = image.size();
      const page = pages[pages.length - 1]; // Drawing on the last page

      page.drawImage(image, {
        x: this.state.drawingPositions[i].x,
        y: this.state.drawingPositions[i].y,
        width,
        height,
      });
    }

    // Add signed text content (if any)
    for (let i = 0; i < this.state.signedContents.length; i++) {
      const page = pages[pages.length - 1]; // Drawing on the last page

      page.drawText(this.state.signedContents[i], {
        x: this.state.drawingSignPositions[i].x,
        y: this.state.drawingSignPositions[i].y,
        size: this.state.fontSize,
        font: font, // Use the embedded font here
        color: rgb(0, 0, 0), // Optional: set text color (black in this case)
      });
    }

    // Save the modified PDF
    const pdfBytes = await pdfDoc.save();
    const blob1 = new Blob([pdfBytes], { type: "application/pdf" });

    // Create a download link and trigger download
    const url = URL.createObjectURL(blob1);
    const a = document.createElement("a");
    a.href = url;
    a.download = sessionStorage.getItem("fileName"); // Ensure the correct file name is used
    a.click();
    URL.revokeObjectURL(url); // Clean up the URL
  };

  updateESignDocument = async (docData) => {
    const api = "/documentsattachmentdetail/updateEsignDocument";
    try {
      const response = await uploadDocument(api, docData);
      this.setState({
        notification: {
          message: `Document ${this.state.fileName} eSign successful`,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  // handleDownload = async () => {
  //   const element = this.downloadRef.current;
  //   if (!element) return;

  //   // Capture the FileViewer

  //   const canvas = await html2canvas(element, {
  //     useCORS: true,
  //     scale: 2,
  //   });

  //   const imgData = canvas.toDataURL("image/jpeg", 1);

  //   // Create a new canvas to combine the document and drawing
  //   const combinedCanvas = document.createElement("canvas");
  //   const context = combinedCanvas.getContext("2d");

  //   combinedCanvas.width = canvas.width;
  //   combinedCanvas.height = canvas.height;

  //   const img = new Image();
  //   img.src = imgData;

  //   img.onload = () => {
  //     context.drawImage(img, 0, 0);

  //     const { drawingData, signedContent, drawingPosition } = this.state;

  //     const drawImage = (src, position) => {
  //       const drawingImg = new Image();
  //       drawingImg.src = src;
  //       drawingImg.onload = () => {
  //         context.drawImage(drawingImg, position.x, position.y);
  //        // this.downloadPDF(combinedCanvas); // Call download after image is drawn
  //       };
  //       drawingImg.onerror = () => {
  //         alert("Error loading drawing image");
  //       };
  //     };

  //     if (drawingData) {
  //       drawImage(drawingData, drawingPosition);
  //     } else if (signedContent) {
  //       // Use a temporary canvas to render the text
  //       const textCanvas = document.createElement("canvas");
  //       const textContext = textCanvas.getContext("2d");
  //       textContext.font = `${this.state.fontSize}px ${
  //         this.state.selectedFont === "1" ? "Cedarville Cursive" : "Arial"
  //       }`;
  //       const metrics = textContext.measureText(signedContent);
  //       textCanvas.width = metrics.width;
  //       textCanvas.height = this.state.fontSize; // Height for a single line of text

  //       // Render the text onto the textCanvas
  //       textContext.fillStyle = "black"; // or any color you want
  //       textContext.fillText(signedContent, 0, this.state.fontSize); // Draw text at the top left corner

  //       const signedContentImage = new Image();
  //       signedContentImage.src = textCanvas.toDataURL("image/jpeg", 1);
  //       signedContentImage.onload = () => {
  //         context.drawImage(
  //           signedContentImage,
  //           this.state.drawingPosition.x,
  //           this.state.drawingPosition.y
  //         );
  //         this.downloadPDF(combinedCanvas); // Call download after all images are drawn
  //       };
  //     } else {
  //       alert("You have not signed yet");
  //     }
  //   };

  //   img.onerror = () => {
  //     alert("Error loading the document image");
  //   };
  // };

  // downloadPDF = (canvas) => {
  //   //const pdf = new jsPDF("p", "mm", "a4");
  //   const pdf = new jsPDF();
  //   const imgData = canvas.toDataURL("image/jpeg", 1);

  //   // Add the image to the PDF
  //   pdf.addImage(
  //     imgData,
  //     "JPEG",
  //     0,
  //     0,
  //     pdf.internal.pageSize.getWidth(),
  //     pdf.internal.pageSize.getHeight()
  //   );

  //   // Save the PDF
  //   pdf.save("signed_document.pdf");
  // };

  // Modify handleDrag to update specific drawing positions
  handleDrag = (e, data, index) => {
    const newPositions = [...this.state.drawingPositions];
    newPositions[index] = { x: data.x, y: data.y };
    this.setState({ drawingPositions: newPositions });
    this.setState({ isSigned: true });
  };

  // Modify handleDragSignedContent to update specific signature positions
  handleDragSignedContent = (e, data, index) => {
    const newPositions = [...this.state.drawingSignPositions];
    newPositions[index] = { x: data.x, y: data.y };
    this.setState({ drawingSignPositions: newPositions });
    this.setState({ isSigned: true });
  };

  closePreviewModal = () => {

  };

  render() {
    const { previewing, filePath, fileType, fileName } = this.state;
    const approvalIds = this.state.emailFields.map((field) => ({
      id: field.id,
      levelStatus: field.levelStatus,
      rejectStatus: field.rejectStatus,
    }));
    const { key, drawingData } = this.state;
    if (this.state.navigate) {
      return <Navigate to="/newDS/document-management" />;
    }
    return (
      <>
        <div className="container">
          {this.state.notification.show && (
            <Notification
              message={this.state.notification.message}
              type={this.state.notification.type}
              onClose={this.closeNotification}
            />
          )}
          <div className="row">
            <div className="col-sm-8 offset-sm-2">
              <div className="card mt-2">
                {this.state.isRejected && (
                  <h3 className="text-center">Rejected</h3>
                )}
                <img
                  src={Letter}
                  className={classes.letterImg}
                  alt="LetterHeadLogo"
                />
                <div className="card-body">
                  {this.state.isRejected ? (
                    <>
                      <h5 className="card-title text-center">
                        Rejected Document is
                      </h5>
                      <h6 className="text-center text-muted">Remarks : "{this.state.remarks}"</h6>
                    </>
                  ) : this.state.isApproved ? (
                    <>
                      <h5 className="card-title text-center">
                        Signed Document Is
                      </h5>
                      <h6 className="text-center text-muted">Remarks : "{this.state.remarks}"</h6>
                    </>
                  ) : (
                    <h5 className="card-title text-center">
                      {sessionStorage.getItem("assignedBy")} is asking you to
                      Sign a Document
                    </h5>
                  )}
                  {/* <p className="card-text text-center">Locations : Projects</p> */}
                  <p className={classes.fileName}>
                    <i
                      className={`${classes.fileIcon} fa fa-file-text`}
                      aria-hidden="true"
                    ></i>
                    {sessionStorage.getItem("fileName")}
                    <a
                      style={{
                        color: "blue",
                        textDecoration: "underline",
                        cursor: "pointer",
                      }}
                    >
                      <i
                        title="download file"
                        onClick={() =>
                          this.downloadeDocumentAttachment(
                            sessionStorage.getItem("filePath"),
                            sessionStorage.getItem("fileName")
                          )
                        }
                        className={`${classes.downloadIcon} fa fa-download`}
                        aria-hidden="true"
                      ></i>
                    </a>
                  </p>
                  <h6 className="text-primary p-2">Note : {this.state.name}</h6>
                  <p className="text-primary p-2">Description : {this.state.description}</p>
                  <h5>Users in this esign workflow</h5>
                <div>
                  {this.state.userList
                    .filter((user) =>
                      approvalIds.some((approval) => approval.id === user.id)
                    ) // Check if user.id is in approvalIds
                    .map((user, index) => {
                      // Find the corresponding approval object
                      const approval = approvalIds.find(
                        (approval) => approval.id === user.id
                      );
                      const isChecked = approval ? approval.levelStatus : false; // Check levelStatus
                      const isDisabled = approval
                        ? !approval.levelStatus
                        : false; // Disable if levelStatus is false

                      return (
                        <div className="form-check" key={index}>
                          <input
                            className="form-check-input"
                            type="checkbox" // Assuming you meant radio buttons
                            value={user.id}
                            id={`emailList-${index}`}
                            checked={isChecked}
                            disabled={isDisabled}
                          />
                          <label
                            className="form-check-label"
                            htmlFor={`emailList-${index}`}
                          >
                            {user.email}
                            {/* Adjust to show actual name if available */}
                          </label>
                        </div>
                      );
                    })}
                </div>
                </div>
              </div>
            </div>
          </div>
          {this.state.isRejected ? (
            ""
          ) : this.state.isApproved ? (
            ""
          ) : (
            <div className="row">
              <div className="col-sm-8 offset-sm-2">
                <div
                  onClick={this.handleShow}
                  style={{
                    cursor: "pointer",
                    height: "100vh",
                    overflow: "auto",
                  }}
                >
                  {this.state.filePathToPreview && (
                    <div
                      ref={this.downloadRef}
                      style={{
                        transform: `scale(${this.state.zoomLevel})`,
                        transformOrigin: "top left",
                      }}
                    >
                      <div style={{ position: "relative", height: "100%" }}>
                        {/* <FileViewer
                          fileType={fileType}
                          filePath={filePath}
                          onError={this.handleError}
                          style={{ width: "100%", height: "100%" }}
                        /> */}
                        <DocumentPreview 
                          filePath={this.state.filePathToPreview}
                          fileName={sessionStorage.getItem("fileName")}
                          closePreview={this.closePreviewModal}
                        />

                        {this.state.drawingData.map((drawing, index) => (
                          <Draggable
                            key={`drawing-${index}`}
                            onDrag={(e, data) =>
                              this.handleDrag(e, data, index)
                            }
                            position={this.state.drawingPositions[index]}
                          >
                            <div
                              style={{ position: "absolute", cursor: "move" }}
                            >
                              <img
                                src={drawing}
                                alt="Drawing"
                                style={{
                                  maxWidth: "100%",
                                  pointerEvents: "all",
                                }}
                              />
                            </div>
                          </Draggable>
                        ))}

                        {this.state.signedContents.map(
                          (signedContent, index) => (
                            <Draggable
                              key={`signed-${index}`}
                              onDrag={(e, data) =>
                                this.handleDragSignedContent(e, data, index)
                              }
                              position={this.state.drawingSignPositions[index]}
                            >
                              <div
                                style={{
                                  position: "absolute",
                                  color: "black",
                                  cursor: "move",
                                  fontFamily:
                                    this.state.selectedFont === "1"
                                      ? "Cedarville Cursive"
                                      : "Arial",
                                  fontSize: `${this.state.fontSize}px`,
                                }}
                              >
                                {signedContent}
                              </div>
                            </Draggable>
                          )
                        )}

                        {/* Display the signed content here */}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          {/* <button
            type="button"
            className="btn btn-primary"
            onClick={this.handlenewDownload}
          >
            Download Document
          </button> */}
          {this.state.isRejected ? (
            ""
          ) : this.state.isApproved ? (
            ""
          ) : (
            <div className="row">
              <div className="col-sm-8 offset-sm-2">
                <div className="card">
                  <div className="card-body mx-5 mt-3 mb-4">
                    <div className="mt-4">
                      <label htmlFor="comments">Comments</label>
                      <textarea
                        className="form-control"
                        placeholder="Type Here"
                        rows="3"
                        id="comments"
                        value={this.state.comments}
                        onChange={this.handleCommentChange}
                      ></textarea>
                      {this.state.rejectError && (
                        <span style={{ color: "red" }}>Comments Required</span>
                      )}
                    </div>

                    <div className="row mt-4">
                      <div className="col-12 text-center">
                        <button
                          type="button"
                          className="btn btn-success mx-3"
                          onClick={this.handleSign}
                          disabled={
                            this.state.isApproved || this.state.isRejected
                          }
                        >
                          SIGN
                        </button>
                        <button
                          type="button"
                          className="btn btn-danger mx-3"
                          onClick={this.handleReject}
                          disabled={
                            this.state.isApproved || this.state.isRejected
                          }
                        >
                          REJECT
                        </button>
                        <br />
                        {/* <button
                        type="button"
                        className="btn btn-link mt-4"
                        style={{ color: "black", fontWeight: "bold" }}
                      >
                        Delegate
                      </button> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <Modal
          show={this.state.show}
          onHide={this.handleClose}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title>E-Signature</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Tabs
              id="sign-tab"
              activeKey={this.state.key}
              onSelect={this.handleSelect}
              className="mb-3"
            >
              <Tab
                eventKey="tab1"
                title={
                  <span
                    style={{
                      backgroundColor: key === "tab1" ? "#5baedc" : "",
                      padding: "10px 20px",
                      borderRadius: "5px",
                      color: key === "tab1" ? "white" : "black",
                    }}
                  >
                    Draw
                  </span>
                }
              >
                {/* Content for Tab 1 */}
                <DrawingPad onSave={this.handleSaveDrawing} />
              </Tab>
              <Tab
                eventKey="tab2"
                title={
                  <span
                    style={{
                      backgroundColor: key === "tab2" ? "#5baedc" : "",
                      padding: "10px 20px",
                      borderRadius: "5px",
                      color: key === "tab2" ? "white" : "black",
                    }}
                  >
                    Type
                  </span>
                }
              >
                {/* Content for Tab 2 */}
                <div className="container">
                  <div className="row">
                    <div className="col-12">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Your Name"
                        value={this.state.text}
                        onChange={this.handleChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="row mt-4">
                    <div className="col-12">
                      <select
                        id="fontFamily"
                        value={this.state.selectedFont}
                        className="form-control"
                        onChange={(e) => this.handleFontChange(e.target.value)}
                      >
                        <option value="1">Handwriting</option>
                        <option value="2">Neutral</option>
                      </select>
                    </div>
                  </div>

                  <div className="row mt-4">
                    <div className="col-12">
                      <textarea
                        ref={this.textareaRef}
                        value={this.state.text}
                        readOnly
                        className="form-control"
                        style={{
                          height: "150px",
                          fontSize: `${this.state.fontSize}px`,
                          resize: "none",
                          overflow: "hidden", // Hide scrollbars initially
                          textAlign: "center",
                          fontFamily: this.state.cursiveState
                            ? "Cedarville Cursive"
                            : "Arial",
                        }}
                      ></textarea>
                    </div>
                  </div>

                  <div className="row mt-4">
                    <div className="col-12 text-center">
                      <button
                        type="button"
                        className="btn btn-link"
                        onClick={this.resetFormFileds}
                      >
                        Clear
                      </button>
                      <button
                        type="submit"
                        onClick={this.handleSignName}
                        className="btn btn-primary"
                        disabled={!this.state.text}
                      >
                        SIGN PDF
                      </button>
                    </div>
                  </div>
                </div>
              </Tab>
            </Tabs>
          </Modal.Body>
        </Modal>
        {this.state.isLoading && <Loader />}
      </>
    );
  }
}

export default EsignApproval;
