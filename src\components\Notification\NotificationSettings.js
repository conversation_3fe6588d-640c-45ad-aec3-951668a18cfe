import React, { Component } from "react";
import classes from "./Email.module.css";
import Notification from "./Notification";
import { Table } from "react-bootstrap";
import { addNew, getList, editById } from "../../services/apiService";
import { DataTable } from "../Table/DataTable";

class NotificationSettings extends Component {
  constructor(props) {
    super(props);
    this.state = {
      notification: {
        message: "",
        type: "",
        show: false,
      },
      Id: null,
      isEditMode: false,
      activities: [
        "Added File/Folder",
        "Deleted File/Folder",
        "Restore File/Folder",
        "Edit Note",
        "Edit date",
        "Edit Due Date",
        "Added Due Date",
        "Edit Document Number",
        "Download",
        "Preview",
        "Lock",
        "Unlock",
        "Add a version",
        "Remove a version",
        "Restore a version",
        "Retention end",
        "Add a signee",
        "Remove a signee",
        "Add metadata",
        "Remove metadata",
        "Edit metadata value",
        "Document approval started",
        "Document approval rejected",
        "Document approval finished",
        // "Document approved",
        // "Document rejected",
        "Document eSign started",
        "Document eSign rejected",
        "Document eSign finished",
        "User approved the document",
        "User acknowledged the document",
        "User signed the document",
        "Document acknowledgement started",
        "Document acknowledgement finished",
        // "Document acknowledgement cancelled",
      ],
      notificationStatus: {},
    };
  }

  componentDidMount() {
    this.fetchNotifications();
  }

  fetchNotifications = async () => {
    const api = `/managenotification/list`;
    try {
      const response = await getList(api);
      const data = response.data;
      if (data) {
        this.setState({
          Id: data.id,
          isEditMode: true,
        });
  
        const notificationStatus = {};
  
        this.state.activities.forEach((activity) => {
          const normalizedActivity = activity.replace(/[\s/]+/g, '').toLowerCase();
  
          notificationStatus[activity] = {
            SMS: data.smsEnabled[normalizedActivity] || false,
            Email: data.emailEnabled[normalizedActivity] || false,
            Web: data.webEnabled[normalizedActivity] || false,
            Whatsapp: data.whatsappEnabled[normalizedActivity] || false,
            InApp: data.inAppEnabled[normalizedActivity] || false,
          };
        });
  
        this.setState({
          notificationStatus: notificationStatus
        });
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  handleCheckboxChange = (activity, type) => {
    this.setState((prevState) => {
      const updatedStatus = {
        ...prevState.notificationStatus,
        [activity]: {
          SMS: prevState.notificationStatus[activity]?.SMS || false,
          Email: prevState.notificationStatus[activity]?.Email || false,
          Web: prevState.notificationStatus[activity]?.Web || false,
          Whatsapp: prevState.notificationStatus[activity]?.Whatsapp || false,
          InApp: prevState.notificationStatus[activity]?.InApp || false,
          [type]: !prevState.notificationStatus[activity]?.[type],
        },
      };
      return { notificationStatus: updatedStatus };
    });
  };

  handleSave = () => {
    const notificationLog = {
      smsEnabled: {},
      emailEnabled: {},
      webEnabled: {},
      whatsappEnabled: {},
      inAppEnabled: {},
    };
  
    this.state.activities.forEach((activity) => {
      const normalizedActivity = activity.replace(/[\s/]+/g, '').toLowerCase();
  
      notificationLog.smsEnabled[normalizedActivity] =
        this.state.notificationStatus[activity]?.SMS || false;
      notificationLog.emailEnabled[normalizedActivity] =
        this.state.notificationStatus[activity]?.Email || false;
      notificationLog.webEnabled[normalizedActivity] =
        this.state.notificationStatus[activity]?.Web || false;
      notificationLog.whatsappEnabled[normalizedActivity] =
        this.state.notificationStatus[activity]?.Whatsapp || false;
      notificationLog.inAppEnabled[normalizedActivity] =
        this.state.notificationStatus[activity]?.InApp || false;
    });
  
    console.log("Notification Log:", notificationLog);
  
    if (this.state.isEditMode) {
      this.updateData(notificationLog);
    } else {
      this.submitData(notificationLog);
    }
  };
  

  submitData = async (obj) => {
    const api = "/managenotification";
    try {
      const response = await addNew(api, obj);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  updateData = async (obj) => {
    const api = `/managenotification/${this.state.Id}`;
    try {
      const response = await editById(api, obj);
      this.setState({
        notification: {
          message: "Notification Settings Updated",
          type: "success",
          show: true,
        },
      });
      this.componentDidMount();
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleClear = () => {
    this.setState({ notificationStatus: {} });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  render() {
    // Prepare data for DataTable
    const data = this.state.activities.map((activity) => ({
      activity,
      SMS: this.state.notificationStatus[activity]?.SMS || false,
      Email: this.state.notificationStatus[activity]?.Email || false,
      // Web: this.state.notificationStatus[activity]?.Web || false, // Uncomment if needed
      Whatsapp: this.state.notificationStatus[activity]?.Whatsapp || false,
      InApp: this.state.notificationStatus[activity]?.InApp || false,
    }));

    // Define columns for DataTable
    const columns = [
      {
        key: "activity",
        header: "Activity Name",
        sortable: true,
        render: (value) => <strong>{value}</strong>,
      },
      {
        key: "SMS",
        header: "SMS Enabled",
        render: (_value, row) => (
          <input
            type="checkbox"
            checked={row.SMS}
            onChange={() => this.handleCheckboxChange(row.activity, "SMS")}
          />
        ),
      },
      {
        key: "Email",
        header: "Email Enabled",
        render: (_value, row) => (
          <input
            type="checkbox"
            checked={row.Email}
            onChange={() => this.handleCheckboxChange(row.activity, "Email")}
          />
        ),
      },
      // {
      //   key: "Web",
      //   header: "Web Enabled",
      //   render: (_value, row) => (
      //     <input
      //       type="checkbox"
      //       checked={row.Web}
      //       onChange={() => this.handleCheckboxChange(row.activity, "Web")}
      //     />
      //   ),
      // },
       {
        key: "InApp",
        header: "In-App Enabled",
        render: (_value, row) => (
          <input
            type="checkbox"
            checked={row.Web}
            onChange={() => this.handleCheckboxChange(row.activity, "InApp")}
          />
        ),
      },
      {
        key: "Whatsapp",
        header: "Whatsapp",
        render: (_value, row) => (
          <input
            type="checkbox"
            checked={row.Whatsapp}
            onChange={() => this.handleCheckboxChange(row.activity, "Whatsapp")}
          />
        ),
      },
    ];

    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Notification Settings</h4>
            <hr />
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 p-3 border rounded">
            <h4>
              <i className="fa fa-list"></i>&nbsp;List of Notification Activity
            </h4>
            <hr />
            <div className="row mt-3">
              <div className="table-responsive">
                <DataTable
                  data={data}
                  columns={columns}
                  searchable={true}
                  showSno={true}
                  className="table-striped table-hover"
                  itemsPerPage={data.length}
                  hidePagination
                />
              </div>
            </div>
            <div>
              <button
                className="btn btn-primary mt-3"
                onClick={this.handleSave}
              >
                {this.state.isEditMode ? "Update" : "Save"}
              </button>
              &nbsp;&nbsp;
              <button
                className="btn btn-danger mt-3"
                onClick={this.handleClear}
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default NotificationSettings;
