import React from "react";
import classes from "./EmailApproval.module.css";
import Letter from "../images/Letter.jpg";
import {
  findById,
  getList,
  downloadeDocument,
} from "../../services/apiService";
import { GlobalConstants } from "../../constants/global-constants";
import { Navigate } from "react-router-dom";
import axios from "../../services/api";
import Notification from "../Notification/Notification";
import Loader from "../loader/Loader";
import { Modal, Button } from "react-bootstrap";
import DocumentPreview from "../DocumentManagement/DocumentPreview";

class EmailApproval extends React.Component {
  state = {
    fileName: "",
    name: "",
    description: "",
    fileId: null,
    emailFields: [{ id: "", levelStatus: false, rejectStatus: false }],
    userList: [],
    userEmails: [],
    comments: "",
    approvalDocId: null,
    navigate: false,
    isApproved: false,
    isRejected: false,
    levelId: null,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    rejectError: "",
    isLoading: false,
    isPreviewModalOpen: false,
    previewing: false,
    filePath: "",
    remarks:"",
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  componentDidMount() {
    this.getApprovalWorkFlowById();
    this.fetchUserList();
  }
  componentWillUnmount() {
    this.setState({
      fileName: "",
      fileId: null,
    });
  }

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=100&search=&sort=";
    try {
      const data = await getList(api);
      this.setState({ userList: data.data.content });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  getApprovalWorkFlowById = async () => {
    this.setState({ isLoading: true });
    const api = `/approvalworkflow/doc1/approve/${sessionStorage.getItem(
      "fileId"
    )}`;
    try {
      const response = await findById(api);
      //console.log(response.data.id, "===========");

      const sessionId = localStorage.getItem("id");
      const matchedApprovalLevel = response.data.approvalLevelDTO.find(
        (level) => level.usersId.toString() === sessionId
      );

      this.setState({
        isApproved: matchedApprovalLevel.levelStatus,
        isRejected: matchedApprovalLevel.rejectStatus,
      });

      if (matchedApprovalLevel) {
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
            rejectStatus: level.rejectStatus,
          })),
          name: response.data.name,
          description: response.data.description,
          approvalDocId: response.data.id,
          levelId: matchedApprovalLevel.id, // Storing the level id
          remarks:matchedApprovalLevel.remarks,
          isLoading: false,
        });
      } else {
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
            rejectStatus: level.rejectStatus,
          })),
          name: response.data.name,
          approvalDocId: response.data.id,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error("Error fetching approval workflow:", error);
      this.setState({ isLoading: false });
    }

    //this.handleButtons();
  };

  handleCommentChange = (event) => {
    this.setState({ rejectError: "", comments: event.target.value });
  };

  handleApprove = async () => {
    console.log("Comments : ", this.state.comments);
    console.log(
      "Approved Document : ",
      sessionStorage.getItem("fileId"),
      " Approved by UserID: ",
      localStorage.getItem("id")
    );
    //const api=`${GlobalConstants.globalURL}/approvalworkflow/docapprove/${this.state.approvalDocId}/${sessionStorage.getItem("id")}`;
    const api = `${GlobalConstants.globalURL}/approvalworkflow/docUpdateLevelStatus/${this.state.levelId}?remarks=${this.state.comments}`;
    try {
      const response = axios.put(api);
      this.setState({
        notification: {
          message: `Document ${sessionStorage.getItem(
            "fileName"
          )} Approved Successfully!`,
          type: "success",
          show: true,
        },
      });
      setTimeout(() => {
        this.setState({ navigate: true });
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleReject = () => {
    if (this.state.comments === "") {
      this.setState({ rejectError: "Required" });
      return;
    }
    console.log(
      "Rejected Document : ",
      sessionStorage.getItem("fileId"),
      " Rejected by UserID : ",
      localStorage.getItem("id")
    );
    const api = `${GlobalConstants.globalURL}/approvalworkflow/docRejectLevelStatus/${this.state.levelId}?remarks=${this.state.comments}`;
    try {
      const response = axios.put(api);
      this.setState({
        notification: {
          message: `Document ${this.state.fileName} approval rejected successfully`,
          type: "success",
          show: true,
        },
      });
      setTimeout(() => {
        this.setState({ navigate: true });
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  downloadeDocumentAttachment = async (filePath, fileName) => {
    try {
      // alert("hi");
      const data = await downloadeDocument(filePath);
      let blob = new Blob([data], { type: "application/octet-stream" });
      let url = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = fileName;
      anchor.target = "_blank";
      anchor.click();
      //window.location.href = response.url;
      //fileSaver.saveAs(blob, 'employees.json');
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleButtons = () => {
    //this.setState({isLoading:true})
    // const approvalIds = this.state.emailFields.map((field) => ({
    //   id: field.id,
    //   levelStatus: field.levelStatus,
    //   rejectStatus: field.rejectStatus,
    // }));
    // const sessionId = localStorage.getItem("id");
    // const matchedApproval = approvalIds.find(
    //   (approval) => approval.id.toString() === sessionId
    // );
    // console.log(this.state.matchedApprovalLevel);
    // // if (matchedApproval) {
    //   this.setState({
    //     isApproved: this.state.matchedApprovalLevel.levelStatus,
    //     isRejected: this.state.matchedApprovalLevel.rejectStatus,
    //     isLoading:false,
    //   });
    // } else {
    //   this.setState({ isApproved: false,isLoading:false });
    // }
  };

  previewDocument = async (filePath, fileName) => {
    this.setState({
      isPreviewModalOpen: false,
      previewing: false,
      filePath: "",
    });

    try {
      const data = await downloadeDocument(filePath);
      const blob = new Blob([data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      this.setState({
        filePath: url,
        previewing: true,
        isPreviewModalOpen: true,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Could not preview the document",
          type: "error",
          show: true,
        },
      });
    }
  };

  closePreviewModal = () => {
    this.setState({
      filePath: "",
      isPreviewModalOpen: false,
      previewing: false,
    });
  };

  render() {
    const approvalIds = this.state.emailFields.map((field) => ({
      id: field.id,
      levelStatus: field.levelStatus,
      rejectStatus: field.rejectStatus,
    }));

    if (this.state.navigate) {
      return <Navigate to="/newDS/document-management" />;
    }

    return (
      <div className="container">
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row">
          <div className="col-sm-8 offset-sm-2">
            <div className="card mt-2">
              {this.state.isRejected && (
                <>
                  <h3 className="text-center">Rejected</h3>
                  <h6 className="text-center text-muted">Remarks : "{this.state.remarks}"</h6>
                </>
              )}
              <img
                src={Letter}
                className={classes.letterImg}
                alt="LetterHeadLogo"
              />
              <div className="card-body">
                {this.state.isRejected ? (
                  <h5 className="card-title text-center">Document is</h5>
                ) : this.state.isApproved ? (
                  <>
                    <h5 className="card-title text-center">
                    Approved document is
                    </h5>
                    <h6 className="text-center text-muted">Remarks : "{this.state.remarks}"</h6>
                  </>
                  
                ) : (
                  <h5 className="card-title text-center">
                    {sessionStorage.getItem("assignedBy")} is asking you to
                    Approve a Document
                  </h5>
                )}
                {/* <p className="card-text text-center">Locations : Projects</p> */}
                <p className={classes.fileName}>
                  <span
                    style={{ cursor: "pointer" }}
                    onClick={(e) => {
                      e.preventDefault();
                      this.previewDocument(
                        sessionStorage.getItem("filePath"),
                        sessionStorage.getItem("fileName")
                      );
                    }}
                  >
                    <i
                      className={`${classes.fileIcon} fa fa-file-text`}
                      aria-hidden="true"
                    ></i>
                    {sessionStorage.getItem("fileName")}
                  </span>
                  <a
                    style={{
                      color: "blue",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                  >
                    <i
                      title="download file"
                      onClick={() =>
                        this.downloadeDocumentAttachment(
                          sessionStorage.getItem("filePath"),
                          sessionStorage.getItem("fileName")
                        )
                      }
                      className={`${classes.downloadIcon} fa fa-download`}
                      aria-hidden="true"
                    ></i>
                  </a>
                </p>
                <h6 className="text-primary p-2">Note : {this.state.name}</h6>
                <p className="text-primary p-2">
                  Description : {this.state.description}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-8 offset-sm-2">
            <div className="card">
              <div className="card-body mx-5 mt-3 mb-4">
                <h5>Users in this approval workflow</h5>
                <div>
                  {this.state.userList
                    .filter((user) =>
                      approvalIds.some((approval) => approval.id === user.id)
                    ) // Check if user.id is in approvalIds
                    .map((user, index) => {
                      // Find the corresponding approval object
                      const approval = approvalIds.find(
                        (approval) => approval.id === user.id
                      );
                      const isChecked = approval ? approval.levelStatus : false; // Check levelStatus
                      const isDisabled = approval
                        ? !approval.levelStatus
                        : false; // Disable if levelStatus is false

                      return (
                        <div className="form-check" key={index}>
                          <input
                            className="form-check-input"
                            type="checkbox" // Assuming you meant radio buttons
                            value={user.id}
                            id={`emailList-${index}`}
                            checked={isChecked}
                            disabled={isDisabled}
                          />
                          <label
                            className="form-check-label"
                            htmlFor={`emailList-${index}`}
                          >
                            {user.email}
                            {/* Adjust to show actual name if available */}
                          </label>
                        </div>
                      );
                    })}
                </div>

                {this.state.isRejected ? (
                  ""
                ) : this.state.isApproved ? (
                  ""
                ) : (
                  <div className="mt-4">
                    <label htmlFor="comments">Comments</label>
                    <textarea
                      className="form-control"
                      placeholder="Type Here"
                      rows="3"
                      id="comments"
                      value={this.state.comments}
                      onChange={this.handleCommentChange}
                    ></textarea>
                    {this.state.rejectError && (
                      <span style={{ color: "red" }}>Comments Required</span>
                    )}
                  </div>
                )}

                {this.state.isRejected ? (
                  ""
                ) : this.state.isApproved ? (
                  ""
                ) : (
                  <div className="mt-4">
                    <label htmlFor="fileUpload">Add File (Optional)</label>
                    <input
                      type="file"
                      className="form-control"
                      id="fileUpload"
                    />
                  </div>
                )}

                {this.state.isRejected ? (
                  ""
                ) : this.state.isApproved ? (
                  ""
                ) : (
                  <div className="row mt-4">
                    <div className="col-12 text-center">
                      <button
                        type="button"
                        className="btn btn-success mx-3"
                        onClick={this.handleApprove}
                        disabled={
                          this.state.isApproved || this.state.isRejected
                        }
                      >
                        APPROVE
                      </button>
                      <button
                        type="button"
                        className="btn btn-danger mx-3"
                        onClick={this.handleReject}
                        disabled={
                          this.state.isApproved || this.state.isRejected
                        }
                      >
                        REJECT
                      </button>
                      <br />
                      {/* <button
                      type="button"
                      className="btn btn-link mt-4"
                      style={{ color: "black", fontWeight: "bold" }}
                    >
                      Delegate
                    </button> */}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <div>
          <Modal
            show={this.state.isPreviewModalOpen}
            size="xl"
            onHide={this.closePreviewModal}
          >
            <Modal.Header className={classes.modalHeaderModern} closeButton>
              Previewing&nbsp;&nbsp;
              <strong>"{sessionStorage.getItem("fileName")}"</strong>
            </Modal.Header>
            <Modal.Body className={`${classes.previewModalBody}`}>
              {this.state.previewing && (
                <DocumentPreview
                  filePath={this.state.filePath}
                  fileName={sessionStorage.getItem("fileName")}
                  closePreview={this.closePreviewModal}
                />
              )}
            </Modal.Body>
            <Modal.Footer className={classes.modalFooterModern}>
              <Button
                onClick={this.closePreviewModal}
                className="btn btn-primary"
              >
                Close
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default EmailApproval;
