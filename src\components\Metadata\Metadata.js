import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { DataTable } from "../Table/DataTable";
import {
  addNew,
  deleteById,
  getList,
  editById,
} from "../../services/apiService";
import Notification from "../Notification/Notification";
import { error } from "pdf-lib";

class Metadata extends React.Component {
  state = {
    isOpen: false,
    isEditing: false,
    metaDataList: [],
    notification: {
      message: "",
      type: "",
      show: false,
    },
    newMetaData: {
      fieldName: "",
      fieldDesc: "",
      fieldType: "boolean",
      type: "metadata",
      multiple: false,
    },
    typeArray: [
      "boolean",
      "date",
      "datetime",
      "decimal",
      "email",
      "integer",
      "list",
      "longtext",
      "time",
      "url",
    ],
    showDeleteConfirm: false,
    metaIdToDelete: null,
    error:"",
    itemsPerPage: 10,
    currentPage: 0,
    searchTerm: "",
  };

  componentDidMount() {
    this.fetchMetaDataList();
  }

  fetchMetaDataList = async () => {
    const api = `/metadata/list?type=metadata`;
    try {
      const response = await getList(api);
      console.log(response);
      
      const transformedData = response.data.map(item => ({
        ...item,
        multipleText: item.multiple ? "Yes" : "No"
      })).sort((a, b) => a.id - b.id); 
      
      
      this.setState({
        metaDataList: transformedData,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleInputChange = (e) => {
    this.setState({error: ""});
    const { name, type, checked, value } = e.target;
    this.setState((prevState) => ({
      newMetaData: {
        ...prevState.newMetaData,
        [name]: type === "checkbox" ? checked : value, // Handle checkbox correctly
      },
    }));
  };

  createMetaData = async (newMetaData) => {
    console.log("New Meta Data Submitted:", newMetaData);
    const api = `/metadata`;
    try {
      const response = await addNew(api, newMetaData);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  editMetaData = async (metaId, updatedMetaData) => {
    const api = `/metadata/${metaId}`;
    console.log(api);
    console.log("Updated meta data submitted : ", updatedMetaData);
    try {
      const response = await editById(api, updatedMetaData);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();
    const { newMetaData, isEditing, metaDataList } = this.state;

    const isDuplicate = metaDataList.some(item => 
      item.fieldName.toLowerCase() === newMetaData.fieldName.toLowerCase().trim() &&
      (!isEditing || item.id !== newMetaData.id) 
    );

    if (isDuplicate) {
      this.setState({
          error: `${newMetaData.fieldName} already exists as metadata field!`,
      });
      return;
    }

    try {
      if (isEditing) {
        await this.editMetaData(newMetaData.id, newMetaData);
      } else {
        await this.createMetaData(newMetaData);  
      }
      this.fetchMetaDataList();
      this.closeModal();
    } catch (error) {
      console.log(error);
    }
    this.closeModal();
  };

  openDeleteConfirm = (metaId) => {
    this.setState({ showDeleteConfirm: true, metaIdToDelete: metaId });
  };

  closeDeleteConfirm = () => {
    this.setState({ showDeleteConfirm: false, metaIdToDelete: null });
  };

  confirmDelete = async () => {
    const id = this.state.metaIdToDelete;
    const api = `/metadata/${id}`;
    try {
      const response = await deleteById(api);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.fetchMetaDataList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    } finally {
      this.closeDeleteConfirm();
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openModal = (meta = null) => {
    if (meta) {
      this.setState({
        isOpen: true,
        isEditing: true,
        newMetaData: { ...meta },
      });
    } else {
      this.setState({
        isOpen: true,
        isEditing: false,
        newMetaData: {
          fieldName: "",
          fieldDesc: "",
          fieldType: "boolean",
          type: "metadata",
          multiple: false,
        },
      });
    }
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      isEditing: false,
      error:"",
      newMetaData: {
        fieldName: "",
        fieldDesc: "",
        fieldType: "boolean",
        type: "metadata",
        multiple: false,
      },
    });
  };

  fetchMetaDataById = async (id) => {
    // if (id) {
    //     const api=`/metadata/${id}`;
    //     try{
    //         const response=await findById(api);
    //         console.log(response.data);
    //         const data=response.data;
    //         setName(data.fieldName);
    //         setDescription(data.fieldDesc);
    //         setType(data.fieldType)
    //         setUseMultiple(data.multiple)
    //     }
    //     catch(error){
    //         //throw error;
    //     }
    // }
  };

  handleItemsPerPageChange = (newItemsPerPage) => {
    this.setState({ 
      itemsPerPage: newItemsPerPage,
      currentPage: 0 // Reset to first page when changing items per page
    });
  };

  handlePageChange = (page) => {
    this.setState({ currentPage: page });
  };

  handleSearch = (searchTerm) => {
    this.setState({ 
      searchTerm: searchTerm,
      currentPage: 0 // Reset to first page when searching
    });
  };


  render() {
    const { typeArray,metaDataList, currentPage, itemsPerPage, searchTerm } = this.state;
    let filteredData = metaDataList;
    if (searchTerm) {
      filteredData = metaDataList.filter(tag => 
        tag.fieldName && tag.fieldName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Calculate paginated data
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = filteredData.slice(startIndex, endIndex);
    
    return (
      <div className="container mt-3">
        <div className="row text-center">
          <div className="col-12">
            <h4>Meta Data</h4>
            <hr />
            {/* <Button variant="primary" onClick={()=>this.openModal()}>
              Add Metadata
            </Button>  */}
          </div>
        </div>
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        {/* <div className="row">
          <div className="col-12 text-center">
            <Button variant="primary" onClick={()=>this.openModal()}>
              Add Metadata
            </Button>
          </div>
        </div> */}

        <div className="row mt-2">
          <div className="col-12">
            <div className="col-12 d-flex align-items-center">
              <h4 className="mb-0">Manage Custom Metadata Fields</h4>
              <Button
                variant="primary"
                className="ms-3"
                onClick={() => this.openModal()}
              >
                Add Metadata
              </Button>
            </div>

            <div className="mt-4">
              <DataTable
                data={paginatedData}
                totalItems={filteredData.length}
                columns={[
                  {
                    key: "fieldName",
                    header: "Name",
                    sortable: true,
                    width: "20%"
                  },
                  {
                    key: "fieldDesc",
                    header: "Description",
                    sortable: true,
                    width: "30%"
                  },
                  {
                    key: "fieldType",
                    header: "Type",
                    sortable: true,
                    width: "15%"
                  },
                  {
                    key: "multiple",
                    header: "Multiple",
                    sortable: true,
                    width: "15%",
                    headerClassName: "text-center",
                    className: "text-center",
                    render: (value) => (
                      <div>
                        {value ? (
                          <Badge bg="success">Yes</Badge>
                        ) : (
                          <Badge bg="secondary">No</Badge>
                        )}
                      </div>
                    ),
                  },
                  {
                    key: "actions",
                    header: "Actions",
                    width: "20%",
                    render: (_, row) => (
                      <div className="text-center d-flex justify-content-start">
                        <button
                          title="edit user"
                          className="btn btn-primary"
                          onClick={() => this.openModal(row)}
                        >
                          <i className="fa fa-edit"></i>
                        </button>
                        <button
                          title="delete user"
                          className="btn btn-danger ms-2"
                          onClick={() => this.openDeleteConfirm(row.id)}
                        >
                          <i className="fa fa-trash"></i>
                        </button>
                      </div>
                    )
                  }
                ]}
                searchable={true}
                searchTerm={this.state.searchTerm}
                onSearch={this.handleSearch}
                itemsPerPage={this.state.itemsPerPage}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                currentPage={this.state.currentPage}
                onPageChange={this.handlePageChange}
                className="table-striped"
                showSno={true}
              />
            </div>
          </div>
        </div>

        {/* add modal */}
        <div>
          <Modal
            show={this.state.isOpen}
            onHide={this.closeModal}
            centered
            size="md"
          >
            <Modal.Header
              closeButton
              className="modal-header-modern"
            >
              <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                {this.state.isEditing
                  ? "Edit Metadata Field"
                  : "New Metadata Field"}
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <form onSubmit={this.handleSubmit}>
                <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <input
                        type="text"
                        id="name"
                        name="fieldName"
                        className="form-control"
                        value={this.state.newMetaData.fieldName}
                        required
                        placeholder="Field Name"
                        onChange={this.handleInputChange}
                      />
                    </div>
                  </div>
                  <span className="text-danger"> {this.state.error}</span>
                </div>

                <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <input
                        type="text"
                        id="description"
                        name="fieldDesc"
                        className="form-control"
                        value={this.state.newMetaData.fieldDesc}
                        required
                        placeholder="Field Description"
                        onChange={this.handleInputChange}
                      />
                    </div>
                  </div>
                </div>

                <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <select
                        id="type"
                        name="fieldType"
                        className="form-control"
                        value={this.state.newMetaData.fieldType}
                        onChange={this.handleInputChange}
                      >
                        {typeArray.map((type, index) => {
                          return (
                            <option key={index} value={type}>
                              {type}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  </div>
                </div>

                <div className="container my-3">
                  <div className="row">
                    <div className="col-8">
                      <div className="form-check">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          checked={this.state.newMetaData.multiple}
                          id="multiple"
                          name="multiple"
                          onChange={this.handleInputChange}
                        />
                        <label className="form-check-label" htmlFor="multiple">
                          Allow using multiple times for the same file
                        </label>
                      </div>
                    </div>

                    <div className="col-4">
                      <Button type="submit" variant="primary">
                        {this.state.isEditing ? "Update" : "Submit"}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </Modal.Body>
          </Modal>
        </div>
        {/* Delete Modal */}
        <div>
          <Modal
            show={this.state.showDeleteConfirm}
            onHide={this.closeDeleteConfirm}
            centered
          >
            <Modal.Header
              closeButton
              className="modal-header-modern"
            >
              <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                Confirm Deletion
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              Are you sure you want to delete this metadata?
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.closeDeleteConfirm}>
                Cancel
              </Button>
              <Button variant="danger" onClick={this.confirmDelete}>
                Delete
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>
    );
  }
}

export default Metadata;
