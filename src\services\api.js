import axios from 'axios';

const api = axios.create({
  //baseURL: "http://*************/dms-service", // Set your API base URL
  // baseURL: "http://localhost:8080/dms-service", // Set your API base URL
 baseURL: "http://***********:8001/dms-service", // Set your API base URL
  
});

// Request Interceptor - Add token to headers if it exists
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    const companyId = localStorage.getItem('companyId');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
     // config.headers.companyId = `${companyId}`;
    }
     if (companyId) {
       config.headers['X-Requested-With'] = companyId;
     }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Response Interceptor - Handle Unauthorized errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response && error.response.status === 401) {
      // Handle token expiration, redirect to login, or refresh token
      window.location.href = '/login'; // Redirect to login
    }
    return Promise.reject(error);
  }
);

export default api;
