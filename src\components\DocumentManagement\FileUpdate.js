import React, { Component } from "react";
import classes from "./FileUpdate.module.css";
import { findById, editById, getList } from "../../services/apiService";
import Loader from "../loader/Loader";
import Notification from "../Notification/Notification";
import { downloadeDocument } from "../../services/apiService";
import { Modal, Button } from "react-bootstrap";
import <PERSON>Viewer from "react-file-viewer";
import { Navigate } from "react-router-dom";
import DocumentPreview from "./DocumentPreview";
import Select from "react-select";

class FileUpdate extends Component {
  state = {
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    documentDetails: {
      documentName: "",
      linkPath: "",
      metaData: "",
      signedBy: "",
      createdDate: "",
      dueDate: "",
      documentNumber: "",
      remarks: "",
      fileType: "",
    },
    navigate: false,
    docuName: "",
    isPreviewModalOpen: false,
    previewing: false,
    // zoomLevel: 1,
    filePath: "",
    fileType: "",
    fileName: "",
    linkPath: "",
    metaDataList: [],
    tagList: [],
    selectedMetaData: [],
    selectedTag: [],
    metaDataLinkList: [],
    metaDataInputValues: {},
    signedUsers:"None",
    isViewer:false,
    usersGroup:[],
  };

  componentDidMount() {
    this.fetchMetaDataList();
    this.fetchTagList();
    this.fetchUserGroup();
    const hash = window.location.hash;
    const queryString = hash.split("?")[1];

    if (queryString) {
      const queryParams = new URLSearchParams(queryString);
      const fileId = queryParams.get("fileId");
      this.fetchSignedUsersByFileId(fileId);
      this.fetchFileDetails(fileId);
    } else {
      console.error("No query parameters found in the hash fragment");
    }
  }

  fetchSignedUsersByFileId = async (fileId) => {
    const api = `/approvalworkflow/doc1/eSign/${fileId}`;
    try {
      const response = await findById(api);
      const approvalLevels=response.data.approvalLevelDTO;

      const signedUsers = approvalLevels
      .filter(user => user.levelStatus === true)
      .map(user => user.name)
      .join(', ');
      console.log("Signed users:", signedUsers);
      this.setState({signedUsers:signedUsers || "None"});
    }
    catch(e) {}
  }

  fetchMetaDataList = async () => {
    const api = `/metadata/list?type=metadata`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({
        metaDataList: response.data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  fetchTagList = async () => {
    const api = `/metadata/list?type=tag`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({
        tagList: response.data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleMetaDataChange = (selectedOptions) => {
    console.log(selectedOptions);
    this.setState({
      selectedMetaData: selectedOptions || [],
    });
  };

  handleTagChange = (selectedOptions) => {
    console.log(selectedOptions);
    this.setState({
      selectedTag: selectedOptions,
    });
  };

  formatISODate = (isoDateString) => {
    const dateObject = new Date(isoDateString);
    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, "0");
    const day = String(dateObject.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

    fetchUserGroup = async () => {
      let api = `/team/team_dropdown_list`;
      try {
        const response = await getList(api);
        const data = response.data;
        this.setState({
          usersGroup: data,
        });
      } catch (error) {
        this.setState({
          notification: {
            message: "Something went Wrong",
            type: "error",
            show: true,
          },
        });
      }
    };

  fetchFileDetails = async (fileId) => {
    this.setState({ isLoading: true });
    let api = `/documentsattachmentdetail/${fileId}`;
    try {
      const data = await findById(api);
      const metaDataInputValues = {};

      if (data.data.metaDataLink) {
        data.data.metaDataLink.forEach((meta) => {
          if (meta.value) {
            metaDataInputValues[meta.key] = meta.value || '';
          }
        });
      }

      if(data.data.sharedUsers){
        const isShared=data.data.sharedUsers.find(x => x.id == localStorage.getItem("id"));
        console.log("=====",isShared);
        isShared && this.setState({isViewer:true})
      }

      if(data.data.sharedTeams){
        const userId = Number(localStorage.getItem("id"));
        const matchingSharedTeam = data.data.sharedTeams.find(sharedTeam => {
          const fullTeamDetails = this.state.usersGroup.find(team => team.id === sharedTeam.id);
  
            return fullTeamDetails && 
              (fullTeamDetails.teamLead === userId || 
                (fullTeamDetails.teamMemebers && fullTeamDetails.teamMemebers.includes(userId)));
        });

        if (matchingSharedTeam) {
          console.log("Matching shared team item:", matchingSharedTeam);
          matchingSharedTeam && this.setState({isViewer:true})
        }
      } 

      this.setState({
        documentDetails: data.data,
        linkPath: data.data.linkPath,
        docuName: data.data.documentName,
        isLoading: false,
        metaDataLinkList: data.data.metaDataLink || [],
        metaDataInputValues,
      });

      const metaDataList = data.data.metaDataLink || [];
      const selectedMetaData = metaDataList.map((meta) => ({
        label: meta.key,
        value: meta.id || meta.key, 
      }));

      this.setState({
        selectedMetaData: selectedMetaData,
      });

      const tagList = data.data.metaDataList || [];
      const selectedTag = tagList.map((meta) => ({
        label: meta.fieldName,
        value: meta.id,
      }));
      this.setState({
        selectedTag: selectedTag,
      });
    } catch (error) {
      console.log(error);
      this.setState({ isLoading: false });
    }
  };

  previewDocument = async (filePath, fileName) => {
    this.setState({
      isPreviewModalOpen: false,
      previewing: false,
      filePath: "",
      fileName: "",
    });

    try {
      const data = await downloadeDocument(filePath);
      const blob = new Blob([data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      this.setState({
        filePath: url,
        fileName: fileName,
        previewing: true,
        isPreviewModalOpen: true,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Could not preview the document",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleInputChange = (event) => {
    const { id, value } = event.target;
    this.setState((prevState) => ({
      documentDetails: {
        ...prevState.documentDetails,
        [id]: value,
      },
    }));
  };

  handleMetaDataInputChange = (event, key) => {
    const { value } = event.target;
    this.setState((prevState) => ({
      metaDataInputValues: {
        ...prevState.metaDataInputValues,
        [key]: value,
      },
    }));
  };

  handleSave = () => {
    const {
      selectedMetaData,
      documentDetails,
      selectedTag,
      metaDataInputValues,
      metaDataLinkList,
    } = this.state;
    
    // Create metaDataList from selected tags
    const metaDataList = selectedTag.map((meta) => ({ id: meta.value }));
    
    // Create metaDataLink array
    const metaDataLink = [];
    
    // First process the existing metadata link list
    metaDataLinkList.forEach((meta) => {
      // Only include if the metadata is still selected
      if (selectedMetaData.some(item => item.label === meta.key)) {
        const value = metaDataInputValues[meta.key] !== undefined 
          ? metaDataInputValues[meta.key] 
          : meta.value;
        metaDataLink.push({ key: meta.key, value: value });
      }
    });
  
    // Then add newly selected metadata that aren't already in the list
    selectedMetaData.forEach((tag) => {
      const exists = metaDataLink.some(item => item.key === tag.label);
      if (!exists) {
        metaDataLink.push({ key: tag.label, value: '' }); // Initialize new metadata with empty value
      }
    });
  
    if (!documentDetails.linkPath) {
      const fullDocName = documentDetails.documentName.includes(
        documentDetails.fileType
      )
        ? documentDetails.documentName
        : `${documentDetails.documentName}${documentDetails.fileType}`;
  
      const updatedDocumentDetails = {
        ...documentDetails,
        documentName: fullDocName,
        metaDataList,
        metaDataLink,
      };
      console.log("updated file details : ",updatedDocumentDetails);
      this.updateDocument(
        documentDetails.documentsAttachmentId,
        updatedDocumentDetails
      );
    } else {
      const updatedDocumentDetails = {
        ...documentDetails,
        metaDataList,
        metaDataLink,
      };
      console.log("updated file details : ",updatedDocumentDetails);
      this.updateDocument(
        documentDetails.documentsAttachmentId,
        updatedDocumentDetails
      );
    }
  };

  updateDocument = async (
    documentsAttachmentId,
    DocumentsAttachmentDetailDTO
  ) => {
    const api = `/documentsattachmentdetail/${documentsAttachmentId}`;
    console.log(DocumentsAttachmentDetailDTO);
    try {
      const response = await editById(api, DocumentsAttachmentDetailDTO);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  // zoomOut = () => {
  //   this.setState((prevState) => ({
  //     zoomLevel: Math.max(prevState.zoomLevel - 0.1, 0.1),
  //   }));
  // };

  handleCancel = () => {
    this.setState({
      navigate: true,
    });
    // const hash = window.location.hash;
    // const queryString = hash.split("?")[1];
    // let fileId = null;
    // if (queryString) {
    //   const queryParams = new URLSearchParams(queryString);
    //   fileId = queryParams.get("fileId");
    // }
    // if (this.props.onClose && fileId) {
    //   this.props.onClose({
    //     documentsAttachmentId: fileId,
    //     documentName: this.state.documentDetails.documentName,
    //     filePath: this.state.documentDetails.filePath
    //   });
    // }
  };

  // zoomIn = () => {
  //   this.setState((prevState) => ({
  //     zoomLevel: prevState.zoomLevel + 0.1,
  //   }));
  // };

  closePreviewModal = () => {
    this.setState({
      isPreviewModalOpen: false,
      previewing: false,
      filePath: "",
      fileName: "",
    });
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  downloadeDocumentAttachment = async (filePath, fileName) => {
    try {
      const data = await downloadeDocument(filePath);
      let blob = new Blob([data], { type: "application/octet-stream" });
      let url = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = fileName;
      anchor.target = "_blank";
      anchor.click();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  render() {
    const { documentDetails, previewing, filePath, fileType } = this.state;
    const { metaDataList, selectedMetaData, tagList, selectedTag } = this.state;
    const options = metaDataList.map((item) => ({
      label: item.fieldName,
      value: item.id,
    }));

    if (this.state.navigate) {
      return <Navigate to="/newDS/document-management" />;
    }

    const tagoptions = tagList.map((item) => ({
      label: item.fieldName,
      value: item.id,
    }));

    const formattedCreatedDate = documentDetails.createdDate
      ? this.formatISODate(documentDetails.createdDate)
      : "";

    const formattedDueDate = documentDetails.dueDate
      ? this.formatISODate(documentDetails.dueDate)
      : "";
    return (
      <div className="container">
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row">
          <div className="col-12">
            <div
              style={{
                display: "flex",
                justifyContent: "start",
                alignItems: "center",
                flexDirection: "row",
                gap: "12px",
                marginBottom: "12px",
                padding: "8px 0 6px 0",
                borderBottom: "1px solid #e0e0e0",
              }}
            >
              <div
                className={classes.fileView}
                style={{ fontSize: "14px", minWidth: 120 }}
                onClick={(e) => {
                  e.preventDefault();
                  if (this.state.linkPath) {
                    let url = this.state.linkPath;
                    if (!url.match(/^https?:\/\//i)) {
                      url = "http://" + url;
                    }
                    window.open(url, "_blank");
                  } else {
                    this.previewDocument(
                      documentDetails.filePath,
                      documentDetails.fileName
                    );
                  }
                }}
              >
                Preview
              </div>
              <div>
                <a
                  style={{
                    color: "#357ae8",
                    textDecoration: "underline",
                    cursor: "pointer",
                    fontWeight: 600,
                    fontSize: "1.08rem",
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    if (this.state.linkPath) {
                      let url = this.state.linkPath;
                      if (!url.match(/^https?:\/\//i)) {
                        url = "http://" + url;
                      }
                      window.open(url, "_blank");
                    } else {
                      this.downloadeDocumentAttachment(
                        documentDetails.filePath,
                        documentDetails.documentName
                      );
                    }
                  }}
                >
                  {this.state.docuName}
                </a>
              </div>
            </div>
          </div>
        </div>
        <div className="row mt-2">
          <div className="col-12 pt-1">
            <label htmlFor="name">Name</label>
          </div>
          <div className="col-sm-6 d-flex align-items-center">
            <input
              type="text"
              id="documentName"
              value={documentDetails.documentName.replace(/\.[^/.]+$/, "")}
              onChange={this.handleInputChange}
              className={`form-control ${classes.inputModern}`}
              style={{
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
                borderRight: "none",
              }}
              title={this.state.isViewer ? "You cannot edit" : "File Name"}
              readOnly={this.state.isViewer}
            />
            <input
              type="text"
              value={documentDetails.fileType}
              className="form-control"
              disabled
              style={{
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                backgroundColor: "#f8f9fa",
                cursor: "not-allowed",
                color: "#6c757d",
                width: "80px",
                borderLeft: "none",
              }}
            />
          </div>
        </div>
        {this.state.linkPath && (
          <div className="row mt-2">
            <div className="col-sm-12 pt-1">
              <label htmlFor="name">Link URL</label>
            </div>
            <div className="col-sm-6">
              <input
                type="text"
                id="linkPath"
                value={documentDetails.linkPath}
                onChange={this.handleInputChange}
                className={`form-control ${classes.inputModern}`}
              />
            </div>
          </div>
        )}
        <div className="row mt-2">
          <div className="col-sm-6">
            <label htmlFor="metaData">Meta Data</label>
            <Select
              isMulti
              id="metaData"
              options={options}
              value={selectedMetaData}
              onChange={this.handleMetaDataChange}
              className={classes.selectModern}
              classNamePrefix="select"
            />
          </div>
          <div className="col-sm-6">
            <label htmlFor="tag">Tag</label>
            <Select
              isMulti
              id="tag"
              options={tagoptions}
              value={selectedTag}
              onChange={this.handleTagChange}
              className={classes.selectModern}
              classNamePrefix="select"
            />
          </div>
        </div>
        <div className="row mt-2">
          <div className="col-sm-6">
            <label htmlFor="signedby">Signed by</label>
            <input
              type="text"
              id="signedBy"
              value={this.state.signedUsers}
              readOnly
              className={`form-control ${classes.inputModern}`}
            />
          </div>
          <div className="col-sm-3">
            <label htmlFor="date">Date</label>
            <input
              id="createdDate"
              type="date"
              readOnly
              value={formattedCreatedDate}
              onChange={this.handleInputChange}
              className={`form-control ${classes.inputModern}`}
            />
          </div>
          <div className="col-sm-3">
            <label htmlFor="duedate">Due Date</label>
            <input
              id="dueDate"
              type="date"
              value={formattedDueDate}
              onChange={this.handleInputChange}
              className={`form-control ${classes.inputModern}`}
              min={new Date().toISOString().split("T")[0]}
            />
          </div>
        </div>
        <div className="row mt-2">
          <div className="col-sm-6">
            <label htmlFor="remarks">Notes</label>
            <textarea
              id="remarks"
              value={documentDetails.remarks}
              onChange={this.handleInputChange}
              className={`form-control ${classes.textareaModern}`}
            />
          </div>{" "}
          <div className="col-sm-3">
            <label htmlFor="ocr">OCR Language</label>
            <input
              id="ocr"
              type="text"
              value="English"
              className={`form-control ${classes.inputModern}`}
              readOnly
            />
          </div>
          <div className="col-sm-3">
            <label htmlFor="dno">Document Number</label>
            <input
              id="documentNumber"
              type="text"
              value={documentDetails.documentNumber}
              onChange={this.handleInputChange}
              className={`form-control ${classes.inputModern}`}
            />
          </div>
        </div>
        <div className={classes.sectionTitle}>Meta Data Values</div>
        <div className={classes.divider}></div>
        <div className="row mt-2">
          <div className="col-sm-12">
            <div className={classes.metaDataContainer}>
              <div className="row">
                {this.state.metaDataLinkList &&
                  this.state.metaDataLinkList.map((meta, index) => (
                    <div className="col-md-4 mb-2" key={index}>
                      <div className={classes.metaDataCard}>
                        <label
                          className={classes.metaDataInputLabel}
                          htmlFor={`meta-${index}`}
                        >
                          {meta.key}
                        </label>
                        <input
                          id={`meta-${index}`}
                          type="text"
                          className={`form-control ${classes.metaDataInputField}`}
                          value={ this.state.metaDataInputValues[meta.key] ?? '' }
                          onChange={(e) =>
                            this.handleMetaDataInputChange(e, meta.key)
                          }
                        />
                      </div>
                    </div>
                  ))}
              </div>
              {this.state.metaDataLinkList &&
                this.state.metaDataLinkList.length === 0 && (
                  <div className={classes.noMetaDataMessage}>
                    No metadata values available for this document.
                  </div>
                )}
            </div>
          </div>
        </div>
        <div className="row mt-3 mb-3">
          <div className="col-sm-12 text-center">
            <button
              type="button"
              className="btn btn-primary"
              onClick={this.handleSave}
            >
              Save
            </button>
            <button
              type="button"
              className="btn btn-danger ms-3"
              onClick={this.handleCancel}
            >
              Close
            </button>
          </div>
        </div>
        <div>
          <Modal
            show={this.state.isPreviewModalOpen}
            size="xl"
            onHide={this.closePreviewModal}
          >
            <Modal.Header className={classes.modalHeaderModern} closeButton>
              Previewing&nbsp;&nbsp;
              <strong>"{documentDetails.documentName}"</strong>
            </Modal.Header>
            <Modal.Body className={`${classes.previewModalBody}`}>
              {previewing && (
                <DocumentPreview
                  filePath={this.state.filePath}
                  fileName={this.state.fileName}
                  closePreview={this.closePreviewModal}
                />
              )}
            </Modal.Body>
            <Modal.Footer className={classes.modalFooterModern}>
              <Button
                onClick={this.closePreviewModal}
                className="btn btn-primary"
              >
                Close
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default FileUpdate;
