.parent {
  background-image: url("../images/1.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
}

.formStyle {
  margin-top: 20px;
}

.formLabel {
  font-family: consolas;
  font-size: 20px;
}

.formFields {
  padding: 20px;
}

.formControl {
  border: none !important;
  outline: none !important;
  padding: 10px;
  width: 100%;
  box-shadow: none !important;
  background-color: rgba(255, 255, 255, 0.75);
}

input[type="password"] {
  width: 369px;
}

input[type="email"] {
  width: 369px;
}

.underLine {
  margin-top: 0;
  margin-bottom: 0;
  width: 100%;
  color: green;
}
.formControl:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.input-group .btn {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.input-group .btn:focus,
.input-group .btn:active {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.loginBox {
  background-color: rgba(255, 255, 255, 0.75);
  width: 300px;
  height: 350px;
  position: absolute;
  top: 120px;
  right: 40px;
  text-align: center;
  border-radius: 5px;
}

.header {
  width: 85%;
  margin: 20px 10px;
}

.bg-gradient-custom {
  background: linear-gradient(224.72deg, #081a51 65.18%, #123bb7 99.6%);
  color: white;
  height: 100vh;
  width: 100%;
}

.left-side {
  height: 100vh;
  display: flex;
  justify-content: center;
}

.logo-container {
  position: absolute;
  top: 30px;
  left: 40px;
}

.heading {
  font-weight: 600;
  font-size: 30px;
  line-height: 24px;
  letter-spacing: 0%;
  text-transform: capitalize;
}

.sub-heading {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0%;
}

.login-button {
  width: 373px;
  height: 44px;
  top: 495px;
  left: 110px;
  border-radius: 50px;
}

.right-heading {
  font-weight: 400;
  font-size: 30px;
  line-height: 24px;
  letter-spacing: 0%;
  text-align: center;
  text-transform: capitalize;
  color: #407BFF;
}

.input-email {
    width: 369px !important;
}
