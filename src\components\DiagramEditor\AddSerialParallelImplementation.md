# Implementation Guide: Add Serial and Add Parallel Buttons

## Overview
This guide provides step-by-step instructions to implement "Add Serial" and "Add Parallel" buttons that replicate the existing serial and parallel node functionality in the DiagramEditor.

## Implementation Strategy

We'll implement **Option 1: Smart Node Addition** which creates a new node and automatically connects it to the selected node using the appropriate connection type.

## Step 1: Update Toolbar Component

### Add New Button Definitions
Add these button configurations to the Toolbar component:

```javascript
// In Toolbar.js, add these to the nodeTypes array or create a separate array
const connectionTypes = [
  {
    type: 'add-serial',
    label: 'Add Serial',
    icon: '→',
    color: '#4caf50',
    tooltip: 'Add a serial (sequential) node connected to selected node'
  },
  {
    type: 'add-parallel', 
    label: 'Add Parallel',
    icon: '∥',
    color: '#ff9800',
    tooltip: 'Add a parallel (concurrent) node connected to selected node'
  }
];
```

### Update Toolbar Props
```javascript
const Toolbar = ({ 
  onAddNode, 
  onAddSerialNode,     // New prop
  onAddParallelNode,   // New prop
  selectedNode,        // New prop to know which node is selected
  embedded = false, 
  // ... other existing props
}) => {
```

### Add Button Rendering
```javascript
// Add this section after the existing node types
{!isMinimized && (
  <>
    <hr style={{ margin: embedded ? '12px 0' : '6px 0', border: 'none', borderTop: '1px solid #eee' }} />
    
    <h4 style={{ margin: '0 0 12px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
      Smart Connect
    </h4>
    
    {/* Add Serial Button */}
    <button
      style={{
        ...buttonStyle,
        backgroundColor: selectedNode ? '#4caf50' : '#e9ecef',
        color: selectedNode ? 'white' : '#6c757d',
        border: `1px solid ${selectedNode ? '#4caf50' : '#dee2e6'}`,
        cursor: selectedNode ? 'pointer' : 'not-allowed',
        width: "100px"
      }}
      onClick={selectedNode ? onAddSerialNode : undefined}
      onMouseOver={(e) => {
        if (selectedNode) {
          e.target.style.backgroundColor = '#388e3c';
        }
      }}
      onMouseOut={(e) => {
        if (selectedNode) {
          e.target.style.backgroundColor = '#4caf50';
        }
      }}
      title={selectedNode ? 'Add serial node connected to selected node' : 'Select a node first'}
    >
      <div style={{ ...iconStyle, backgroundColor: selectedNode ? 'transparent' : '#6c757d' }}>
        →
      </div>
      Add Serial
    </button>

    {/* Add Parallel Button */}
    <button
      style={{
        ...buttonStyle,
        backgroundColor: selectedNode ? '#ff9800' : '#e9ecef',
        color: selectedNode ? 'white' : '#6c757d',
        border: `1px solid ${selectedNode ? '#ff9800' : '#dee2e6'}`,
        cursor: selectedNode ? 'pointer' : 'not-allowed',
        width: "100px"
      }}
      onClick={selectedNode ? onAddParallelNode : undefined}
      onMouseOver={(e) => {
        if (selectedNode) {
          e.target.style.backgroundColor = '#f57c00';
        }
      }}
      onMouseOut={(e) => {
        if (selectedNode) {
          e.target.style.backgroundColor = '#ff9800';
        }
      }}
      title={selectedNode ? 'Add parallel node connected to selected node' : 'Select a node first'}
    >
      <div style={{ ...iconStyle, backgroundColor: selectedNode ? 'transparent' : '#6c757d' }}>
        ∥
      </div>
      Add Parallel
    </button>
  </>
)}
```

## Step 2: Update DiagramEditor Component

### Add Handler Functions
Add these functions to DiagramEditor.js:

```javascript
// Add Serial Node Handler
const handleAddSerialNode = useCallback(() => {
  if (!selectedNode) {
    showAlert('Please select a node first', 'warning');
    return;
  }
  
  if (locked) {
    showAlert('Cannot add nodes when diagram is locked', 'warning');
    return;
  }

  // Calculate position for new serial node (below the selected node)
  const newPosition = {
    x: selectedNode.position.x,
    y: selectedNode.position.y + 150 // Position below
  };

  // Create new node
  const newNode = createDiagramNode({
    type: 'process',
    position: newPosition,
    label: 'Serial Process Node',
    assignedUsers: [],
    levelStatus: false,
  });

  // Convert to React Flow format
  const reactFlowNode = {
    id: newNode.id,
    type: 'process',
    position: newNode.position,
    data: {
      ...newNode.data,
      onUpdateNode: handleUpdateNode
    },
  };

  // Create edge connecting selected node to new node (serial connection)
  const newEdge = createDiagramEdge({
    id: `edge_${selectedNode.id}_${newNode.id}`,
    source: selectedNode.id,
    target: newNode.id,
    sourceHandle: 'output-bottom', // Serial connection uses bottom output
    targetHandle: 'input-top',     // Serial connection uses top input
    label: 'Serial (→)',
    data: {
      connectionType: 'series',
      workflowRule: 'Serial execution - connects to bottom output'
    },
    style: {
      stroke: '#4caf50',
      strokeWidth: 2,
      strokeDasharray: 'none'
    }
  });

  // Update state
  setNodes((nds) => {
    const updatedNodes = nds.concat(reactFlowNode);
    return updateNodeConnectionTypes(updatedNodes, [...edges, newEdge]);
  });
  
  setEdges((eds) => [...eds, newEdge]);
  setIsWorkflowModified(true);
  
  showAlert('Serial node added successfully', 'success');
}, [selectedNode, setNodes, setEdges, edges, locked, handleUpdateNode]);

// Add Parallel Node Handler  
const handleAddParallelNode = useCallback(() => {
  if (!selectedNode) {
    showAlert('Please select a node first', 'warning');
    return;
  }
  
  if (locked) {
    showAlert('Cannot add nodes when diagram is locked', 'warning');
    return;
  }

  // Calculate position for new parallel node (to the right of selected node)
  const existingParallelNodes = edges.filter(edge => 
    edge.source === selectedNode.id && edge.sourceHandle === 'output-right'
  ).length;
  
  const newPosition = {
    x: selectedNode.position.x + 300, // Position to the right
    y: selectedNode.position.y + (existingParallelNodes * 120) // Offset if multiple parallel nodes
  };

  // Create new node
  const newNode = createDiagramNode({
    type: 'process',
    position: newPosition,
    label: 'Parallel Process Node',
    assignedUsers: [],
    levelStatus: false,
  });

  // Convert to React Flow format
  const reactFlowNode = {
    id: newNode.id,
    type: 'process',
    position: newNode.position,
    data: {
      ...newNode.data,
      onUpdateNode: handleUpdateNode
    },
  };

  // Create edge connecting selected node to new node (parallel connection)
  const newEdge = createDiagramEdge({
    id: `edge_${selectedNode.id}_${newNode.id}`,
    source: selectedNode.id,
    target: newNode.id,
    sourceHandle: 'output-right', // Parallel connection uses right output
    targetHandle: 'input-left',   // Parallel connection uses left input
    label: 'Parallel (∥)',
    data: {
      connectionType: 'parallel',
      workflowRule: 'Parallel execution - connects to right output'
    },
    style: {
      stroke: '#ff9800',
      strokeWidth: 2,
      strokeDasharray: '5,5'
    }
  });

  // Update state with auto-layout for parallel nodes
  setEdges((eds) => {
    const updatedEdges = [...eds, newEdge];
    
    setNodes((nds) => {
      const updatedNodes = nds.concat(reactFlowNode);
      const nodesWithConnectionTypes = updateNodeConnectionTypes(updatedNodes, updatedEdges);
      
      // Apply auto-layout for parallel nodes
      return autoLayoutParallelNodes(nodesWithConnectionTypes, updatedEdges);
    });
    
    return updatedEdges;
  });
  
  setIsWorkflowModified(true);
  
  showAlert('Parallel node added successfully', 'success');
}, [selectedNode, setNodes, setEdges, edges, locked, handleUpdateNode, autoLayoutParallelNodes]);
```

### Update Toolbar Usage
Update the Toolbar component usage in DiagramEditor:

```javascript
<Toolbar 
  onAddNode={handleAddNode}
  onAddSerialNode={handleAddSerialNode}     // New prop
  onAddParallelNode={handleAddParallelNode} // New prop
  selectedNode={selectedNode}               // New prop
  embedded={embedded}
  usersLoading={usersLoading}
  onCopyNode={handleCopyNode}
  onPasteNode={handlePasteNode}
  canCopy={!!(selectedNode || selectedNodes.length > 0)}
  canPaste={!!(copiedNode || copiedNodes.length > 0)}
  selectedNodeCount={selectedNodes.length > 0 ? selectedNodes.length : (selectedNode ? 1 : 0)}
  copiedNodeCount={copiedNodes.length > 0 ? copiedNodes.length : (copiedNode ? 1 : 0)}
  onSave={embedded && onSave ? onSave : null}
  onCancel={embedded ? handleCancel : (onCancel || null)}
  isEditMode={!!workflowId}
  canSave={hasProcessNode && !locked}
  locked={locked}
/>
```

## Step 3: Enhanced Features (Optional)

### Multiple Node Addition
For adding multiple nodes at once:

```javascript
const handleAddSerialChain = useCallback((count = 3) => {
  if (!selectedNode) {
    showAlert('Please select a node first', 'warning');
    return;
  }
  
  let currentSourceNode = selectedNode;
  const newNodes = [];
  const newEdges = [];
  
  for (let i = 0; i < count; i++) {
    const newPosition = {
      x: currentSourceNode.position.x,
      y: currentSourceNode.position.y + (150 * (i + 1))
    };
    
    const newNode = createDiagramNode({
      type: 'process',
      position: newPosition,
      label: `Serial Node ${i + 1}`,
      assignedUsers: [],
      levelStatus: false,
    });
    
    const reactFlowNode = {
      id: newNode.id,
      type: 'process',
      position: newNode.position,
      data: {
        ...newNode.data,
        onUpdateNode: handleUpdateNode
      },
    };
    
    const newEdge = createDiagramEdge({
      id: `edge_${currentSourceNode.id}_${newNode.id}`,
      source: currentSourceNode.id,
      target: newNode.id,
      sourceHandle: 'output-bottom',
      targetHandle: 'input-top',
      label: 'Serial (→)',
      data: {
        connectionType: 'series',
        workflowRule: 'Serial execution - connects to bottom output'
      },
      style: {
        stroke: '#4caf50',
        strokeWidth: 2,
        strokeDasharray: 'none'
      }
    });
    
    newNodes.push(reactFlowNode);
    newEdges.push(newEdge);
    currentSourceNode = reactFlowNode; // Next node becomes source for chain
  }
  
  // Update state
  setNodes((nds) => {
    const updatedNodes = nds.concat(newNodes);
    return updateNodeConnectionTypes(updatedNodes, [...edges, ...newEdges]);
  });
  
  setEdges((eds) => [...eds, ...newEdges]);
  setIsWorkflowModified(true);
  
  showAlert(`${count} serial nodes added successfully`, 'success');
}, [selectedNode, setNodes, setEdges, edges, handleUpdateNode]);
```

### Keyboard Shortcuts
Add keyboard shortcuts for quick access:

```javascript
// Add to handleKeyPress function in DiagramEditor
if (event.ctrlKey && event.key === 's' && selectedNode && !isTypingInInput) {
  event.preventDefault();
  handleAddSerialNode();
}
if (event.ctrlKey && event.key === 'p' && selectedNode && !isTypingInInput) {
  event.preventDefault();
  handleAddParallelNode();
}
```

## Step 4: Visual Feedback

### Connection Preview
Add visual feedback when hovering over buttons:

```javascript
const [connectionPreview, setConnectionPreview] = useState(null);

// Show preview on button hover
const showConnectionPreview = (type) => {
  if (selectedNode) {
    setConnectionPreview({
      type: type,
      sourceNode: selectedNode,
      position: type === 'serial' ? 
        { x: selectedNode.position.x, y: selectedNode.position.y + 150 } :
        { x: selectedNode.position.x + 300, y: selectedNode.position.y }
    });
  }
};

// Hide preview
const hideConnectionPreview = () => {
  setConnectionPreview(null);
};
```

## Step 5: Testing Scenarios

### Test Cases
1. **Basic Serial Addition**: Select a node, click "Add Serial", verify new node is positioned below and connected via bottom output
2. **Basic Parallel Addition**: Select a node, click "Add Parallel", verify new node is positioned to the right and connected via right output
3. **Multiple Parallel Nodes**: Add multiple parallel nodes to same source, verify they are vertically spaced
4. **Mixed Connections**: Add both serial and parallel nodes to same source, verify correct positioning and connection types
5. **Auto-Layout**: Verify parallel nodes are automatically positioned correctly
6. **Workflow Conversion**: Save and reload workflow, verify connections are preserved
7. **Level Assignment**: Verify new nodes get correct level, rootLevel, and rootLevelType properties
8. **Locked Mode**: Verify buttons are disabled when diagram is locked
9. **No Selection**: Verify buttons are disabled when no node is selected
10. **Connection Type Analysis**: Verify connection types are correctly analyzed and displayed

## Benefits of This Implementation

1. **Consistency**: Uses existing connection logic and styling
2. **Smart Positioning**: Automatically positions nodes based on connection type
3. **Auto-Layout**: Leverages existing parallel node layout system
4. **Level Management**: Properly assigns level hierarchy
5. **Visual Feedback**: Clear button states and tooltips
6. **Workflow Integration**: Seamlessly integrates with save/load functionality
7. **User Experience**: Reduces manual connection work
8. **Extensible**: Easy to add more smart connection types

This implementation replicates and enhances the existing serial/parallel functionality while providing a more user-friendly interface for creating workflow diagrams.
