import React, { Component } from "react";
import classes from "./RecyleBin.module.css";
import Notification from "../Notification/Notification";
import { getList, formatDate, getDisplayPath } from "../../services/apiService";
import { Modal, Button } from "react-bootstrap";
import { GlobalConstants } from "../../constants/global-constants";
import axios from "../../services/api";
import Loader from "../loader/Loader";
import { DataTable } from "../Table/DataTable";
import ReactPaginate from "react-paginate";

class RecyleBin extends Component {
  state = {
    uploadedFiles: [],
    notification: {
      message: "",
      type: "",
      show: false,
    },
    filePath:null,
    isEmptyBinModalOpen: false,
    isRestoreAllModalOpen: false,
    isRestoreFileModalOpen: false,
    isDeleteFileModalOpen: false,
    fileIdToRestore: null,
    fileNameToRestore: "",
    fileIdToDelete: null,
    fileNameToDelete: "",
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 25,
    searchParam: "",
    isLoading: false,
  };
  componentDidMount() {
    this.fetchDocumentList();
  }
  fetchDocumentList = async (
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    this.setState({ isLoading: true });
    const api =
      "/documentsattachmentdetail/list_by_employees/" +
      localStorage.getItem("id") +
      `?page=${page}&size=${itemsPerPage}&searchParam=${this.state.searchParam}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        uploadedFiles: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchDocumentList(page);
  };

  handleSearchInputChange = (event) => {
    const searchParam = event.target.value;
    this.setState({ searchParam }, () => {
      console.log(searchParam);
      this.fetchDocumentList();
    });
  };

  handleItemsPerPageChange = (size) => {
    this.setState({ itemsPerPage: size }, () => {
      this.fetchDocumentList(0, size);
    });
  };


    fetchActualDocumentList = async () => {
      const userId = localStorage.getItem("id");
      const api =
        "/documentsattachmentdetail/list_by_employee/" +
        userId +
        `?page=0&size=500&searchParam=`;
      try {
        const response = await getList(api);
        const data = response.data;        
        return data.content;
      } catch (error) { }
    };

  restoreFile = async (id) => {
    this.setState({ isLoading: true });
    const { filePath } = this.state;
    const list=await this.fetchActualDocumentList();
    const matchedFilePath=list.find((item)=>item.filePath === filePath);
    console.log("matched file path : ",matchedFilePath);
    if(matchedFilePath){
      this.setState({
        notification: {
          message: `File with same name already exists in ${getDisplayPath(filePath)}`,
          type: "error",
          show: true,
        },
        isLoading:false,
      });
      return;
    }
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/restore/${id}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Restored Successfully",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
      await this.fetchDocumentList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }
    this.closeRestoreFileModal();
  };

  deleteFile = async (id) => {
    this.setState({ isLoading: true });
    console.log("File id : ", id);
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/perminentdelete/${id}`;
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Permenently Deleted Successfully",
          type: "success",
          show: true,
        },
        isLoading:false,
      });
      await this.fetchDocumentList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading:false,
      });
    }
    this.closeDeleteFileModal();
  };

  openEmptyBinModal = () => {
    this.setState({ isEmptyBinModalOpen: true });
  };

  openRestoreModal = () => {
    this.setState({ isRestoreAllModalOpen: true });
  };

  closeRestoreAllModal = () => {
    this.setState({ isRestoreAllModalOpen: false });
  };

  closeEmptyBinModal = () => {
    this.setState({ isEmptyBinModalOpen: false });
  };

  openRestoreFileModal = (file) => {
    this.setState({
      isRestoreFileModalOpen: true,
      fileIdToRestore: file.documentsAttachmentId,
      filePath:file.filePath,
      fileNameToRestore: file.documentName,
    });
  };
  closeRestoreFileModal = () => {
    this.fetchDocumentList();
    this.setState({
      isRestoreFileModalOpen: false,
      fileIdToRestore: null,
      filePath:null,
      fileNameToRestore: "",
    });
  };

  openDeleteFileModal = (file) => {
    this.setState({
      isDeleteFileModalOpen: true,
      fileIdToDelete: file.documentsAttachmentId,
      fileNameToDelete: file.documentName,
    });
  };
  closeDeleteFileModal = () => {
    this.fetchDocumentList();
    this.setState({
      isDeleteFileModalOpen: false,
      fileIdToDelete: null,
      fileNameToDelete: "",
    });
  };

  restoreAll = async () => {
    // let type="restoreall"
    // let api = "/documentsattachmentdetail/list_by_employees/" + 1+`?type=${type}`;
    // try {
    //   const data = await getList(api);
    //   this.fetchDocumentList();
    // } catch (error) {
    //   this.setState({
    //     notification: {
    //       message: "Something went Wrong",
    //       type: "error",
    //       show: true,
    //     },
    //   });
    // }
    this.closeRestoreAllModal();
  };

  emptyBin = async () => {
    // let type="deleteall"
    // let api = "/documentsattachmentdetail/list_by_employees/" + 1+`?type=${type}`;
    // try {
    //   const data = await getList(api);
    //   this.fetchDocumentList();
    // } catch (error) {
    //   this.setState({
    //     notification: {
    //       message: "Something went Wrong",
    //       type: "error",
    //       show: true,
    //     },
    //   });
    // }
    this.closeEmptyBinModal();
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  render() {
    const { itemsPerPage, currentPage, totalItems, uploadedFiles } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    // Define columns for DataTable
    const columns = [
      {
        key: "index",
        header: "S No.",
        width: "80px",
        render: (_, row, index) => currentPage * itemsPerPage + index + 1,
      },
      {
        key: "documentName",
        header: "Name",
        sortable: true,
        render: (value, row) => (
          <button
            style={{
              color: "blue",
              textDecoration: "underline",
              cursor: "pointer",
              background: "none",
              border: "none",
            }}
          >
            {value}
          </button>
        ),
      },
      {
        key: "filePath",
        header: "Location",
        sortable: true,
        render: (value) => value && getDisplayPath(value),
      },
      {
        key: "modifiedDate",
        header: "Deleted At",
        sortable: true,
        render: (value) => formatDate(value),
      },
      {
        key: "actions",
        header: "Actions",
        width: "120px",
        render: (_, row) => (
          <>
            <i
              className="fa fa-refresh"
              style={{
                color: "blue",
                fontSize: "18px",
                cursor: "pointer",
              }}
              aria-hidden="true"
              title="Restore"
              onClick={() => this.openRestoreFileModal(row)}
            ></i>
            <i
              className="fa fa-trash"
              style={{
                color: "red",
                fontSize: "18px",
                marginLeft: "20px",
                cursor: "pointer",
              }}
              aria-hidden="true"
              title="Delete"
              onClick={() => this.openDeleteFileModal(row)}
            ></i>
          </>
        ),
      },
    ];

    return (
      <div className="container mt-3">
        <div className="row">
          <div className="col-sm-4 col-12">
            <h4>Recycle Bin</h4>
          </div>
          <div className="col-sm-3 offset-sm-5 col-12">
            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}
            {/* <div className="text-center h6" onClick={this.openEmptyBinModal}>
                <i className="fa fa-trash" aria-hidden="true" ></i>&nbsp;&nbsp;Empty Bin
              </div>
              <div className="text-center h6" onClick={this.openRestoreModal} title="restore all">
                <i className="fa fa-refresh" aria-hidden="true"></i>&nbsp;&nbsp;Restore All
              </div> */}
            <input
              title="search"
              type="search"
              value={this.state.searchParam}
              name="searchParam"
              className="form-control ms-2"
              placeholder="Search recyclebin"
              style={{ height: "40px" }}
              onChange={this.handleSearchInputChange}
            />
          </div>
        </div>
        <div className="row mt-3">
          <div className="col-12">
            <DataTable
              data={uploadedFiles}
              columns={columns}
              searchable={false} // We're using our own search input
              className="table-striped"
              itemsPerPage={itemsPerPage}
              totalItems={totalItems}
              currentPage={currentPage}
              onPageChange={this.handlePageChange}
              onItemsPerPageChange={this.handleItemsPerPageChange}
            />
          </div>
        </div>
        {/* Restore All Modal */}
        <div>
          <Modal
            show={this.state.isRestoreAllModalOpen}
            onHide={this.closeRestoreAllModal}
          >
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title
                style={{ textAlign: "center", width: "100%", margin: 0 }}
              >
                Restore All Files
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>Do you want to restore all the files?</Modal.Body>
            <Modal.Footer className="modal-footer-modern">
              <div>
                <Button variant="info" onClick={this.restoreAll}>
                  Restore All
                </Button>
                <Button variant="gray" onClick={this.closeRestoreAllModal}>
                  Close
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        </div>
        {/* Empty Bin Modal */}
        <div>
          <Modal
            show={this.state.isEmptyBinModalOpen}
            onHide={this.closeEmptyBinModal}
          >
            <Modal.Header closeButton className="modal-header-modern">
              <Modal.Title
                style={{ textAlign: "center", width: "100%", margin: 0 }}
              >
                Empty Recycle Bin
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              Do you want to delete all the files permanently?
            </Modal.Body>
            <Modal.Footer className="modal-footer-modern">
              <div>
                <Button variant="info" onClick={this.emptyBin}>
                  Delete All
                </Button>
                <Button variant="gray" onClick={this.closeEmptyBinModal}>
                  Close
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        </div>
        {/* Restore File Modal */}
        <Modal
          show={this.state.isRestoreFileModalOpen}
          onHide={this.closeRestoreFileModal}
        >
          
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title
              style={{ textAlign: "center", width: "100%", margin: 0 }}
            >
              Restore File
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            Do you want to restore the file
            <strong>"{this.state.fileNameToRestore}"</strong> ?
          </Modal.Body>
          <Modal.Footer className="modal-footer-modern">
            <div>
              <Button
                variant="info"
                onClick={() => this.restoreFile(this.state.fileIdToRestore)}
              >
                Restore
              </Button>
              <Button variant="gray" onClick={this.closeRestoreFileModal}>
                Close
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {/* Delete File Modal */}
        <Modal
          show={this.state.isDeleteFileModalOpen}
          onHide={this.closeDeleteFileModal}
        >
          
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title
              style={{ textAlign: "center", width: "100%", margin: 0 }}
            >
              Delete File
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            Do you want to delete the file
            <strong>"{this.state.fileNameToDelete}"</strong> permanently?
          </Modal.Body>
          <Modal.Footer className="modal-footer-modern">
            <div>
              <Button
                variant="info"
                onClick={() => this.deleteFile(this.state.fileIdToDelete)}
              >
                Delete
              </Button>
              <Button variant="gray" onClick={this.closeDeleteFileModal}>
                Close
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default RecyleBin;
