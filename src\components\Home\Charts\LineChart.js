import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register required Chart.js components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const LineChart = (props) => {
  // Default color palette
  const colorPalette = [
    '#4338ca', '#059669', '#0891b2', '#db2777', '#8b5cf6',
    '#f59e0b', '#10b981', '#3b82f6', '#6366f1', '#ec4899'
  ];

  // Generate time series data for a given value
  const generateTimeSeriesData = (baseCount) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map((_, index) => {
      const variation = Math.floor(Math.random() * (baseCount * 0.3));
      const trend = index * 2;
      return Math.max(0, baseCount + variation + trend);
    });
  };

  // Prepare chart data based on props
  const prepareChartData = () => {
    // If data prop is provided, use that (from widgetReports)
    if (props.data) {
      const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
      const datasets = Object.entries(props.data).map(([label, value], index) => ({
        label: label,
        data: generateTimeSeriesData(value || 0),
        borderColor: colorPalette[index % colorPalette.length],
        backgroundColor: `rgba(${hexToRgb(colorPalette[index % colorPalette.length])}, 0.1)`,
        borderWidth: 2,
        fill: false,
        tension: 0.1,
      }));

      return {
        labels: labels,
        datasets: datasets
      };
    }
    
    // Fallback to individual props if data prop isn't provided
    return {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Duplicate Files',
          data: generateTimeSeriesData(props.duplicateCount || 15),
          borderColor: '#4338ca',
          backgroundColor: 'rgba(67, 56, 202, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
        },
        {
          label: 'Pending Workflows',
          data: generateTimeSeriesData(props.pendingWFCount || 8),
          borderColor: '#059669',
          backgroundColor: 'rgba(5, 150, 105, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
        },
        {
          label: 'Retention End',
          data: generateTimeSeriesData(props.retentionCount || 12),
          borderColor: '#0891b2',
          backgroundColor: 'rgba(8, 145, 178, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
        },
        {
          label: 'Due Date',
          data: generateTimeSeriesData(props.dueCount || 5),
          borderColor: '#db2777',
          backgroundColor: 'rgba(219, 39, 119, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
        },
        {
          label: 'Archive Files',
          data: generateTimeSeriesData(props.archiveCount || 20),
          borderColor: '#8b5cf6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.1,
        },
      ],
    };
  };

  // Helper function to convert hex to rgb
  const hexToRgb = (hex) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `${r}, ${g}, ${b}`;
  };

  const data = prepareChartData();
  console.log("linechart",data)

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 12,
          padding: 6,
          font: {
            size: 10
          }
        },
      },
      title: {
        display: true,
        text: props.title || 'Document Trends Over Time',
        font: {
          size: 14
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.parsed.y}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 10
          }
        }
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
        },
        ticks: {
          font: {
            size: 10
          }
        }
      },
    },
    interaction: {
      mode: 'index',
      intersect: false,
    },
    hover: {
      mode: 'index',
      intersect: false,
    },
    layout: {
      padding: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10
      }
    }
  };

  return (
    <div style={{ 
      position: 'relative',
      width: '100%', 
      height: '100%',
      minWidth: '200px',
      minHeight: '150px',
      overflow: 'hidden'
    }}>
      <Line data={data} options={options} />
    </div>
  );
};

export default LineChart;