/* NumberingModal.module.css */

.modalContainer {
    display: block;
    opacity: 1;
    visibility: visible;
    position: fixed;
    top: 100px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 500px;
    max-width: 90%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 20px;
  }
  
  .formModal {
    width: 100%;
  }
  
  .title {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .description {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 1rem;
  }
  
  .field {
    margin-bottom: 1.5rem;
  }
  .removeButton{
    border: none;
    background-color: white;
  }
  .label {
    font-size: 1rem;
    margin: 0.5rem 0px;
    cursor: pointer;
  }
  
  .inputRadio {
    margin-right: 0.5rem;
  }
  
  .numberingFields {
    margin-top: 1rem;
  }
  
  .textarea,
  .textInput,
  .numberInput,
  .select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
  }
  
  .textarea {
    height: 50px;
    resize: none;
  }
  
  .row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  .columns {
    flex: 1;
  }
  
  .numberingSchemeExample {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    padding: 0.5rem;
  }
  
  .multiSelect {
    height: 100px;
  }
  
  .submitButton {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    width: 100%;
  }
  
  .submitButton:hover {
    background-color: #0056b3;
  }
  
  .closeModalButton {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
  }
  
  /* Responsive adjustments */
  @media (max-width: 600px) {
    .modalContainer {
      width: 90%;
    }
  
    .row {
      flex-direction: column;
    }
  }
  