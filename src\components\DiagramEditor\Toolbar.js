import React, { useState } from 'react';
import { NODE_TYPES } from './types';

const Toolbar = ({ 
  onAddNode, 
  onAddSerialNode = null,     // New prop
  onAddParallelNode = null,   // New prop
  selectedNode = null,        // New prop to know which node is selected
  embedded = false, 
  usersLoading = false,
  onCopyNode = null,
  onPasteNode = null,
  canCopy = false,
  canPaste = false,
  onSave = null,
  onCancel = null,
  isEditMode = false,
  canSave = true,
  selectedNodeCount = 0,
  copiedNodeCount = 0,
  locked = false,
  onDownloadFile = null,      // New prop for download functionality
  onUploadFile = null         // New prop for upload functionality
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const toolbarStyle = embedded ? {
    width: isMinimized ? '50px' : '140px',
    minWidth: isMinimized ? '50px' : '140px',
    backgroundColor: 'white',
    borderRight: '1px solid #ddd',
    padding: isMinimized ? '8px 4px' : '16px',
    display: 'flex',
    flexDirection: 'column',
    gap: isMinimized ? '6px' : '10px',
    overflowY: 'auto',
    height: '100%',
    boxShadow: '2px 0 4px rgba(0,0,0,0.1)',
    transition: 'all 0.3s ease',
  } : {
    position: 'fixed',
    top: '20px',
    left: '20px',
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: isMinimized ? '6px 4px' : '10px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column',
    gap: isMinimized ? '3px' : '6px',
    zIndex: 1000,
    width: isMinimized ? '40px' : 'auto',
    minWidth: isMinimized ? '40px' : '100px',
    transition: 'all 0.3s ease',
  };

  const buttonStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    padding: embedded ? '8px 10px' : '4px 6px',
    border: '1px solid #ddd',
    borderRadius: '6px',
    backgroundColor: 'white',
    cursor: 'pointer',
    fontSize: embedded ? '13px' : '12px',
    transition: 'all 0.2s',
    minWidth: embedded ? '20%' : '40px',
    justifyContent: 'flex-start',
  };

  const iconStyle = {
    width: '18px',
    height: '18px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '4px',
    fontSize: '11px',
    fontWeight: 'bold',
    color: 'white',
  };

  const nodeTypes = [
    {
      type: NODE_TYPES.PROCESS,
      label: 'Process',
      icon: '⬜',
      color: '#0277bd',
    },
  ];

  const handleNodeClick = (nodeType) => {
    // Generate a random position for the new node
    const position = {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100,
    };

    onAddNode(nodeType, position);
  };

  const toggleButtonStyle = {
    position: 'absolute',
    bottom: '8px',
    right: '8px',
    width: '28px',
    height: '28px',
    border: '2px solid #007bff',
    borderRadius: '6px',
    backgroundColor: '#007bff',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    color: 'white',
    fontWeight: 'bold',
    transition: 'all 0.2s',
    zIndex: 100,
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  };

  return (
    <div style={{ ...toolbarStyle, position: 'relative' }}>
      {/* Toggle Button */}
      <button
        style={toggleButtonStyle}
        onClick={() => setIsMinimized(!isMinimized)}
        title={isMinimized ? 'Expand Toolbar' : 'Minimize Toolbar'}
        onMouseOver={(e) => {
          e.target.style.backgroundColor = '#0056b3';
          e.target.style.transform = 'scale(1.1)';
        }}
        onMouseOut={(e) => {
          e.target.style.backgroundColor = '#007bff';
          e.target.style.transform = 'scale(1)';
        }}
      >
        {isMinimized ? '▶' : '◀'}
      </button>

      {/* Content Container with padding to avoid toggle button overlap */}
      <div style={{ 
        paddingBottom: '40px', // Space for toggle button
        display: 'flex',
        flexDirection: 'column',
        gap: isMinimized ? (embedded ? '8px' : '4px') : (embedded ? '12px' : '8px'),
        height: '100%',
        overflowY: 'auto'
      }}>
        {/* Save/Cancel buttons at the top */}
        {embedded && onSave && onCancel && (
          <>
            <h4 style={{ margin: '0 0 4px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
              {sessionStorage.getItem("wfName")}
            </h4>
            
            <button
              style={{
                ...buttonStyle,
                backgroundColor: canSave ? '#28a745' : '#e9ecef',
                color: canSave ? 'white' : '#6c757d',
                border: `1px solid ${canSave ? '#28a745' : '#dee2e6'}`,
                cursor: canSave ? 'pointer' : 'not-allowed',
                width: isMinimized ? '40px' : '100px'
              }}
              onClick={canSave ? onSave : undefined}
              onMouseOver={(e) => {
                if (canSave) {
                  e.target.style.backgroundColor = '#218838';
                }
              }}
              onMouseOut={(e) => {
                if (canSave) {
                  e.target.style.backgroundColor = '#28a745';
                }
              }}
              title={canSave ? "Save workflow (Ctrl+S)" : "Add at least one process node to save"}
            >
              <div style={{ ...iconStyle, backgroundColor: canSave ? 'transparent' : '#6c757d',fontSize:"9px" }}>
                💾
              </div>
              {isEditMode ? 'Save/Update Changes' : 'Save/Update'}
            </button>

            <button
              style={{
                ...buttonStyle,
                backgroundColor: '#dc3545',
                color: 'white',
                border: '1px solid #dc3545',
                width: isMinimized ? "40px" : "100px"

              }}
              onClick={onCancel}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = '#c82333';
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = '#dc3545';
              }}
              title="Cancel and return to list (Esc)"
            >
              <div style={{ ...iconStyle, backgroundColor: 'transparent' }}>
                ❌
              </div>
              Cancel
            </button>

            <hr style={{ margin: embedded ? '4px 0' : '2px 0', border: 'none', borderTop: '1px solid #eee' }} />
          </>
        )}

        {/* Copy/Paste Section */}
        {!isMinimized ? (
        <>
          <h4 style={{ margin: '0 0 4px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
            Actions
          </h4>
          
          <button
            style={{
              ...buttonStyle,
              backgroundColor: canCopy ? '#007bff' : '#e9ecef',
              color: canCopy ? 'white' : '#6c757d',
              border: `1px solid ${canCopy ? '#007bff' : '#dee2e6'}`,
              cursor: canCopy ? 'pointer' : 'not-allowed',
              width: "100px"

            }}
            onClick={canCopy ? onCopyNode : undefined}
            onMouseOver={(e) => {
              if (canCopy) {
                e.target.style.backgroundColor = '#0056b3';
              }
            }}
            onMouseOut={(e) => {
              if (canCopy) {
                e.target.style.backgroundColor = '#007bff';
              }
            }}
            title={canCopy ? 
              (selectedNodeCount > 1 ? `Copy ${selectedNodeCount} selected nodes (Ctrl+C)` : 'Copy selected node (Ctrl+C)') : 
              'Select a node to copy'}
          >
            <div style={{ ...iconStyle, backgroundColor: canCopy ? 'transparent' : '#6c757d' }}>
              📋
            </div>
            {selectedNodeCount > 1 ? `Copy ${selectedNodeCount}` : 'Copy'}
          </button>

          <button
            style={{
              ...buttonStyle,
              backgroundColor: canPaste ? '#28a745' : '#e9ecef',
              color: canPaste ? 'white' : '#6c757d',
              border: `1px solid ${canPaste ? '#28a745' : '#dee2e6'}`,
              cursor: canPaste ? 'pointer' : 'not-allowed',
              width: "100px"

            }}
            onClick={canPaste ? onPasteNode : undefined}
            onMouseOver={(e) => {
              if (canPaste) {
                e.target.style.backgroundColor = '#218838';
              }
            }}
            onMouseOut={(e) => {
              if (canPaste) {
                e.target.style.backgroundColor = '#28a745';
              }
            }}
            title={canPaste ? 
              (copiedNodeCount > 1 ? `Paste ${copiedNodeCount} copied nodes (Ctrl+V)` : 'Paste copied node (Ctrl+V)') : 
              'No node copied'}
          >
            <div style={{ ...iconStyle, backgroundColor: canPaste ? 'transparent' : '#6c757d' }}>
              📄
            </div>
            {copiedNodeCount > 1 ? `Paste ${copiedNodeCount}` : 'Paste'}
          </button>

          <hr style={{ margin: embedded ? '4px 0' : '2px 0', border: 'none', borderTop: '1px solid #eee' }} />
          
          {/* Smart Connect Section */}
          <h4 style={{ margin: '0 0 4px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
            Connectors
          </h4>
          
          {/* Add Serial Button */}
          <button
            style={{
              ...buttonStyle,
              backgroundColor: (selectedNode && !locked) ? '#4caf50' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#4caf50' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "100px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              console.log('Add Serial clicked, selectedNode:', selectedNode); // Debug log
              onAddSerialNode();
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#388e3c';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#4caf50';
              }
            }}
            title={locked ? 'Cannot add nodes when diagram is locked' : (selectedNode ? 'Add serial node connected to selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              →
            </div>
            Serial
          </button>

          {/* Add Parallel Button */}
          <button
            style={{
              ...buttonStyle,
              backgroundColor: (selectedNode && !locked) ? '#ff9800' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#ff9800' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "100px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              console.log('Add Parallel clicked, selectedNode:', selectedNode); // Debug log
              onAddParallelNode();
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#f57c00';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#ff9800';
              }
            }}
            title={locked ? 'Cannot add nodes when diagram is locked' : (selectedNode ? 'Add parallel node connected to selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              ∥
            </div>
            Parallel
          </button>

          <hr style={{ margin: embedded ? '4px 0' : '2px 0', border: 'none', borderTop: '1px solid #eee' }} />
          
          {/* File Operations Section */}
          <h4 style={{ margin: '0 0 4px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
            File
             {/* Operations */}
          </h4>
          
          {/* Download Button */}
          <button
            style={{
              ...buttonStyle,
              backgroundColor: (selectedNode && !locked) ? '#4caf50' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#4caf50' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "100px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              if (onDownloadFile && selectedNode) {
                onDownloadFile(selectedNode.dtoId);
              }
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#388e3c';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#4caf50';
              }
            }}
            title={locked ? 'Cannot download when diagram is locked' : (selectedNode ? 'Download file for selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              ⬇️
            </div>
            Download
          </button>

          {/* Upload Button */}
          <button
            style={{
              ...buttonStyle,
              backgroundColor: (selectedNode && !locked) ? '#2196f3' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#2196f3' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "100px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              if (onUploadFile && selectedNode) {
                onUploadFile(selectedNode.dtoId);
              }
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#1976d2';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#2196f3';
              }
            }}
            title={locked ? 'Cannot upload when diagram is locked' : (selectedNode ? 'Upload file for selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              📁
            </div>
            Upload
          </button>

          <hr style={{ margin: embedded ? '4px 0' : '2px 0', border: 'none', borderTop: '1px solid #eee' }} />

          {/* Node Types Section */}
          <h4 style={{ margin: '0 0 4px 0', fontSize: embedded ? '15px' : '12px', color: '#333', fontWeight: '600' }}>
            Add Node
          </h4>
        </>
      ) : (
        <>
          {/* Minimized Copy/Paste Buttons */}
          <button
            style={{
              ...buttonStyle,
              padding: embedded ? '8px' : '6px',
              justifyContent: 'center',
              minWidth: 'auto',
              backgroundColor: canCopy ? '#007bff' : '#e9ecef',
              color: canCopy ? 'white' : '#6c757d',
              border: `1px solid ${canCopy ? '#007bff' : '#dee2e6'}`,
              cursor: canCopy ? 'pointer' : 'not-allowed',
              width: "40px"

            }}
            onClick={canCopy ? onCopyNode : undefined}
            onMouseOver={(e) => {
              if (canCopy) {
                e.target.style.backgroundColor = '#0056b3';
              }
            }}
            onMouseOut={(e) => {
              if (canCopy) {
                e.target.style.backgroundColor = '#007bff';
              }
            }}
            title={canCopy ? 'Copy selected node (Ctrl+C)' : 'Select a node to copy'}
          >
            <div style={{ ...iconStyle, backgroundColor: canCopy ? 'transparent' : '#6c757d' }}>
              📋
            </div>
          </button>

          <button
            style={{
              ...buttonStyle,
              padding: embedded ? '8px' : '6px',
              justifyContent: 'center',
              minWidth: 'auto',
              backgroundColor: canPaste ? '#28a745' : '#e9ecef',
              color: canPaste ? 'white' : '#6c757d',
              border: `1px solid ${canPaste ? '#28a745' : '#dee2e6'}`,
              cursor: canPaste ? 'pointer' : 'not-allowed',
              width: "40px"

            }}
            onClick={canPaste ? onPasteNode : undefined}
            onMouseOver={(e) => {
              if (canPaste) {
                e.target.style.backgroundColor = '#218838';
              }
            }}
            onMouseOut={(e) => {
              if (canPaste) {
                e.target.style.backgroundColor = '#28a745';
              }
            }}
            title={canPaste ? 'Paste copied node (Ctrl+V)' : 'No node copied'}
          >
            <div style={{ ...iconStyle, backgroundColor: canPaste ? 'transparent' : '#6c757d' }}>
              📄
            </div>
          </button>

          {/* Minimized Download Button */}
          <button
            style={{
              ...buttonStyle,
              padding: embedded ? '8px' : '6px',
              justifyContent: 'center',
              minWidth: 'auto',
              backgroundColor: (selectedNode && !locked) ? '#4caf50' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#4caf50' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "40px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              if (onDownloadFile && selectedNode) {
                onDownloadFile(selectedNode.dtoId);
              }
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#388e3c';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#4caf50';
              }
            }}
            title={locked ? 'Cannot download when diagram is locked' : (selectedNode ? 'Download file for selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              ⬇️
            </div>
          </button>

          {/* Minimized Upload Button */}
          <button
            style={{
              ...buttonStyle,
              padding: embedded ? '8px' : '6px',
              justifyContent: 'center',
              minWidth: 'auto',
              backgroundColor: (selectedNode && !locked) ? '#2196f3' : '#e9ecef',
              color: (selectedNode && !locked) ? 'white' : '#6c757d',
              border: `1px solid ${(selectedNode && !locked) ? '#2196f3' : '#dee2e6'}`,
              cursor: (selectedNode && !locked) ? 'pointer' : 'not-allowed',
              width: "40px"
            }}
            onClick={(selectedNode && !locked) ? () => {
              if (onUploadFile && selectedNode) {
                onUploadFile(selectedNode.dtoId);
              }
            } : undefined}
            onMouseOver={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#1976d2';
              }
            }}
            onMouseOut={(e) => {
              if (selectedNode && !locked) {
                e.target.style.backgroundColor = '#2196f3';
              }
            }}
            title={locked ? 'Cannot upload when diagram is locked' : (selectedNode ? 'Upload file for selected node' : 'Select a node first')}
          >
            <div style={{ ...iconStyle, backgroundColor: (selectedNode && !locked) ? 'transparent' : '#6c757d' }}>
              📁
            </div>
          </button>

          {/* Minimized Save/Cancel Buttons - only show when explicitly provided */}
          {embedded && onSave && onCancel && (
            <>
              <button
                style={{
                  ...buttonStyle,
                  padding: embedded ? '8px' : '6px',
                  justifyContent: 'center',
                  minWidth: 'auto',
                  backgroundColor: canSave ? '#28a745' : '#e9ecef',
                  color: canSave ? 'white' : '#6c757d',
                  border: `1px solid ${canSave ? '#28a745' : '#dee2e6'}`,
                  cursor: canSave ? 'pointer' : 'not-allowed',
                  width: embedded ? "40px" : "400px"
                }}
                onClick={canSave ? onSave : undefined}
                onMouseOver={(e) => {
                  if (canSave) {
                    e.target.style.backgroundColor = '#218838';
                  }
                }}
                onMouseOut={(e) => {
                  if (canSave) {
                    e.target.style.backgroundColor = '#28a745';
                  }
                }}
                title={canSave ? "Save workflow (Ctrl+S)" : "Add at least one process node to save"}
              >
                <div style={{ ...iconStyle, backgroundColor: canSave ? 'transparent' : '#6c757d' }}>
                  💾
                </div>
              </button>

              <button
                style={{
                  ...buttonStyle,
                  padding: embedded ? '8px' : '6px',
                  justifyContent: 'center',
                  minWidth: 'auto',
                  backgroundColor: '#dc3545',
                  color: 'white',
                  border: '1px solid #dc3545',
                  width: "40px"

                }}
                onClick={onCancel}
                onMouseOver={(e) => {
                  e.target.style.backgroundColor = '#c82333';
                }}
                onMouseOut={(e) => {
                  e.target.style.backgroundColor = '#dc3545';
                }}
                title="Cancel and return to list (Esc)"
              >
                <div style={{ ...iconStyle, backgroundColor: 'transparent' }}>
                  ❌
                </div>
              </button>
            </>
          )}
        </>
      )}
      
      {nodeTypes.map(({ type, label, icon, color, tooltip }) => (
        <button
          key={type}
          style={{
            ...buttonStyle,
            padding: isMinimized ? (embedded ? '8px' : '6px') : buttonStyle.padding,
            justifyContent: isMinimized ? 'center' : 'flex-start',
            minWidth: isMinimized ? 'auto' : buttonStyle.minWidth,
            width: isMinimized ? "40px" : "100px"
          }}
          onClick={() => handleNodeClick(type)}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = '#f5f5f5';
            e.target.style.borderColor = color;
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = 'white';
            e.target.style.borderColor = '#ddd';
          }}
          title={isMinimized ? label : tooltip || label}
        >
          <div style={{ ...iconStyle, backgroundColor: color }}>
            {icon}
          </div>
          {!isMinimized && label}
        </button>
      ))}
      
      {!isMinimized && (
        <>
          {/* <hr style={{ margin: embedded ? '3px 0' : '1px 0', border: 'none', borderTop: '1px solid #eee' }} /> */}
          
          {/* <div style={{
            fontSize: embedded ? '12px' : '11px',
            color: '#666',
            textAlign: 'center',
            lineHeight: '1.3',
          }}>
            Drag nodes to position them<br/>
            Click to select<br/>
            Connect with handles (2 inputs/2 outputs)
          </div> */}
          
          {usersLoading && (
            <div style={{
              fontSize: '11px',
              color: '#666',
              textAlign: 'center',
              padding: '8px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              margin: '8px 0 0 0',
            }}>
              ⟳ Loading users...
            </div>
          )}


        </>
      )}
      </div>
    </div>
  );
};

export default Toolbar;
