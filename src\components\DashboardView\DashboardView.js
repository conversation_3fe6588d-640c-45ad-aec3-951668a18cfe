import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Mo<PERSON>, Button } from 'react-bootstrap';
import {
  getList,
  addNew,
  editById,
  deleteById,
} from "../../services/apiService";
import Notification from "../Notification/Notification";
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Resizable } from 're-resizable';
import CustomBreadcrumb from '../common/CustomBreadcrumb';
import BarChart from '../Home/Charts/BarChart';
import PieChart from '../Home/Charts/PieChart';
import LineChart from '../Home/Charts/LineChart';
import { DataTable } from '../Table/DataTable';
import { availableWidgets } from '../../config/widgetConfig';
import Loader from '../loader/Loader';

// Sortable Widget Component with Resize and Free Positioning
function SortableWidget({ id, children, widgetType, position, onPositionChange, onSizeChange, size, isDeleting, canModify = true }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id, disabled: !canModify });

  // state = {
  //    notification: {
  //     message: "",
  //     type: "",
  //     show: false,
  //   },
  //   currentPage: 0,
  //   totalItems: 0,
  //   itemsPerPage: 20,
  //   searchParam: "",
  // }

  const [localSize, setLocalSize] = useState(size || getDefaultSize(widgetType));

  // Update local size when external size changes (from auto-arrange)
  React.useEffect(() => {
    if (size && (size.width !== localSize.width || size.height !== localSize.height)) {
      setLocalSize(size);
    }
  }, [size, localSize]);

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? transition : 'none',
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 'auto',
    position: 'absolute',
    left: position?.x || 0,
    top: position?.y || 0,
  };

  function getDefaultSize(type) {
    switch (type) {
      case 'table':
        return { width: 600, height: 400 };
      case 'stats-card':
        return { width: 300, height: 200 };
      case 'bar-chart':
      case 'pie-chart':
      case 'line-chart':
        return { width: 500, height: 400 };
      default:
        return { width: 400, height: 300 };
    }
  }

  const resizeStyle = {
    display: 'flex',
    flexDirection: 'column',
    width: localSize.width,
    height: localSize.height,
    minWidth: 200,
    minHeight: 150,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`position-absolute widget-container ${isDeleting ? 'widget-deleting' : ''}`}
      {...attributes}
    >
      <Resizable
        style={resizeStyle}
        size={localSize}
        onResizeStop={(e, direction, ref, d) => {
          const newSize = {
            width: localSize.width + d.width,
            height: localSize.height + d.height,
          };
          setLocalSize(newSize);
          // Update the parent component's size tracking
          if (onSizeChange) {
            onSizeChange(id, newSize);
          }
        }}
        enable={{
          top: false,
          right: true,
          bottom: true,
          left: false,
          topRight: true,
          bottomRight: true,
          bottomLeft: false,
          topLeft: false,
        }}
        handleStyles={{
          right: {
            width: '8px',
            right: '-4px',
            backgroundColor: '#007bff',
            opacity: 0.3,
            borderRadius: '4px',
          },
          bottom: {
            height: '8px',
            bottom: '-4px',
            backgroundColor: '#007bff',
            opacity: 0.3,
            borderRadius: '4px',
          },
          bottomRight: {
            width: '20px',
            height: '20px',
            right: '-10px',
            bottom: '-10px',
            backgroundColor: '#007bff',
            borderRadius: '4px',
            opacity: 0.8,
            border: '2px solid white',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'nw-resize',
          },
          topRight: {
            width: '12px',
            height: '12px',
            right: '-6px',
            top: '-6px',
            backgroundColor: '#007bff',
            borderRadius: '50%',
            opacity: 0.7,
            border: '2px solid white',
          },
        }}
        handleClasses={{
          right: 'resize-handle-right',
          bottom: 'resize-handle-bottom',
          bottomRight: 'resize-handle-corner-main',
          topRight: 'resize-handle-corner',
        }}
        handleComponent={{
          bottomRight: (
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              color: 'white',
              fontWeight: 'bold'
            }}>
              ⋰⋱
            </div>
          )
        }}
      >
        <div 
          {...(canModify ? listeners : {})} 
          style={{ cursor: canModify ? (isDragging ? 'grabbing' : 'grab') : 'default', flex: 1 }}
          className="h-100"
        >
          {children}
        </div>
      </Resizable>
    </div>
  );
}

function DashboardView() {
  const location = useLocation();
  const navigate = useNavigate();
  const { dashboardData } = location.state || {};

  // Get current user role and ID for permission checks
  const currentUserRole = localStorage.getItem("role");
  const currentUserId = parseInt(localStorage.getItem("id"));
  const currentUserName = localStorage.getItem("userName");
  const isAdmin = currentUserRole === "ROLE_SUPER_ADMIN";
  
  // Allow SUPER_ADMIN users to modify any dashboard, while other users can only modify their own
  const canModifyDashboard = isAdmin || dashboardData?.createdBy === currentUserName;

  // Add debug logging to help troubleshoot permission issues
  console.log("Permission Check:", {
    currentUserRole,
    currentUserName,
    isAdmin,
    dashboardCreator: dashboardData?.createdBy,
    canModifyDashboard
  });

  // Add widgetReports state
  const [widgetReports, setWidgetReports] = useState(dashboardData?.widgetReports || {});

  // Move useEffect before conditional code
  // Restore layout when dashboard loads
  useEffect(() => {
    console.log("dashboardData.widgetLayout", dashboardData?.widgetLayout);
    
    if (dashboardData?.widgetLayout && Object.keys(dashboardData.widgetLayout).length > 0) {
      // Restore widget positions and sizes
      const restoredPositions = {};
      const restoredSizes = {};
      
      Object.entries(dashboardData.widgetLayout).forEach(([widgetId, layout]) => {
        if (layout?.position) {
          restoredPositions[widgetId] = layout.position;
        }
        if (layout?.size) {
          restoredSizes[widgetId] = layout.size;
        }
      });

      // Only update if we actually have positions or sizes to restore
      if (Object.keys(restoredPositions).length > 0) {
        setWidgetPositions(restoredPositions);
      }
      if (Object.keys(restoredSizes).length > 0) {
        setWidgetSizes(restoredSizes);
      }
    }
  }, [dashboardData]);

  const [notification, setNotification] = useState({
    message: "",
    type: "",
    show: false,
  });
  const [loading, setLoading] = useState(false);

  const [duplicateCount, setDuplicateCount] = useState(null);
  const [pendingWFCount, setPendingWFCount] = useState(null);
  const [retentionCount, setRetentionCount] = useState(null);
  const [dueCount, setDueCount] = useState(null);
  const [archiveCount, setArchiveCount] = useState(null);
  const [documents, setDocuments] = useState([]);
  const [count, setCount] = useState(null);

  useEffect(() => {
    fetchDocumentCount();
    fetchDuplicateCount();
    fetchPendingWorkFlowCount();
    fetchDueDateCount();
    fetchRetentionEndCount();
    fetchArchivedFilesCount();
  }, []);

  const closeNotification = () => {
    setNotification({ message: "", type: "", show: false });
  };

  const fetchDocumentCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setDocuments(data.content);
      setCount(data.totalElements);
    } catch (error) {
      console.log(error);
    }
  };
  
  const fetchDuplicateCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_duplicates_count?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setDuplicateCount(data.totalElements);
    } catch (error) {
      console.log(error);
    }
  };
  
  const fetchPendingWorkFlowCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_pending_workflows_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
  
      const awaitingCount = data.content.reduce((count, flow) => {
        const hasPendingLevel = flow.approvalLevelDTO?.some(
          (level) => !level.levelStatus
        );
        return hasPendingLevel ? count + 1 : count;
      }, 0);
      setPendingWFCount(awaitingCount);
    } catch (e) {
      console.log(e);
    }
  };
  
  const fetchRetentionEndCount = async () => {
    const api = `/documentreports/list_by_all_retention_end_files?page=0&size=2000`;
    try {
      const response = await getList(api);
      const data = response.data;
      setRetentionCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };
  
  const fetchDueDateCount = async () => {
    const api = `/documentreports/list_by_due_date_documents?page=0&size=2000`;
    try {
      const response = await getList(api);
      const data = response.data;
      setDueCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };
  
  const fetchArchivedFilesCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_archive_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setArchiveCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };

  const counts = {
    duplicate_files: duplicateCount || 0,
    pending_work_flow_files: pendingWFCount || 0,
    retention_end_files: retentionCount || 0,
    due_date_files: dueCount || 0,
    archive_files: archiveCount || 0,
    total_files: count || 0,
  };

  const [showSaveModal, setShowSaveModal] = useState(false);
  // State to manage widget order and positions
  const [widgetOrder, setWidgetOrder] = useState(() => {
    // Check if we have saved layout data
    if (dashboardData?.widgetLayout && Object.keys(dashboardData.widgetLayout).length > 0) {
      // Use saved layout data - extract IDs from object keys
      return Object.keys(dashboardData.widgetLayout);
    }
    
    const widgets = dashboardData?.selectedWidgets || [];
    
    // Check if widgets are objects (old format from previous version)
    const isOldObjectFormat = widgets.length > 0 && typeof widgets[0] === 'object';
    
    if (isOldObjectFormat) {
      // Old object format: widgets are objects, just extract IDs
      return widgets.map(widget => widget.id);
    }
    
    // Default: Convert simple widget IDs from CreateDashboard to numbered instances
    const numberedWidgets = [];
    const typeCounters = {};
    
    widgets.forEach(widgetId => {
      // Check if this is already a numbered widget (from Add Widgets modal)
      if (widgetId.match(/-\d+$/)) {
        // Already numbered, use as-is
        numberedWidgets.push(widgetId);
      } else {
        // Simple widget ID from CreateDashboard, convert to numbered
        typeCounters[widgetId] = (typeCounters[widgetId] || 0) + 1;
        const numberedId = `${widgetId}-${typeCounters[widgetId]}`;
        numberedWidgets.push(numberedId);
      }
    });
    
    return numberedWidgets;
  });
  
  // State for add widgets modal
  const [showAddWidgetsModal, setShowAddWidgetsModal] = useState(false);
  const [selectedNewWidgets, setSelectedNewWidgets] = useState([]);
  
  // State for widget reports assignment modal
  const [showWidgetReportsModal, setShowWidgetReportsModal] = useState(false);
  const [currentWidgetForReports, setCurrentWidgetForReports] = useState(null);
  const [newWidgetReports, setNewWidgetReports] = useState({});
  
  // Reports list for widget configuration
  const [reportList] = useState([
    'Total Files', 'Duplicate Files', 'Pending Workflows Files', 
    'Due Date Files', 'Archive Files', 'Retention End Files'
  ]);
  
  // State to track widgets being deleted for animation
  const [deletingWidgets, setDeletingWidgets] = useState(new Set());
  
  // State to track widget types for each instance
  const [widgetTypes, setWidgetTypes] = useState(() => {
    const types = {};
    
    // Check if we have saved layout data
    if (dashboardData?.widgetLayout && Object.keys(dashboardData.widgetLayout).length > 0) {
      // Use saved layout data
      Object.entries(dashboardData.widgetLayout).forEach(([widgetId, layout]) => {
        // Extract base type from widget ID if not explicitly provided
        types[widgetId] = layout?.type || widgetId.replace(/-\d+$/, '');
      });
      return types;
    }
    
    const widgets = dashboardData?.selectedWidgets || [];
    
    // Check if widgets are objects (old format from previous version)
    const isOldObjectFormat = widgets.length > 0 && typeof widgets[0] === 'object';
    
    if (isOldObjectFormat) {
      // Old object format: widgets are objects with id and type
      widgets.forEach(widget => {
        types[widget.id] = widget.type;
      });
      return types;
    }
    
    // Default: widgets are just IDs
    widgets.forEach(widgetId => {
      // Check if this is already a numbered widget
      if (widgetId.match(/-\d+$/)) {
        // Extract base type from numbered widget
        const widgetType = widgetId.replace(/-\d+$/, '');
        types[widgetId] = widgetType;
      } else {
        // Simple widget ID, use as-is and create numbered version
        types[`${widgetId}-1`] = widgetId;
      }
    });
    return types;
  });

  // Generate unique widget ID
  const generateWidgetId = (widgetType) => {
    const existingIds = widgetOrder.filter(id => widgetTypes[id] === widgetType || id.startsWith(widgetType));
    const maxNumber = existingIds.length;
    return `${widgetType}-${maxNumber + 1}`;
  };

  const [widgetPositions, setWidgetPositions] = useState(() => {
    // Initialize positions in a grid layout
    const positions = {};
    
    // Check if we have saved layout data
    if (dashboardData?.widgetLayout && Object.keys(dashboardData.widgetLayout).length > 0) {
      // Use saved layout data
      Object.entries(dashboardData.widgetLayout).forEach(([widgetId, layout]) => {
        if (layout?.position) {
          positions[widgetId] = layout.position;
        }
      });
      return positions;
    }
    
    const widgets = dashboardData?.selectedWidgets || [];
    
    // Check if widgets are objects (old format from previous version)
    const isOldObjectFormat = widgets.length > 0 && typeof widgets[0] === 'object';
    
    if (isOldObjectFormat) {
      // Old object format: widgets are objects with id, type, position, size
      widgets.forEach(widget => {
        if (widget.position) {
          positions[widget.id] = widget.position;
        }
      });
      return positions;
    }
    
    // Default: widgets are just IDs, need to convert and position
    const initialWidgetOrder = (() => {
      const numberedWidgets = [];
      const typeCounters = {};
      
      widgets.forEach(widgetId => {
        if (widgetId.match(/-\d+$/)) {
          numberedWidgets.push(widgetId);
        } else {
          typeCounters[widgetId] = (typeCounters[widgetId] || 0) + 1;
          const numberedId = `${widgetId}-${typeCounters[widgetId]}`;
          numberedWidgets.push(numberedId);
        }
      });
      
      return numberedWidgets;
    })();
    
    let currentX = 20;
    let currentY = 20;
    let maxHeightInRow = 0;
    const containerWidth = 1200; // Approximate container width
    
    initialWidgetOrder.forEach((widgetId, index) => {
      const widgetType = widgetId.replace(/-\d+$/, '');
      const defaultSize = getDefaultSizeForType(widgetType);
      
      // Check if we need to wrap to next row
      if (currentX + defaultSize.width > containerWidth && index > 0) {
        currentX = 20;
        currentY += maxHeightInRow + 20;
        maxHeightInRow = 0;
      }
      
      positions[widgetId] = { x: currentX, y: currentY };
      currentX += defaultSize.width + 20;
      maxHeightInRow = Math.max(maxHeightInRow, defaultSize.height);
    });
    
    return positions;
  });

  // Store widget sizes for collision detection
  const [widgetSizes, setWidgetSizes] = useState(() => {
    const sizes = {};
    
    // Check if we have saved layout data
    if (dashboardData?.widgetLayout && Object.keys(dashboardData.widgetLayout).length > 0) {
      // Use saved layout data
      Object.entries(dashboardData.widgetLayout).forEach(([widgetId, layout]) => {
        if (layout?.size) {
          sizes[widgetId] = layout.size;
        } else {
          // Fallback to default size if not specified
          const widgetType = layout?.type || widgetId.replace(/-\d+$/, '');
          sizes[widgetId] = getDefaultSizeForType(widgetType);
        }
      });
      return sizes;
    }
    
    const widgets = dashboardData?.selectedWidgets || [];
    
    // Check if widgets are objects (old format from previous version)
    const isOldObjectFormat = widgets.length > 0 && typeof widgets[0] === 'object';
    
    if (isOldObjectFormat) {
      // Old object format: widgets are objects with id, type, position, size
      widgets.forEach(widget => {
        if (widget.size) {
          sizes[widget.id] = widget.size;
        } else {
          // Fallback to default size if not specified
          const widgetType = widget.type || widget.id.replace(/-\d+$/, '');
          sizes[widget.id] = getDefaultSizeForType(widgetType);
        }
      });
      return sizes;
    }
    
    // Default: widgets are just IDs, need to convert and size
    const initialWidgetOrder = (() => {
      const numberedWidgets = [];
      const typeCounters = {};
      
      widgets.forEach(widgetId => {
        if (widgetId.match(/-\d+$/)) {
          numberedWidgets.push(widgetId);
        } else {
          typeCounters[widgetId] = (typeCounters[widgetId] || 0) + 1;
          const numberedId = `${widgetId}-${typeCounters[widgetId]}`;
          numberedWidgets.push(numberedId);
        }
      });
      
      return numberedWidgets;
    })();
    
    initialWidgetOrder.forEach(widgetId => {
      const widgetType = widgetId.replace(/-\d+$/, '');
      sizes[widgetId] = getDefaultSizeForType(widgetType);
    });
    return sizes;
  });

  function getDefaultSizeForType(type) {
    switch (type) {
      case 'table':
        return { width: 600, height: 400 };
      case 'stats-card':
        return { width: 600, height: 400 };
      case 'bar-chart':
      case 'pie-chart':
      case 'line-chart':
        return { width: 500, height: 400 };
      default:
        return { width: 400, height: 300 };
    }
  }

  // Check if two rectangles overlap
  const checkOverlap = (rect1, rect2) => {
    return !(
      rect1.x + rect1.width <= rect2.x ||
      rect2.x + rect2.width <= rect1.x ||
      rect1.y + rect1.height <= rect2.y ||
      rect2.y + rect2.height <= rect1.y
    );
  };

  // Find a non-overlapping position for a widget
  const findNonOverlappingPosition = (targetWidgetId, desiredPosition) => {
    const targetSize = widgetSizes[targetWidgetId];
    const margin = 10; // Minimum margin between widgets
    
    // Create the desired rectangle
    let testRect = {
      x: desiredPosition.x,
      y: desiredPosition.y,
      width: targetSize.width,
      height: targetSize.height
    };

    // Check against all other widgets
    const otherWidgets = widgetOrder.filter(id => id !== targetWidgetId);
    let hasOverlap = false;
    
    for (const widgetId of otherWidgets) {
      const otherPos = widgetPositions[widgetId];
      const otherSize = widgetSizes[widgetId];
      
      if (otherPos) {
        const otherRect = {
          x: otherPos.x,
          y: otherPos.y,
          width: otherSize.width,
          height: otherSize.height
        };
        
        if (checkOverlap(testRect, otherRect)) {
          hasOverlap = true;
          break;
        }
      }
    }
    
    if (!hasOverlap) {
      return desiredPosition; // No overlap, use desired position
    }

    // If there's overlap, try to find a nearby position
    const stepSize = 20;
    const maxAttempts = 50;
    
    // Try positions in expanding circles around the desired position
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const radius = attempt * stepSize;
      
      // Try positions around the desired position
      const testPositions = [
        { x: desiredPosition.x + radius, y: desiredPosition.y }, // Right
        { x: desiredPosition.x - radius, y: desiredPosition.y }, // Left
        { x: desiredPosition.x, y: desiredPosition.y + radius }, // Down
        { x: desiredPosition.x, y: desiredPosition.y - radius }, // Up
        { x: desiredPosition.x + radius, y: desiredPosition.y + radius }, // Bottom-right
        { x: desiredPosition.x - radius, y: desiredPosition.y - radius }, // Top-left
        { x: desiredPosition.x + radius, y: desiredPosition.y - radius }, // Top-right
        { x: desiredPosition.x - radius, y: desiredPosition.y + radius }, // Bottom-left
      ];
      
      for (const testPos of testPositions) {
        // Ensure position is not negative
        if (testPos.x < 0 || testPos.y < 0) continue;
        
        testRect = {
          x: testPos.x,
          y: testPos.y,
          width: targetSize.width,
          height: targetSize.height
        };
        
        let foundOverlap = false;
        for (const widgetId of otherWidgets) {
          const otherPos = widgetPositions[widgetId];
          const otherSize = widgetSizes[widgetId];
          
          if (otherPos) {
            const otherRect = {
              x: otherPos.x,
              y: otherPos.y,
              width: otherSize.width,
              height: otherSize.height
            };
            
            if (checkOverlap(testRect, otherRect)) {
              foundOverlap = true;
              break;
            }
          }
        }
        
        if (!foundOverlap) {
          return testPos; // Found a non-overlapping position
        }
      }
    }
    
    // If we couldn't find a good position, return the original desired position
    // This shouldn't happen often with the expanding search
    return desiredPosition;
  };

  // Update widget size tracking
  const updateWidgetSize = (widgetId, newSize) => {
    setWidgetSizes(prev => ({
      ...prev,
      [widgetId]: newSize
    }));
  };

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before activating drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  if (!dashboardData) {
    return (
      <div className="container mt-4">
        <div className="alert alert-warning">
          <h4>No Dashboard Data Found</h4>
          <p>Please create a dashboard first.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/newDS/createDashboard')}
          >
            Create Dashboard
          </button>
        </div>
      </div>
    );
  }

  const { dashboardName, dashboardDescription } = dashboardData;

  // Handle drag end with collision detection
  function handleDragEnd(event) {
    const { active, delta } = event;

    if (delta && (delta.x !== 0 || delta.y !== 0)) {
      const currentPosition = widgetPositions[active.id] || { x: 0, y: 0 };
      const desiredPosition = {
        x: Math.max(0, currentPosition.x + delta.x),
        y: Math.max(0, currentPosition.y + delta.y),
      };
      
      // Find a non-overlapping position
      const finalPosition = findNonOverlappingPosition(active.id, desiredPosition);
      
      setWidgetPositions(prev => ({
        ...prev,
        [active.id]: finalPosition
      }));
    }
  }

  // Handle position changes from individual widgets
  const handlePositionChange = (widgetId, newPosition) => {
    setWidgetPositions(prev => ({
      ...prev,
      [widgetId]: newPosition
    }));
  };


  const saveDashboardView = async () => {
    // Check permissions before saving
    if (!canModifyDashboard) {
      setNotification({
        message: "You do not have permission to modify this dashboard",
        type: "error",
        show: true,
      });
      return;
    }

    try {
      const transformKeyToCamelCase = (key) => {
        return key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      };

      // Transform widget layout data
      const widgetLayoutData = {};
      widgetOrder.forEach(widgetId => {
        const position = widgetPositions[widgetId];
        const size = widgetSizes[widgetId];
        widgetLayoutData[widgetId] = {
          position: position || { x: 0, y: 0 },
          size: size || getDefaultSizeForType(widgetId.split('-')[0])
        };
      });

      // Transform widgetReports to camelCase
      const transformedReports = Object.fromEntries(
        Object.entries(widgetReports).map(([key, value]) => [
          transformKeyToCamelCase(key),
          value,
        ])
      );

      // Prepare dashboard data with layout information
      const dashboardViewData = {
        name: dashboardData.dashboardName,
        description: dashboardData.dashboardDescription,
        widgets: widgetOrder,
        widgetLayout: widgetLayoutData,  // Add layout data
        widgetReports: transformedReports,
        assignedUsers:dashboardData.assignedUsers
      };


      const api = `/managedashboard?id=${localStorage.getItem("id")}`;
      await addNew(api, dashboardViewData);
      
      setNotification({
        message: "Dashboard view saved successfully",
        type: "success",
        show: true,
      });
    } catch (error) {
      setNotification({
        message: "Error saving dashboard view",
        type: "error",
        show: true,
      });
    }
    finally{
      setTimeout(() => {
        navigate('/newDS/dashboardManagement')
      }, 750);
    }
  };

  const updateDashboardView = async () => {
    // Check permissions before updating
    if (!canModifyDashboard) {
      setNotification({
        message: "You do not have permission to modify this dashboard",
        type: "error",
        show: true,
      });
      return;
    }

    //setShowSaveModal(true);
    setLoading(true)

    const widgetReportsDTO = {};

    const transformKeyToCamelCase = (key) => {
      return key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
    };
  

    availableWidgets.forEach(widget => {
      const camelKey = widget.alias;
      widgetReportsDTO[camelKey] = dashboardData.widgetReports[widget.id] || [];
    });

    
    const transformedWidgetReports = {};
    
    Object.entries(dashboardData.widgetReports).forEach(([key, value]) => {
      const camelCaseKey = transformKeyToCamelCase(key);
      transformedWidgetReports[camelCaseKey] = value;
    });
  
    const dashboardConfig = {
      name: dashboardName,
      description: dashboardDescription,
      widgets: widgetOrder, // Keep as array of IDs for API compatibility
      widgetLayout: widgetOrder.map(id => ({
        id,
        type: widgetTypes[id],
        position: widgetPositions[id],
        size: widgetSizes[id]
      })), // Store layout data separately
      widgetReports: transformedWidgetReports,
      createdAt: new Date().toISOString()
    };
  
    console.log("Final Dashboard Config:", dashboardConfig);
    console.log("DASHBOARD ID : ", dashboardData.id);

     const api = `/managedashboard/updateWidgetData/${dashboardData.id}`;
        try {
          const response = await editById(api, dashboardConfig);
          setLoading(false);
          setNotification({
            message: `${dashboardName} updated successfully`,
            type: "success",
            show: true,
          });
          return response.data;
        } catch (error) {
          setLoading(false);
          setNotification({
            message: `${dashboardName} failed to save`,
            type: "error",
            show: true,
          });
          console.error(
            "Error creating dashboard:",
            error.response ? error.response.data : error.message
          );
          // throw error;
        }
        finally{
          setTimeout(() => {
            navigate('/newDS/dashboardManagement')
          }, 750);
        }
  };

  const createDashboard = () => {
    const dashboardConfig = {
      name: dashboardName,
      description: dashboardDescription,
      widgets: widgetOrder.map(id => ({
        id,
        type: widgetTypes[id],
        position: widgetPositions[id],
        size: widgetSizes[id]
      })),
      widgetReports:dashboardData.widgetReports,
      createdAt: new Date().toISOString()
    };
    console.log(dashboardConfig);
  }

  const downloadAsFile = () => {
    const dashboardConfig = {
      name: dashboardName,
      description: dashboardDescription,
      widgets: widgetOrder.map(id => ({
        id,
        type: widgetTypes[id],
        position: widgetPositions[id],
        size: widgetSizes[id]
        })),
      createdAt: new Date().toISOString()
    };
      
    const blob = new Blob([JSON.stringify(dashboardConfig, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${dashboardName.replace(/\s+/g, '_')}_view_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    setShowSaveModal(false);
  }

  // Handle widget deletion
  const handleDeleteWidget = (widgetId) => {
    // Check permissions before deleting
    if (!canModifyDashboard) {
      setNotification({
        message: "You do not have permission to modify this dashboard",
        type: "error",
        show: true,
      });
      return;
    }

    // Mark widget as deleting for animation
    setDeletingWidgets(prev => new Set([...prev, widgetId]));
    
    // Wait for animation, then remove widget
    setTimeout(() => {
      // Remove from widget order
      setWidgetOrder(prev => {
        const newOrder = prev.filter(id => id !== widgetId);
        // Auto-arrange remaining widgets after deletion
        setTimeout(() => {
          if (newOrder.length > 0) {
            autoArrangeWidgets(newOrder);
          }
        }, 50);
        return newOrder;
      });
      
      // Remove from widget positions
      setWidgetPositions(prev => {
        const newPositions = { ...prev };
        delete newPositions[widgetId];
        return newPositions;
      });
      
      // Remove from widget sizes
      setWidgetSizes(prev => {
        const newSizes = { ...prev };
        delete newSizes[widgetId];
        return newSizes;
      });
      
      // Remove from widget types
      setWidgetTypes(prev => {
        const newTypes = { ...prev };
        delete newTypes[widgetId];
        return newTypes;
      });
      
      // Remove from deleting set
      setDeletingWidgets(prev => {
        const newSet = new Set(prev);
        newSet.delete(widgetId);
        return newSet;
      });
    }, 300); // Match CSS transition duration
  };

  // Handle clearing all widgets
  const handleClearAllWidgets = () => {
    setWidgetOrder([]);
    setWidgetPositions({});
    setWidgetSizes({});
    setWidgetTypes({});
  };

  // Auto-arrange widgets (extracted for reuse)
  const autoArrangeWidgets = (widgetList = widgetOrder) => {
    const positions = {};
    const widgetsPerRow = 3; // 3 widgets per row
    const containerWidth = 1200; // Approximate container width
    const margin = 20; // Margin between widgets
    const widgetWidth = Math.floor((containerWidth - (margin * (widgetsPerRow + 1))) / widgetsPerRow);
    
    let currentX = margin;
    let currentY = margin;
    let widgetsInCurrentRow = 0;
    let maxHeightInRow = 0;
    
    widgetList.forEach((widgetId, index) => {
      // Get the widget type to determine default size
      const widgetType = widgetTypes[widgetId] || widgetId.split('-').slice(0, -1).join('-') || widgetId;
      const defaultSize = getDefaultSizeForType(widgetType);
      
      // Use consistent width but keep original height
      const widgetHeight = defaultSize.height;
      
      // Check if we need to move to next row
      if (widgetsInCurrentRow >= widgetsPerRow) {
        currentX = margin;
        currentY += maxHeightInRow + margin;
        widgetsInCurrentRow = 0;
        maxHeightInRow = 0;
      }
      
      // Set position
      positions[widgetId] = { 
        x: currentX, 
        y: currentY 
      };
      
      // Update widget size to match the calculated width
      setWidgetSizes(prev => ({
        ...prev,
        [widgetId]: {
          width: widgetWidth,
          height: widgetHeight
        }
      }));
      
      // Update for next widget
      currentX += widgetWidth + margin;
      widgetsInCurrentRow++;
      maxHeightInRow = Math.max(maxHeightInRow, widgetHeight);
    });
    
    setWidgetPositions(positions);
  };

  // Auto-arrange widgets in a grid (original function for button)
  const autoArrange = () => {
    autoArrangeWidgets();
  };

  // All widgets are always available for addition (allow multiple instances)
  const getAvailableNewWidgets = () => {
    return availableWidgets; // Return all widgets, allowing multiple instances
  };

  // Handle widget selection in modal - now shows report selection modal
  const handleWidgetToggle = (widgetType) => {
    const isSelected = selectedNewWidgets.includes(widgetType);
    
    if (!isSelected) {
      setCurrentWidgetForReports(widgetType);
      initializeNewWidgetReports(widgetType);
      setShowWidgetReportsModal(true);
    } else {
      setSelectedNewWidgets(prev => prev.filter(type => type !== widgetType));
      // Remove the reports when unselecting
      setNewWidgetReports(prev => {
        const newReports = {...prev};
        delete newReports[widgetType];
        return newReports;
      });
    }
  };

  // Initialize reports for a new widget when selected
  const initializeNewWidgetReports = (widgetType) => {
    if (!newWidgetReports[widgetType]) {
      setNewWidgetReports(prev => ({
        ...prev,
        [widgetType]: []
      }));
    }
  };

  // Handle report selection for new widgets
  const handleReportToggleForNewWidget = (widgetType, report) => {
    setNewWidgetReports(prev => {
      const currentReports = prev[widgetType] || [];
      const updatedReports = currentReports.includes(report)
        ? currentReports.filter(r => r !== report)
        : [...currentReports, report];
      
      return {
        ...prev,
        [widgetType]: updatedReports
      };
    });
  };

  // Confirm widget reports selection
  const handleWidgetReportsModalConfirm = () => {
    if (!selectedNewWidgets.includes(currentWidgetForReports)) {
      setSelectedNewWidgets(prev => [...prev, currentWidgetForReports]);
    }
    setShowWidgetReportsModal(false);
  };

  // Close widget reports modal
  const handleWidgetReportsModalClose = () => {
    setShowWidgetReportsModal(false);
  };

  // Validate widget reports for new widgets
  const validateNewWidgetReports = () => {
    for (const widgetType in newWidgetReports) {
      if (selectedNewWidgets.includes(widgetType) && newWidgetReports[widgetType].length < 2) {
        const widget = availableWidgets.find(w => w.id === widgetType);
        const widgetName = widget ? widget.name : widgetType;
        setNotification({
          message: `${widgetName}: must contain at least 2 reports to compare data`,
          type: "error",
          show: true,
        });
        return false;
      }
    }
    return true;
  };

  // Add selected widgets to dashboard
  const handleAddWidgets = () => {
    // Check permissions before adding widgets
    if (!canModifyDashboard) {
      setNotification({
        message: "You do not have permission to modify this dashboard",
        type: "error",
        show: true,
      });
      return;
    }

    if (selectedNewWidgets.length === 0) return;

    // Validate widget reports before adding
    if (!validateNewWidgetReports()) {
      return;
    }

    // Create unique IDs for new widgets and update widget types
    const newWidgetIds = [];
    const newWidgetTypes = { ...widgetTypes };
    
    selectedNewWidgets.forEach(widgetType => {
      const newWidgetId = generateWidgetId(widgetType);
      newWidgetIds.push(newWidgetId);
      newWidgetTypes[newWidgetId] = widgetType;
    });

    // Add to widget order
    const newWidgetOrder = [...widgetOrder, ...newWidgetIds];
    setWidgetOrder(newWidgetOrder);
    setWidgetTypes(newWidgetTypes);

    // Add to widget sizes
    const newSizes = { ...widgetSizes };
    newWidgetIds.forEach(widgetId => {
      const widgetType = newWidgetTypes[widgetId];
      newSizes[widgetId] = getDefaultSizeForType(widgetType);
    });
    setWidgetSizes(newSizes);

    // Position new widgets
    const newPositions = { ...widgetPositions };
    let currentX = 20;
    let currentY = 20;
    let maxHeightInRow = 0;
    const containerWidth = 1200;

    // Find the lowest Y position to start placing new widgets
    let maxY = 0;
    Object.values(widgetPositions).forEach(pos => {
      if (pos && pos.y) {
        const widgetId = Object.keys(widgetPositions).find(id => widgetPositions[id] === pos);
        const widgetType = widgetTypes[widgetId] || widgetId;
        const size = widgetSizes[widgetId] || getDefaultSizeForType(widgetType);
        maxY = Math.max(maxY, pos.y + size.height);
      }
    });

    currentY = maxY + 40; // Start below existing widgets with some margin

    newWidgetIds.forEach((widgetId, index) => {
      const widgetType = newWidgetTypes[widgetId];
      const defaultSize = getDefaultSizeForType(widgetType);
      
      if (currentX + defaultSize.width > containerWidth && index > 0) {
        currentX = 20;
        currentY += maxHeightInRow + 20;
        maxHeightInRow = 0;
      }
      
      newPositions[widgetId] = { x: currentX, y: currentY };
      currentX += defaultSize.width + 20;
      maxHeightInRow = Math.max(maxHeightInRow, defaultSize.height);
    });

    setWidgetPositions(newPositions);

    // Update dashboard data with new widget reports
    const updatedWidgetReports = { ...dashboardData.widgetReports };
    newWidgetIds.forEach(widgetId => {
      const widgetType = newWidgetTypes[widgetId];
      if (newWidgetReports[widgetType]) {
        updatedWidgetReports[widgetId] = newWidgetReports[widgetType];
      }
    });
    
    // Update dashboardData.widgetReports
    dashboardData.widgetReports = updatedWidgetReports;

    // Reset modal state
    setSelectedNewWidgets([]);
    setNewWidgetReports({});
    setShowAddWidgetsModal(false);

    // Show success notification
    setNotification({
      message: `Successfully added ${selectedNewWidgets.length} widget${selectedNewWidgets.length !== 1 ? 's' : ''} to dashboard`,
      type: "success",
      show: true,
    });
  };

  // Helper function to render widget header icons based on permissions
  const renderWidgetHeaderIcons = (widgetId, textColor = 'text-white', fontSize = '14px') => (
    <div className="d-flex gap-2">
      {canModifyDashboard && (
        <>
          <i className={`fa fa-expand-arrows-alt ${textColor} widget-header-icon`} title="Resize widget" style={{ fontSize }}></i>
          <i className={`fa fa-arrows ${textColor} widget-header-icon`} title="Drag to move"></i>
          <i 
            className={`fa fa-trash ${textColor} widget-delete-btn`} 
            title="Delete widget" 
            style={{ fontSize, cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteWidget(widgetId);
            }}
          ></i>
        </>
      )}
      {!canModifyDashboard && (
        <i className={`fa fa-eye ${textColor}`} title="View only" style={{ fontSize }}></i>
      )}
    </div>
  );

  // Sample data for the DataTable widget
  const sampleTableData = [
    {
      id: 1,
      documentName: 'Sample Document 1.pdf',
      type: 'PDF',
      status: 'Active',
      createdDate: '2024-01-15',
      owner: 'John Doe',
      size: '2.5 MB'
    },
    {
      id: 2,
      documentName: 'Project Report.docx',
      type: 'DOCX',
      status: 'Pending',
      createdDate: '2024-01-14',
      owner: 'Jane Smith',
      size: '1.8 MB'
    },
    {
      id: 3,
      documentName: 'Financial Analysis.xlsx',
      type: 'XLSX',
      status: 'Active',
      createdDate: '2024-01-13',
      owner: 'Mike Johnson',
      size: '3.2 MB'
    },
    {
      id: 4,
      documentName: 'Presentation.pptx',
      type: 'PPTX',
      status: 'Draft',
      createdDate: '2024-01-12',
      owner: 'Sarah Wilson',
      size: '5.1 MB'
    },
    {
      id: 5,
      documentName: 'Contract Agreement.pdf',
      type: 'PDF',
      status: 'Active',
      createdDate: '2024-01-11',
      owner: 'David Brown',
      size: '1.2 MB'
    }
  ];

  // Column configuration for DataTable
  const tableColumns = [
    {
      key: 'documentName',
      header: 'Document Name',
      sortable: true,
      width: '30%'
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      width: '10%',
      render: (value) => (
        <span className={`badge ${
          value === 'PDF' ? 'bg-danger' :
          value === 'DOCX' ? 'bg-primary' :
          value === 'XLSX' ? 'bg-success' :
          value === 'PPTX' ? 'bg-warning' : 'bg-secondary'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      width: '15%',
      render: (value) => (
        <span className={`badge ${
          value === 'Active' ? 'bg-success' :
          value === 'Pending' ? 'bg-warning' :
          value === 'Draft' ? 'bg-info' : 'bg-secondary'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'createdDate',
      header: 'Created Date',
      sortable: true,
      width: '15%'
    },
    {
      key: 'owner',
      header: 'Owner',
      sortable: true,
      width: '20%'
    },
    {
      key: 'size',
      header: 'Size',
      sortable: true,
      width: '10%'
    }
  ];

  const renderWidget = (widgetId) => {
    const widgetType = widgetTypes[widgetId] || widgetId; // Get widget type from state or fallback to ID
    const widget = availableWidgets.find(w => w.id === widgetType);
    
    // Fallback widget definition if not found
    const fallbackWidget = {
      id: widgetType,
      name: widgetType.charAt(0).toUpperCase() + widgetType.slice(1).replace('-', ' '),
      icon: 'fa-square',
      description: 'Widget'
    };
    
    const currentWidget = widget || fallbackWidget;

    const getWidgetInstanceData = (baseWidgetId) => {
      const widgetType = baseWidgetId.replace(/-\d+$/, '');
      const widgetNumber = parseInt(baseWidgetId.split('-').pop()) || 1;
      
      // Default data values
      const defaultData = {
        "Total Files": count,
        "Duplicate Files": duplicateCount,
        "Pending Workflows Files": pendingWFCount,
        "Due Date Files": dueCount,
        "Archive Files": archiveCount,
        "Retention End Files": retentionCount
      };
    
      // Get the reports for this widget type from widgetReports
      // Check for instance-specific reports first, then fall back to widget type reports
      const instanceReports = dashboardData.widgetReports[baseWidgetId] || 
                             dashboardData.widgetReports[widgetType] || [];
      
      // Check if this is a newly added widget (not from original dashboard creation)
      const isNewWidget = !dashboardData.widgetReports[widgetType] && !dashboardData.widgetReports[baseWidgetId];
      
      // If it's a new widget, return empty state indicator
      if (isNewWidget) {
        return { isEmpty: true, widgetType };
      }
      
      // If no specific reports are configured but widget was from original creation, show all data
      if (instanceReports.length === 0) {
        return defaultData;
      }
      
      // Filter the data to only include the specified reports
      const filteredData = {};
      instanceReports.forEach(report => {
        if (defaultData.hasOwnProperty(report)) {
          filteredData[report] = defaultData[report];
        }
      });
    
      return filteredData;
    };

    const widgetData = getWidgetInstanceData(widgetId);
    
    const getWidgetContent = () => {
      switch (widgetType) {
        case 'bar-chart':
          const barChartData = getWidgetInstanceData(widgetId);
          return (
            <div className="card shadow h-100 d-flex flex-column">
              <div className="card-header bg-primary text-white d-flex justify-content-between align-items-center flex-shrink-0">
                <h5 className="mb-0">
                  <i className={`fa ${currentWidget.icon} me-2`}></i>
                  {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                </h5>
                {renderWidgetHeaderIcons(widgetId)}
              </div>
              <div className="card-body flex-grow-1 p-3 d-flex align-items-center justify-content-center" style={{ minHeight: '250px', overflow: 'hidden' }}>
                {barChartData.isEmpty ? (
                  <div className="text-center text-muted">
                    <i className="fa fa-chart-bar fa-4x mb-3 text-primary opacity-50"></i>
                    <h5 className="text-muted">No Data Configured</h5>
                    <p className="mb-0">Configure data sources to see your bar chart visualization</p>
                    <small className="text-info">This widget will display comparative data once configured</small>
                  </div>
                ) : (
                  <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    <BarChart data={barChartData} counts={counts}/>
                  </div>
                )}
              </div>
            </div>
          );
        
        case 'pie-chart':
          const pieChartData = getWidgetInstanceData(widgetId);
          return (
            <div className="card shadow h-100 d-flex flex-column">
              <div className="card-header bg-success text-white d-flex justify-content-between align-items-center flex-shrink-0">
                <h5 className="mb-0">
                  <i className={`fa ${currentWidget.icon} me-2`}></i>
                  {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                </h5>
                {renderWidgetHeaderIcons(widgetId)}
              </div>
              <div className="card-body flex-grow-1 p-3 d-flex align-items-center justify-content-center" style={{ minHeight: '250px', overflow: 'hidden' }}>
                {pieChartData.isEmpty ? (
                  <div className="text-center text-muted">
                    <i className="fa fa-pie-chart fa-4x mb-3 text-success opacity-50"></i>
                    <h5 className="text-muted">No Data Configured</h5>
                    <p className="mb-0">Configure data sources to see your pie chart visualization</p>
                    <small className="text-info">This widget will display data distribution once configured</small>
                  </div>
                ) : (
                  <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    <PieChart data={pieChartData} counts={counts}/>
                  </div>
                )}
              </div>
            </div>
          );
          case 'stats-card':
            const statsData = getWidgetInstanceData(widgetId);
            
            if (statsData.isEmpty) {
              return (
                <div className="card shadow border-left-primary h-100 d-flex flex-column">
                  <div className="card-header bg-light d-flex justify-content-between align-items-center flex-shrink-0">
                    <h6 className="mb-0 text-primary">
                      <i className={`fa ${currentWidget.icon} me-2`}></i>
                      {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                    </h6>
                    {renderWidgetHeaderIcons(widgetId, 'text-muted', '12px')}
                  </div>
                  <div className="card-body flex-grow-1 d-flex align-items-center justify-content-center">
                    <div className="text-center text-muted">
                      <i className="fa fa-tachometer fa-3x mb-3 text-primary opacity-50"></i>
                      <h6 className="text-muted">No Metrics Configured</h6>
                      <p className="mb-0 small">Configure data sources to display key statistics</p>
                      <small className="text-info">This widget will show important metrics once configured</small>
                    </div>
                  </div>
                </div>
              );
            }

            const statTypes = [
              { label: 'Total Files', value: statsData['Total Files'], icon: 'fa fa-file-text', color: 'success' },
              { label: 'Pending Workflows', value: statsData['Pending Workflows Files'], icon: 'fa fa-clock-o', color: 'warning' },
              { label: 'Duplicates Found', value: statsData['Duplicate Files'], icon: 'fa fa-copy', color: 'danger' },
              { label: 'Archived Files', value: statsData['Archive Files'], icon: 'fa fa-archive', color: 'info' },
              { label: 'Due Items', value: statsData['Due Date Files'], icon: 'fa fa-calendar', color: 'primary' },
              { label: 'Retention End', value: statsData['Retention End Files'], icon: 'fa fa-hourglass-end', color: 'dark' }
            ].filter(stat => stat.value !== undefined); // Only show stats that exist in the data
          
            return (
              <div className="card shadow border-left-primary h-100 d-flex flex-column">
                <div className="card-header bg-light d-flex justify-content-between align-items-center flex-shrink-0">
                  <h6 className="mb-0 text-primary">
                    <i className={`fa ${currentWidget.icon} me-2`}></i>
                    {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                  </h6>
                  {renderWidgetHeaderIcons(widgetId, 'text-muted', '12px')}
                </div>
                {statTypes.map((stat, index) => (
                  <div key={index} className="card-body flex-grow-1 d-flex align-items-center py-1">
                    <div className="row no-gutters align-items-center w-100">
                      <div className="col mr-2">
                        <div className={`text-xs font-weight-bold text-${stat.color} text-uppercase mb-0`}>
                          {stat.label}
                        </div>
                        <div className="h5 mb-0 font-weight-bold text-gray-800">{stat.value && stat.value.toLocaleString()}</div>
                      </div>
                      <div className="col-auto">
                        <i className={`${stat.icon} fa-2x text-gray-300 text-${stat.color}`}></i>
                      </div>
                    </div>
                  </div>
                ))}             
              </div>
            );
        case 'table':
          const tableData = getWidgetInstanceData(widgetId);
          return (
            <div className="card shadow h-100 d-flex flex-column">
              <div className="card-header bg-info text-white d-flex justify-content-between align-items-center flex-shrink-0">
                <h5 className="mb-0">
                  <i className={`fa ${currentWidget.icon} me-2`}></i>
                  {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                </h5>
                {renderWidgetHeaderIcons(widgetId)}
              </div>
              <div className="card-body p-0 flex-grow-1 overflow-hidden">
                {tableData.isEmpty ? (
                  <div className="d-flex align-items-center justify-content-center h-100">
                    <div className="text-center text-muted p-4">
                      <i className="fa fa-table fa-4x mb-3 text-info opacity-50"></i>
                      <h5 className="text-muted">No Data Source Configured</h5>
                      <p className="mb-0">Configure a data source to display tabular data</p>
                      <small className="text-info">This widget will show structured data once configured</small>
                    </div>
                  </div>
                ) : (
                  <div className="p-3 pb-0 w-100 h-100 overflow-auto">
                    <DataTable
                      data={sampleTableData}
                      columns={tableColumns}
                      searchable={true}
                      itemsPerPage={5}
                      className="table-sm"
                      showSno={true}
                    />
                  </div>
                )}
              </div>
            </div>
          );

        case 'line-chart':
          const lineChartData = getWidgetInstanceData(widgetId);
          return (
            <div className="card shadow h-100 d-flex flex-column">
              <div className="card-header bg-warning text-white d-flex justify-content-between align-items-center flex-shrink-0">
                <h5 className="mb-0">
                  <i className={`fa ${currentWidget.icon} me-2`}></i>
                  {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                </h5>
                {renderWidgetHeaderIcons(widgetId)}
              </div>
              <div className="card-body flex-grow-1 p-3 d-flex align-items-center justify-content-center" style={{ minHeight: '250px', overflow: 'hidden' }}>
                {lineChartData.isEmpty ? (
                  <div className="text-center text-muted">
                    <i className="fa fa-line-chart fa-4x mb-3 text-warning opacity-50"></i>
                    <h5 className="text-muted">No Data Configured</h5>
                    <p className="mb-0">Configure data sources to see your line chart visualization</p>
                    <small className="text-info">This widget will display trend data once configured</small>
                  </div>
                ) : (
                  <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                    <LineChart data={lineChartData} counts={counts}/>
                  </div>
                )}
              </div>
            </div>
          );

        default:
          const defaultData = getWidgetInstanceData(widgetId);
          return (
            <div className="card shadow h-100 d-flex flex-column">
              <div className="card-header bg-secondary text-white d-flex justify-content-between align-items-center flex-shrink-0">
                <h5 className="mb-0">
                  <i className={`fa ${currentWidget.icon} me-2`}></i>
                  {currentWidget.name} #{widgetId.split('-').pop() || '1'}
                </h5>
                {renderWidgetHeaderIcons(widgetId)}
              </div>
              <div className="card-body text-center flex-grow-1 d-flex align-items-center justify-content-center">
                <div>
                  <i className={`fa ${currentWidget.icon} fa-3x text-muted mb-3`}></i>
                  <h5>{currentWidget.name}</h5>
                  {defaultData.isEmpty ? (
                    <div>
                      <p className="text-muted">No configuration available</p>
                      <small className="text-info">This widget is ready to use once data is configured</small>
                    </div>
                  ) : (
                    <p className="text-muted">Widget coming soon</p>
                  )}
                </div>
              </div>
            </div>
          );
      }
    };

    return (
      <SortableWidget 
        key={widgetId} 
        id={widgetId} 
        widgetType={widgetType}
        position={widgetPositions[widgetId]}
        onPositionChange={handlePositionChange}
        onSizeChange={updateWidgetSize}
        size={widgetSizes[widgetId]}
        isDeleting={deletingWidgets.has(widgetId)}
        canModify={canModifyDashboard}
      >
        {getWidgetContent()}
      </SortableWidget>
    );
  };

  return (
    <div className="container-fluid mt-4">
      <style jsx>{`
        .resize-handle-right:hover,
        .resize-handle-bottom:hover {
          opacity: 0.8 !important;
          background-color: #0056b3 !important;
        }
        .resize-handle-corner:hover {
          opacity: 1 !important;
          transform: scale(1.2);
        }
        .resize-handle-corner-main:hover {
          opacity: 1 !important;
          transform: scale(1.1);
          background-color: #0056b3 !important;
        }
        .resize-handle-right {
          cursor: ew-resize !important;
          transition: all 0.2s ease;
        }
        .resize-handle-bottom {
          cursor: ns-resize !important;
          transition: all 0.2s ease;
        }
        .resize-handle-corner {
          cursor: nw-resize !important;
          transition: all 0.2s ease;
        }
        .resize-handle-corner-main {
          cursor: nw-resize !important;
          transition: all 0.2s ease;
          position: relative;
        }
        .resize-handle-corner-main::after {
          content: '';
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 0 0 8px 8px;
          border-color: transparent transparent rgba(255,255,255,0.8) transparent;
        }
        .dashboard-canvas {
          min-height: 900px;
          background: linear-gradient(90deg, #f8f9fa 1px, transparent 1px),
                      linear-gradient(#f8f9fa 1px, transparent 1px);
          background-size: 20px 20px;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          position: relative;
          overflow: visible;
        }
        /* Widget container improvements */
        .widget-container {
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          border-radius: 8px;
          background: white;
          overflow: hidden;
        }
        .widget-container:hover {
          box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        /* Chart container improvements */
        .widget-container .card-body {
          padding: 12px !important;
        }
        .widget-container .card {
          border: none;
          box-shadow: none;
        }
        /* Widget resize hint */
        .widget-container:hover .resize-handle-corner-main {
          animation: pulse-resize 2s infinite;
        }
        @keyframes pulse-resize {
          0%, 100% { opacity: 0.8; }
          50% { opacity: 1; }
        }
        /* Ensure charts don't overflow */
        .chart-container {
          width: 100% !important;
          height: 100% !important;
          position: relative !important;
          overflow: hidden !important;
        }
        .chart-container canvas {
          max-width: 100% !important;
          max-height: 100% !important;
        }
        /* Widget delete button styling */
        .widget-delete-btn {
          transition: all 0.2s ease;
          opacity: 0.7;
        }
        .widget-delete-btn:hover {
          opacity: 1 !important;
          transform: scale(1.2);
          color: #ff4444 !important;
          text-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
        }
        /* Widget header icons styling */
        .widget-header-icon {
          transition: all 0.2s ease;
          opacity: 0.8;
        }
        .widget-header-icon:hover {
          opacity: 1 !important;
          transform: scale(1.1);
        }
        /* Widget deletion animation */
        .widget-deleting {
          opacity: 0;
          transform: scale(0.95);
          transition: all 0.3s ease;
        }
        .widget-container {
          transition: all 0.3s ease;
        }
      `}</style>
      
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div>
          <h2 className="text-primary mb-1">
            <i className="fa fa-dashboard me-2"></i>
            {dashboardName}
          </h2>
          {dashboardDescription && (
            <p className="text-muted mb-0">{dashboardDescription}</p>
          )}
          <div className="d-flex align-items-center gap-3">
            <small className="text-info">
              <i className="fa fa-info-circle me-1"></i>
              {canModifyDashboard ? 'Drag widgets to move • Drag corners/edges to resize' : 'View-only mode • You can only view this dashboard'}
            </small>
            {!isAdmin && (
              <span className="badge bg-warning small">
                <i className="fa fa-eye me-1"></i>
                Assigned Dashboard
              </span>
            )}
          </div>
        </div>
        <div className="d-flex gap-2">
          {/* Save/Update button - only for admin users who can modify */}
          {canModifyDashboard && widgetOrder.length > 0 && (
            <button 
              className="btn btn-outline-success btn-sm"
              onClick={dashboardData.id ? updateDashboardView : saveDashboardView}
              title="Save dashboard view"
            >
              <i className="fa fa-save me-2"></i>
              {dashboardData.id ? "Update Dashboard" : "Save Dashboard" }
            </button>  
          )}
          
          {/* Auto-arrange button - only for admin users who can modify */}
          {canModifyDashboard && (
            <button 
              className="btn btn-outline-secondary btn-sm"
              onClick={autoArrange}
              title="Auto-arrange widgets in 3 columns"
            >
              <i className="fa fa-th-large me-2"></i>
              Auto Arrange (3 Columns)
            </button>
          )}
          
          {/* Add Widgets button - only for admin users who can modify */}
          {canModifyDashboard && (
            <button 
              className="btn btn-success"
              onClick={() => setShowAddWidgetsModal(true)}
            >
              <i className="fa fa-plus me-2"></i>
              Add Widgets
            </button>
          )}
          
          {/* New Dashboard button - only for admin users */}
          {isAdmin && (
            <button 
              className="btn btn-outline-primary"
              onClick={() => navigate('/newDS/createDashboard')}
              title="Create a new dashboard from scratch"
            >
              <i className="fa fa-plus me-2"></i>
              New Dashboard
            </button>
          )}
          
          {/* Dashboard Home button - always visible */}
          <button 
            className="btn btn-secondary"
            onClick={() => navigate('/newDS/home')}
            title="Return to main dashboard home"
          >
            <i className="fa fa-home me-2"></i>
            Dashboard Home
          </button>
          
          {/* Clear All button - only for admin users who can modify */}
          {canModifyDashboard && widgetOrder.length > 0 && (
            <button 
              className="btn btn-danger"
              onClick={handleClearAllWidgets}
              title="Delete all widgets"
            >
              <i className="fa fa-trash me-2"></i>
              Clear All
            </button>
          )}
          
          <CustomBreadcrumb
            companyName={localStorage.getItem("companyName") || "Company"}
            featureName={dashboardName}
          />
        </div>
      </div>

      {/* Add Widgets Modal */}
      {showAddWidgetsModal && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  <i className="fa fa-plus me-2"></i>
                  Add Widgets to Dashboard
                </h5>
                <button 
                  type="button" 
                  className="btn-close" 
                  onClick={() => {
                    setShowAddWidgetsModal(false);
                    setSelectedNewWidgets([]);
                  }}
                ></button>
              </div>
              <div className="modal-body">
                <p className="mb-3">Select the widgets you want to add to your dashboard:</p>
                <div className="alert alert-info">
                  <i className="fa fa-info-circle me-2"></i>
                  Click on a widget to configure its data sources. You can add multiple instances of the same widget type.
                </div>
                <div className="row">
                  {getAvailableNewWidgets().map((widget) => (
                    <div key={widget.id} className="col-md-4 mb-3">
                      <div 
                        className={`card h-100 widget-card ${selectedNewWidgets.includes(widget.id) ? 'border-primary bg-primary bg-opacity-10' : 'border-light'}`}
                        style={{ cursor: 'pointer', transition: 'all 0.2s', minWidth: 0, maxWidth: '260px', margin: '0 auto' }}
                        onClick={() => handleWidgetToggle(widget.id)}
                      >
                        <div className="card-body text-center p-2" style={{ minHeight: '120px' }}>
                          <div className="form-check d-flex align-items-center justify-content-center mb-2">
                            <input
                              className="form-check-input me-2"
                              type="checkbox"
                              checked={selectedNewWidgets.includes(widget.id)}
                              onChange={() => handleWidgetToggle(widget.id)}
                              onClick={(e) => e.stopPropagation()}
                              style={{ transform: 'scale(1.1)' }}
                            />
                          </div>
                          <i className={`fa ${widget.icon} fa-2x text-primary mb-2`}></i>
                          <h6 className="card-title mb-1" style={{ fontSize: '1rem' }}>{widget.name}</h6>
                          <p className="card-text text-muted small mb-1" style={{ fontSize: '0.85rem' }}>{widget.description}</p>
                          {selectedNewWidgets.includes(widget.id) && newWidgetReports[widget.id] && (
                            <div className="mt-1">
                              <small className="text-muted">
                                Reports: {newWidgetReports[widget.id].length > 0 ? newWidgetReports[widget.id].join(', ') : 'None selected'}
                              </small>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Selected Widgets Summary */}
                {selectedNewWidgets.length > 0 && (
                  <div className="mt-4">
                    <h6>Selected Widgets ({selectedNewWidgets.length}):</h6>
                    <div className="d-flex flex-wrap gap-2">
                      {selectedNewWidgets.map(widgetType => {
                        const widget = availableWidgets.find(w => w.id === widgetType);
                        if (!widget) return null;
                        return (
                          <span key={widgetType} className="badge bg-primary">
                            <i className={`fa ${widget.icon} me-1`}></i>
                            {widget.name}
                            {newWidgetReports[widgetType]?.length > 0 && (
                              <span className="ms-1">({newWidgetReports[widgetType].length} reports)</span>
                            )}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowAddWidgetsModal(false);
                    setSelectedNewWidgets([]);
                  }}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={handleAddWidgets}
                  disabled={selectedNewWidgets.length === 0}
                >
                  <i className="fa fa-plus me-2"></i>
                  Add {selectedNewWidgets.length} Widget{selectedNewWidgets.length !== 1 ? 's' : ''}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Widget Reports Selection Modal */}
      <Modal show={showWidgetReportsModal} onHide={handleWidgetReportsModalClose} centered>
        <Modal.Header closeButton>
          <Modal.Title style={{ fontSize: '1.05rem', fontWeight: 500 }}>
            Reports to display in this widget (Select at least 2 items)
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentWidgetForReports && (
            <div>
              <p>Select reports to display in this widget:</p>
              <div className="list-group">
                {reportList.map(report => (
                  <div key={report} className="list-group-item">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={newWidgetReports[currentWidgetForReports]?.includes(report) || false}
                        onChange={() => handleReportToggleForNewWidget(currentWidgetForReports, report)}
                        id={`report-new-${currentWidgetForReports}-${report}`}
                      />
                      <label 
                        className="form-check-label" 
                        htmlFor={`report-new-${currentWidgetForReports}-${report}`}
                      >
                        {report}
                      </label>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleWidgetReportsModalClose}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleWidgetReportsModalConfirm}
            disabled={
              !(
                currentWidgetForReports &&
                newWidgetReports[currentWidgetForReports] &&
                newWidgetReports[currentWidgetForReports].length >= 2
              )
            }
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>

      {showSaveModal && (
        <div className="modal fade show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header" 
                style={{background:"linear-gradient(90deg, #4f8cff 60%, #6fc3ff 100%)",color:"#fff !important",borderRadius:"12px 12px 0 0",fontSize:"1.1rem",fontWeight:"600",letterSpacing:"0.01rem"}}
              b>
                <h5 className="modal-title text-white">
                  <i className="fa fa-save me-2"></i>
                  Dashboard View Configuration Preview
                </h5>
                <button 
                  type="button" 
                  className="btn-close" 
                  onClick={() => setShowSaveModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <h5>{dashboardName}</h5>
                  {dashboardDescription && <p className="text-muted">{dashboardDescription}</p>}
                  <hr />
                  <h6>Widget Configuration</h6>
                  <div className="table-responsive">
                    <table className="table table-sm">
                      <thead>
                        <tr>
                          <th>Widget</th>
                          <th>Type</th>
                          <th>Position</th>
                          <th>Size</th>
                        </tr>
                      </thead>
                      <tbody>
                        {widgetOrder.map(id => (
                          <tr key={id}>
                            <td>{id}</td>
                            <td>{widgetTypes[id]}</td>
                            <td>
                              X: {widgetPositions[id]?.x || 0}, 
                              Y: {widgetPositions[id]?.y || 0}
                            </td>
                            <td>
                              W: {widgetSizes[id]?.width || 0}, 
                              H: {widgetSizes[id]?.height || 0}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
                <div className="alert alert-info">
                  <i className="fa fa-info-circle me-2"></i>
                  This is a preview of your dashboard configuration. You can copy this data or save it as a file.
                </div>
                <pre className="bg-light p-3 rounded" style={{ maxHeight: '200px', overflow: 'auto' }}>
                  {JSON.stringify({
                    name: dashboardName,
                    description: dashboardDescription,
                    widgets: widgetOrder.map(id => ({
                      id,
                      type: widgetTypes[id],
                      position: widgetPositions[id],
                      size: widgetSizes[id]
                    })),
                    createdAt: new Date().toISOString()
                  }, null, 2)}
                </pre>
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => setShowSaveModal(false)}
                >
                  Close
                </button>
                <button 
                  type="button" 
                  className="btn btn-success"
                  onClick={createDashboard}
                >
                  Save Dashboard
                </button>
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={downloadAsFile}>
                  <i className="fa fa-download me-2"></i>
                  Download as File
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


      {widgetOrder.length > 0 ? (
        <DndContext
          sensors={sensors}
          onDragEnd={handleDragEnd}
        >
          <div className="dashboard-canvas">
            <SortableContext items={widgetOrder} strategy={rectSortingStrategy}>
              {widgetOrder.map((widgetId) => renderWidget(widgetId))}
            </SortableContext>
          </div>
        </DndContext>
      ) : (
        <div className="text-center py-5">
          <i className="fa fa-chart-bar fa-4x text-muted mb-3"></i>
          <h4 className="text-muted">No Widgets Selected</h4>
          <p className="text-muted">Add some widgets to see your dashboard in action.</p>
          <button 
            className="btn btn-primary"
            onClick={() => setShowAddWidgetsModal(true)}
          >
            <i className="fa fa-plus me-2"></i>
            Add Widgets
          </button>
        </div>
      )}
      {loading && <Loader />}
      {notification.show && 
        <Notification 
          message={notification.message} 
          type={notification.type} 
          show={notification.show} 
          onClose={closeNotification}
        />
      }
    </div>
  );
}

export default DashboardView; 