import React, { useState, useCallback, useMemo, useEffect, forwardRef, useImperativeHandle } from 'react';
import ReactFlow, {
  addEdge,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
  applyEdgeChanges,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Modal, Button, Form, Alert, Spinner } from 'react-bootstrap';
import { getList, downloadeDocument } from '../../services/apiService';

import CustomNode from './CustomNode';
import Toolbar from './Toolbar';
import UserAssignmentPanel from './UserAssignmentPanel';
import { createDiagramNode, createDiagramEdge, NODE_TYPES, updateNodeConnectionTypes } from './types';
import { 
  createWorkflow, 
  getWorkflows, 
  getWorkflowById, 
  updateVisibleMode,
  updateWorkflow, 
  deleteWorkflow,
  convertDiagramToWorkflow,
  convertWorkflowToDiagram
} from '../../services/workflowService';
import { uploadDocument } from '../../services/apiService';

// Define custom node types for React Flow
const nodeTypes = {
  process: CustomNode,
  sub_process: CustomNode,
  start: CustomNode,
  end: CustomNode,
  custom: CustomNode,
  approval: CustomNode,
  esign: CustomNode,
  acknowledge: CustomNode,
};

const DiagramEditor = forwardRef(({ embedded = false, workflowId = null, onWorkflowSaved = null,onModalClosed, workflowType: initialWorkflowType = 'approve',closeModal, onSave = null, onCancel = null, initialWorkflowData  }, ref) => {
  // React Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges] = useEdgesState([]);

  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [selectedPrivacy, setSelectedPrivacy] = useState('private');
  const [locked, setLocked] = useState(false);
  const [isOwner, setIsOwner] = useState(true); // Track if current user is the workflow owner
  
  // UI state
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedNodes, setSelectedNodes] = useState([]);
  const [isPanelVisible, setIsPanelVisible] = useState(false);
  
  // Copy/Paste state
  const [copiedNode, setCopiedNode] = useState(null);
  const [copiedNodes, setCopiedNodes] = useState([]);
  
  // Workflow management state
  const [workflows, setWorkflows] = useState([]);
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [selectedWorkflowId, setSelectedWorkflowId] = useState(null);
  const [wfId,setwfId] = useState(null);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [workflowName, setWorkflowName] = useState('');
  const [workflowType, setWorkflowType] = useState(initialWorkflowType);
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', variant: 'info' });
  const [isWorkflowModified, setIsWorkflowModified] = useState(false);
  
  // Users data from API
  const [availableUsers, setAvailableUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(true);

  // Suppress ResizeObserver warnings (common with React Flow)
  useEffect(() => {
    const resizeObserverErrDiv = document.getElementById('webpack-dev-server-client-overlay-div');
    const resizeObserverErr = document.getElementById('webpack-dev-server-client-overlay');
    if (resizeObserverErr) {
      resizeObserverErr.setAttribute('style', 'display: none');
    }
    if (resizeObserverErrDiv) {
      resizeObserverErrDiv.setAttribute('style', 'display: none');
    }
    
    // Suppress ResizeObserver loop warnings
    const originalError = console.error;
    console.error = (...args) => {
      if (args[0]?.includes?.('ResizeObserver loop completed with undelivered notifications') || 
          args[0]?.includes?.('ResizeObserver loop limit exceeded')) {
        return;
      }
      originalError.apply(console, args);
    };
    
    return () => {
      console.error = originalError;
    };
  }, []);

  // Load workflows and users on component mount
  useEffect(() => {
    loadWorkflows();
    loadUsers();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (initialWorkflowData) {
      console.log('Initial workflow data detected:', initialWorkflowData);
      loadWorkflow(initialWorkflowData.workflow_type, initialWorkflowData.id);
    }
  }, [initialWorkflowData]);

  // Load specific workflow if workflowId is provided (only when explicitly passed as prop)
  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowType, workflowId);
    }
  }, [workflowType, workflowId]); // eslint-disable-line react-hooks/exhaustive-deps

  // Show alert helper
  const showAlert = (message, variant = 'info') => {
    setAlert({ show: true, message, variant });
    setTimeout(() => setAlert({ show: false, message: '', variant: 'info' }), 5000);
  };

  // Check if current user is the workflow owner
  const checkWorkflowOwnership = (workflowData) => {
    const currentUserId = localStorage.getItem("id");
    const currentUserName = localStorage.getItem("userName");
    const currentFirstName = localStorage.getItem("firstName");
    
    // Check multiple possible creator fields for backward compatibility
    const workflowCreatorId = workflowData.createdById;
    const workflowCreatorName = workflowData.createdBy;
    
    console.log('Checking workflow ownership:', {
      currentUserId,
      currentUserName,
      currentFirstName,
      workflowCreatorId,
      workflowCreatorName,
      workflowName: workflowData.name
    });
    
    // If we have creator ID, use that for comparison (most reliable)
    if (workflowCreatorId && currentUserId) {
      return workflowCreatorId.toString() === currentUserId.toString();
    }
    
    // Fallback to name comparison if ID is not available
    if (workflowCreatorName) {
      return workflowCreatorName === currentUserName || workflowCreatorName === currentFirstName;
    }
    
    // If no creator information is available, assume ownership (for backward compatibility)
    console.log('No creator information found, assuming ownership for backward compatibility');
    return true;
  };

  // Load all users from API (following AuditLog.js pattern)
  const loadUsers = async (page = 0, size = 100, search = '') => {
    const api = `/user/list?page=${page}&size=${size}&search=${search}&sort=`;
    try {
      setUsersLoading(true);
      console.log('Loading users with params:', { page, size, search });
      
      const response = await getList(api);
      const data = response.data;
      
      console.log('Users API Response:', data);
      
      if (data && data.content) {
        // Transform the API response to match the expected format
        const transformedUsers = data.content.map(user => {
          // Handle different possible field names from API (same as AuditLog.js structure)
          const userId = user.id?.toString() || user.userId?.toString();
          const firstName = user.firstName || '';
          const lastName = user.lastName || '';
          const userName = user.userName || '';
          const userEmail = user.email || userName;
          const userRole = user.roleName || user.role || 'user';
          
          return {
            id: userId,
            name: firstName + (lastName ? ' ' + lastName : '') || userName || 'Unknown User',
            email: userEmail,
            role: userRole
          };
        }).filter(user => user.id && user.name !== 'Unknown User'); // Filter out invalid users
        
        setAvailableUsers(transformedUsers);
        console.log('Loaded users:', transformedUsers.length, transformedUsers);
      } else {
        console.log('No users content in response');
        setAvailableUsers([]);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      showAlert('Failed to load users', 'warning');
      // Fallback to empty array if API fails
      setAvailableUsers([]);
    } finally {
      setUsersLoading(false);
    }
  };

  const handleWorkflowClick = (e,workflow) => {
    e.stopPropagation();    
    sessionStorage.setItem("wfName",workflow.name);
    console.log('Loading workflow:', workflow.workflow_type, workflow.id);
    loadWorkflow(workflow.workflow_type, workflow.id);
  }

  const handleWorkflowKeyDown = (e, workflow) => {
    if (e.key === 'Delete' && workflow.id) {
      e.stopPropagation();
      e.preventDefault();
      setShowDeleteModal(true);
      setwfId(workflow.id);
      //handleDeleteWorkflow(workflow.id);
    }
  };

  const handlePrivacyChange = (privacyType,workflow) => {
    setSelectedPrivacy(privacyType);
    setCurrentWorkflow(workflow);
    setShowPrivacyModal(true);
  };

  const handleWorkflowDuplicate = (workflow) => {
    setCurrentWorkflow(workflow);
    setShowDuplicateModal(true);
  };
   
  const confirmPrivacyChange= async () => {
    console.log(currentWorkflow)
    console.log(`Workflow made as ${selectedPrivacy}`);
    const response = await updateVisibleMode(currentWorkflow.id,selectedPrivacy);
    loadWorkflows();
    setShowPrivacyModal(false);
  };

  const makeDuplicateWorkflow = async () => {
    const response = await getWorkflowById(currentWorkflow.workflow_type,currentWorkflow.id);
    const formattedApprovalLevels = response.data.approvalLevelDTO.map(level => ({
      name: level.name || "", 
      usersId: level.usersId ? String(level.usersId) : 0 ,
      connectionType: (level.rootLevelType === null ? "Mixed" : level.rootLevelType),
      label:"Process Node",
      level:level.level,
      rootLevel:level.rootLevel,
      rootLevelType:level.rootLevelType
    }));
    const workflowData={
      company:{companyId:localStorage.getItem("companyId")},
      name:currentWorkflow.name+"(copy)",
      type:currentWorkflow.type,
      workflow_type:currentWorkflow.workflow_type,
      approvalLevelDTO:formattedApprovalLevels
    }
    console.log(workflowData);
    const resp = await createWorkflow(workflowData);
    if(resp.status === "success"){
      showAlert(`${currentWorkflow.name} duplicated successfully`)
      setShowDuplicateModal(false);
      await loadWorkflows();
    }
  }

  // Refresh users function (specifically for UI refresh buttons)
  const refreshUsers = () => {
    loadUsers(0, 100, '');
  };

  // Refresh workflows function
  const refreshWorkflows = () => {
    loadWorkflows();
  };

  // Check if there's at least one process node
  const hasProcessNode = useMemo(() => {
    return nodes.some(node => node.type === 'process');
  }, [nodes]);

  // Check if there are parallel connections
  const hasParallelNodes = useMemo(() => {
    return edges.some(edge => edge.sourceHandle === 'output-right');
  }, [edges]);

  // Load all workflows
  const loadWorkflows = async () => {
    try {
      setLoading(true);
      // Fetch all workflows by using a large size parameter
      const response = await getWorkflows('', 0, 1000);
      console.log('Workflows API response:', response);
      if (response.status === 'success') {
        const workflowsData = response.data.content || [];
        console.log(`Loaded ${workflowsData.length} workflows:`, workflowsData);
        setWorkflows(workflowsData);
        
        // Show success message if workflows were loaded
        if (workflowsData.length > 0) {
          console.log(`Successfully loaded ${workflowsData.length} workflows`);
        } else {
          console.log('No workflows found');
        }
      }
    } catch (error) {
      console.error('Error loading workflows:', error);
      showAlert('Failed to load workflows', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Save current workflow
  const saveWorkflow = async () => {
    if (!workflowName.trim()) {
      showAlert('Please enter a workflow name', 'warning');
      return;
    }

    // Validation: At least one process node with a user assigned
    // const hasProcessWithUser = nodes.some(
    //   (node) =>
    //     node.type === 'process' &&
    //     Array.isArray(node.data?.assignedUsers) &&
    //     node.data.assignedUsers.length > 0
    // );
    // if (!hasProcessWithUser) {
    //   showAlert('You must assign at least one user to a process node before saving the workflow.', 'danger');
    //   return;
    // }

    try {
      setLoading(true);
      
      // Log nodes for debugging decision node preservation
      console.log('Saving workflow with nodes:', nodes.map(node => ({ 
        id: node.id, 
        type: node.type, 
        label: node.data?.label,
        assignedUsers: node.data?.assignedUsers?.length || 0
      })));
      
      const workflowData = convertDiagramToWorkflow(nodes, edges, workflowName, workflowType);
      console.log('Converted workflow data for saving:', workflowData);
      
      let response;
      if (currentWorkflow) {
        // Update existing workflow
        const updateData = {
          ...workflowData,
          id: currentWorkflow.id,
          createdAt: currentWorkflow.createdAt,
          approvalLevelDTO: workflowData.approvalLevelDTO.map((level, index) => {
            const existingLevel = currentWorkflow.approvalLevelDTO?.[index];
            return {
              ...level,
              id: existingLevel?.id,
              // match: null,
              // company: { companyId: localStorage.getItem('companyId') },
              // userType: null
            };
          })
        };
        console.log('Updating workflow with data:', updateData);
        const validate={
          id:updateData.id,
          name:updateData.name,
          workflow_type:updateData.workflow_type
        }
        const result=await validateWorkFlow(validate);
        if(result){
          showAlert('Workflow name already exists', 'danger');
          console.log("already exist in update")
          return;
        }
        response = await updateWorkflow(currentWorkflow.id, updateData);
      } else {
        // Create new workflow
        console.log('Creating new workflow with data:', workflowData);
        const validate={
          id:null,
          name:workflowData.name,
          workflow_type:workflowData.workflow_type
        }
        const result=await validateWorkFlow(validate);
        if(result){
          showAlert('Workflow name already exists', 'danger');
          console.log("already exists in create");
          return;
        }
        response = await createWorkflow(workflowData);
      }

      if (response.status === 'success') {
        setCurrentWorkflow(response.data);
        currentWorkflow ?  showAlert('Workflow Updated successfully', 'success') : showAlert('Workflow Created successfully', 'success'); 
        setShowSaveModal(false);
        setIsWorkflowModified(false);
        
        // Refetch workflows to include the newly saved workflow
        console.log('Refetching workflows after successful save...');
        await loadWorkflows();
        
        // Call the callback if provided (for parent component notification)
        if (onWorkflowSaved) {
          onWorkflowSaved();
        }
      }
    } catch (error) {
      console.error('Error saving workflow:', error);
      showAlert('Failed to save workflow', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    saveWorkflow: () => {
      // Trigger the save modal
      setShowSaveModal(true);
    },
    cancelWorkflow: () => {
      // Use the new cancel handler
      handleCancel();
    }
  }));

  const validateWorkFlow = async (data) => {
    const api=`/approvalworkflow/validateWorkflow`
    try{
      const res = await getList(api,data);
      return res.data;
    }
    catch(e){
      console.log(e)
    }
  }

  // Auto-layout function for parallel nodes
  const autoLayoutParallelNodes = useCallback((updatedNodes, updatedEdges) => {
    const nodeMap = new Map(updatedNodes.map(node => [node.id, { ...node }]));
    
    // Find all parallel connections (right output connections)
    const parallelConnections = updatedEdges.filter(edge => edge.sourceHandle === 'output-right');
    
    // Group parallel nodes by their source
    const parallelGroups = new Map();
    parallelConnections.forEach(edge => {
      if (!parallelGroups.has(edge.source)) {
        parallelGroups.set(edge.source, []);
      }
      parallelGroups.get(edge.source).push(edge.target);
    });
    
    // Layout parallel nodes horizontally to the right
    parallelGroups.forEach((targetIds, sourceId) => {
      const sourceNode = nodeMap.get(sourceId);
      if (!sourceNode || targetIds.length === 0) return;
      
      // Calculate positioning for parallel nodes to the right
      const nodeWidth = 200; // Approximate node width
      const horizontalOffset = 300; // Distance to the right of source node
      const verticalSpacing = 120; // Space between parallel nodes vertically
      
      // For single parallel node, place it directly to the right at same Y level
      if (targetIds.length === 1) {
        const targetNode = nodeMap.get(targetIds[0]);
        if (targetNode) {
          targetNode.position = {
            x: sourceNode.position.x + horizontalOffset,
            y: sourceNode.position.y // Same Y level as source
          };
        }
      } else {
        // For multiple parallel nodes, arrange them vertically to the right
        const totalHeight = (targetIds.length - 1) * verticalSpacing;
        const startY = sourceNode.position.y - (totalHeight / 2);
        
        targetIds.forEach((targetId, index) => {
          const targetNode = nodeMap.get(targetId);
          if (targetNode) {
            targetNode.position = {
              x: sourceNode.position.x + horizontalOffset,
              y: startY + (index * verticalSpacing)
            };
          }
        });
      }
    });
    
    return Array.from(nodeMap.values());
  }, []);

  // Load workflow by ID
  const loadWorkflow = async (workflowType, workflowId) => {
    console.log("Loading workflow with type:", workflowType, "and ID:", workflowId);
    
    try {
      setLoading(true);
      console.log("Loading workflow with type:", workflowType, "and ID:", workflowId);
      const response = await getWorkflowById(workflowType, workflowId);
      
      if (response.status === 'success') {
        const workflowData = response.data;
        console.log('Loaded workflow data:', workflowData);
        
        const diagramData = convertWorkflowToDiagram(workflowData);
        console.log('Converted diagram data:', diagramData);
        
        // Log node types for debugging decision node preservation
        if (diagramData.nodes) {
          const nodeTypes = diagramData.nodes.map(node => ({ id: node.id, type: node.type, label: node.data?.label, docList:node?.docList }));
          console.log('Loaded node types:', nodeTypes);
        }
        
        // Apply auto-layout to loaded workflow if it has parallel connections
        const hasParallelConnections = diagramData.edges.some(edge => edge.sourceHandle === 'output-right');
        let finalNodes = diagramData.nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            onUpdateNode: handleUpdateNode // Ensure all loaded nodes have the callback
          }
        }));
        
        if (hasParallelConnections) {
          console.log('Applying auto-layout to loaded workflow with parallel connections');
          finalNodes = autoLayoutParallelNodes(finalNodes, diagramData.edges);
        }
        
        // Check ownership and set lock state
        const userIsOwner = checkWorkflowOwnership(workflowData);
        setIsOwner(userIsOwner);
        
        // Lock the diagram if user is not the owner
        if (!userIsOwner) {
          setLocked(true);
          showAlert(`Workflow loaded in read-only mode. You are not the creator of this workflow.`, 'warning');
        } else {
          setLocked(false);
          showAlert(`Loaded workflow: ${workflowData.name}`, 'success');
        }
        
        setNodes(finalNodes);
        setEdges(diagramData.edges);
        setCurrentWorkflow(workflowData);
        setSelectedWorkflowId(workflowId);
        setWorkflowName(workflowData.name);
        setWorkflowType(workflowData.workflow_type || workflowType);
        setIsWorkflowModified(false);
        setShowLoadModal(false);
      }
    } catch (error) {
      console.error('Error loading workflow:', error);
      showAlert('Failed to load workflow', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Delete workflow
  const handleDeleteWorkflow = async (workflowId) => {
    try {
      setLoading(true);
      const response = await deleteWorkflow(workflowId);
      if (response.status === 'success') {
        showAlert(response.message, 'success');
        await loadWorkflows();
        
        // Clear current workflow if it was deleted
        if (currentWorkflow && currentWorkflow.id === workflowId) {
          setCurrentWorkflow(null);
          setNodes([]);
          setEdges([]);
          setWorkflowName('');
          setIsWorkflowModified(false);
        }
        
        // Always close the modal and clear the workflow ID after successful deletion
        setShowDeleteModal(false);
        setwfId(null);
      } else {
        // Close modal even if deletion failed (after showing error)
        setShowDeleteModal(false);
        setwfId(null);
      }
    } catch (error) {
      showAlert('Failed to delete workflow', 'danger');
      // Close modal even if there was an error
      setShowDeleteModal(false);
      setwfId(null);
    } finally {
      setLoading(false);
    }
  };

  // Create new workflow
  const createNewWorkflow = () => {
    if (isWorkflowModified && !window.confirm('You have unsaved changes. Are you sure you want to create a new workflow?')) {
      return;
    }
    
    setCurrentWorkflow(null);
    setNodes([]);
    setEdges([]);
    setWorkflowName('');
    setWorkflowType('approve');
    setIsWorkflowModified(false);
  };

  const [showCancelConfirm, setShowCancelConfirm] = useState(false);


  const handleCancel = () => {
    setShowCancelConfirm(true);
  };

  const confirmCancel = () => {
    // Clear all workflow data
    setCurrentWorkflow(null);
    setSelectedWorkflowId(null);
    setNodes([]);
    setEdges([]);
    setWorkflowName('');
    setWorkflowType(initialWorkflowType);
    setIsWorkflowModified(false);
    
    // Clear UI state
    setSelectedNode(null);
    setIsPanelVisible(false);
    setCopiedNode(null);
    setShowSaveModal(false);
    setShowLoadModal(false);
    
    // Clear alerts
    setAlert({ show: false, message: '', variant: 'info' });
    
    // Close the confirmation modal
    setShowCancelConfirm(false);
    
    // Call parent cancel callback if provided
    if (onCancel) {
      onCancel();
    }
  };

  // Handle adding new nodes
  const handleAddNode = useCallback((nodeType, position, additionalData = {}) => {
    if (locked) {
      showAlert('Cannot add nodes when diagram is locked', 'warning');
      return;
    }
    
    const newNode = createDiagramNode({
      type: nodeType,
      position,
      label: `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} Node`,
      assignedUsers: [],
      levelStatus: false, // Default to false for new nodes
    });

    // Convert to React Flow format
    const reactFlowNode = {
      id: newNode.id,
      type: nodeType,
      position: newNode.position,
      data: {
        ...newNode.data,
        // Note: callbacks will be added via useEffect
      },
    };

    setNodes((nds) => {
      const updatedNodes = nds.concat(reactFlowNode);
      // Update connection types after adding node
      return updateNodeConnectionTypes(updatedNodes, edges);
    });
    setIsWorkflowModified(true);
  }, [setNodes, edges, locked]);


  // Handle edge connections
  const onConnect = useCallback(
    (params) => {
      if (locked) {
        showAlert('Cannot create connections when diagram is locked', 'warning');
        return;
      }
      
      // Prevent self-connections (node connecting to itself)
      if (params.source === params.target) {
        showAlert('Invalid connection: A node cannot connect to itself', 'warning');
        return;
      }

      // Validate connection based on workflow rules
      const sourceNode = nodes.find(n => n.id === params.source);
      const targetNode = nodes.find(n => n.id === params.target);
      
      if (!sourceNode || !targetNode) {
        showAlert('Invalid connection: Source or target node not found', 'warning');
        return;
      }

      // Apply workflow connection rules
      let connectionType = 'series'; // default
      let connectionLabel = '';
      
      if (params.sourceHandle === 'output-right') {
        connectionType = 'parallel';
        connectionLabel = 'Parallel (∥)';
      } else if (params.sourceHandle === 'output-bottom') {
        connectionType = 'series';
        connectionLabel = 'Serial (→)';
      }

      const newEdge = createDiagramEdge({
        id: `edge_${params.source}_${params.target}`,
        source: params.source,
        target: params.target,
        sourceHandle: params.sourceHandle,
        targetHandle: params.targetHandle,
        label: connectionLabel,
        data: {
          connectionType: connectionType,
          workflowRule: params.sourceHandle === 'output-right' ? 
            'Parallel execution - connects to right output' : 
            'Serial execution - connects to bottom output'
        },
        style: {
          stroke: params.sourceHandle === 'output-right' ? '#ff9800' : '#4caf50',
          strokeWidth: 2,
          strokeDasharray: params.sourceHandle === 'output-right' ? '5,5' : 'none'
        }
      });

      setEdges((eds) => {
        const updatedEdges = addEdge(newEdge, eds);
        
        setNodes((nds) => {
          // Update connection types
          const nodesWithConnectionTypes = updateNodeConnectionTypes(nds, updatedEdges);
          
          // Auto-layout parallel nodes if this is a parallel connection
          if (params.sourceHandle === 'output-right') {
            return autoLayoutParallelNodes(nodesWithConnectionTypes, updatedEdges);
          }
          
          return nodesWithConnectionTypes;
        });
        
        return updatedEdges;
      });
      setIsWorkflowModified(true);
      
    },
    [setEdges, setNodes, nodes, autoLayoutParallelNodes, locked]
  );

  // Custom edges change handler to update connection types
  const onEdgesChange = useCallback((changes) => {
    if (locked) {
      // Filter out remove changes when locked
      const filteredChanges = changes.filter(change => change.type !== 'remove');
      if (filteredChanges.length === 0) return;
      
      setEdges((eds) => {
        const updatedEdges = applyEdgeChanges(filteredChanges, eds);
        // Update node connection types after edge changes
        setNodes((nds) => updateNodeConnectionTypes(nds, updatedEdges));
        return updatedEdges;
      });
    } else {
      setEdges((eds) => {
        const updatedEdges = applyEdgeChanges(changes, eds);
        // Update node connection types after edge changes
        setNodes((nds) => updateNodeConnectionTypes(nds, updatedEdges));
        return updatedEdges;
      });
    }
  }, [setEdges, setNodes, locked]);

  // Handle node selection
  const onNodeClick = useCallback((event, node) => {
    console.log('Node clicked:', node); // Debug log
    if(node.docList!=null){
      const doc=node.docList;
      sessionStorage.setItem("nodeFilePath",doc.filePath)
      sessionStorage.setItem("nodeFileName",doc.fileName)  
    }else{
      sessionStorage.removeItem("nodeFilePath")
      sessionStorage.removeItem("nodeFileName")  
    }
 
    // Check if the click originated from a button inside the node
    if (event.target.tagName === 'BUTTON' || event.target.closest('button')) {
      console.log('Click originated from button, ignoring node selection');
      return;
    }
    
    if (locked) {
      showAlert('Cannot select nodes when diagram is locked', 'warning');
      return;
    }
    setSelectedNode(node);
    setIsPanelVisible(true);
    console.log('Selected node set:', node.id); // Debug log
  }, [locked]);

  // Handle selection change for multi-selection
  const onSelectionChange = useCallback(({ nodes, edges }) => {
    if (locked) {
      return;
    }
    
    setSelectedNodes(nodes);
    
    // If only one node is selected, also set it as the single selected node
    if (nodes.length === 1) {
      setSelectedNode(nodes[0]);
      setIsPanelVisible(true);
    } else if (nodes.length === 0) {
      setSelectedNode(null);
      setIsPanelVisible(false);
    } else {
      // Multiple nodes selected - clear single selection but keep panel closed
      setSelectedNode(null);
      setIsPanelVisible(false);
    }
  }, [locked]);

  // Find all child nodes in a serial chain
  const findSerialChildNodes = useCallback((nodeId, allNodes, allEdges) => {
    const childNodes = [];
    const visited = new Set();
    
    const findChildren = (currentNodeId) => {
      if (visited.has(currentNodeId)) return;
      visited.add(currentNodeId);
      
      // Find all outgoing serial connections (bottom output)
      const serialEdges = allEdges.filter(edge => 
        edge.source === currentNodeId && edge.sourceHandle === 'output-bottom'
      );
      
      serialEdges.forEach(edge => {
        const targetNode = allNodes.find(node => node.id === edge.target);
        if (targetNode) {
          childNodes.push(targetNode.id);
          // Recursively find children of this child
          findChildren(targetNode.id);
        }
      });
    };
    
    findChildren(nodeId);
    return childNodes;
  }, []);

  // Handle updating a node
  const handleUpdateNode = useCallback((updatedNode) => {
    setNodes((nds) => {
      const updatedNodes = nds.map((node) =>
        node.id === updatedNode.id ? {
          ...node,
          data: {
            ...node.data,
            ...updatedNode
          }
        } : node
      );
      // Update connection types after updating node
      return updateNodeConnectionTypes(updatedNodes, edges);
    });
    setSelectedNode(prev => prev && prev.id === updatedNode.id ? {
      ...prev,
      data: { ...prev.data, ...updatedNode }
    } : prev);
    setIsWorkflowModified(true);
  }, [setNodes, edges]);

  const downloadeDocumentAttachment = async (filePath, fileName) => {
      try {
        const data = await downloadeDocument(filePath);
        let blob = new Blob([data], { type: "application/octet-stream" });
        let url = window.URL.createObjectURL(blob);
        const anchor = document.createElement("a");
        anchor.href = url;
        anchor.download = fileName;
        anchor.target = "_blank";
        anchor.click();
      } catch (error) { }
    };

  // Handle downloading a dummy file for a node
  const handleDownloadFileForNode = useCallback((nodeId) => {
    const filePath=sessionStorage.getItem("nodeFilePath")
    const fileName=sessionStorage.getItem("nodeFileName")
    if(filePath!=null && fileName!=null){
      downloadeDocumentAttachment(filePath,fileName);
    }
    else{
      showAlert(`No file found at current node`, 'danger');
    }
//     try {
//       // Create dummy file content
//       const dummyContent = `Dummy file for Node: ${nodeId}
// Generated on: ${new Date().toISOString()}
// Node ID: ${nodeId}
// Workflow: ${workflowName || 'Untitled Workflow'}

// This is a sample file downloaded from the workflow node.
// You can replace this with actual file content or integrate with your file storage system.

// Node Details:
// - Created: ${new Date().toLocaleString()}
// - Type: Workflow Node File
// - Format: Text Document
// `;

//       // Create blob and download
//       const blob = new Blob([dummyContent], { type: 'text/plain' });
//       const url = window.URL.createObjectURL(blob);
//       const link = document.createElement('a');
//       link.href = url;
//       link.download = `node_${nodeId}_file.txt`;
//       document.body.appendChild(link);
//       link.click();
//       document.body.removeChild(link);
//       window.URL.revokeObjectURL(url);
      
//       showAlert(`Dummy file downloaded for node ${nodeId}`, 'success');
//     } catch (error) {
//       console.error('Error downloading file:', error);
//       showAlert('Failed to download file', 'danger');
//     }
  }, [workflowName]);

  // Handle file upload for a node using inbox service
  const handleUploadFileForNode = useCallback(async (nodeId) => {
    if (locked) {
      showAlert('Cannot upload files when diagram is locked', 'warning');
      return;
    }

    console.log("id=========",nodeId)
    
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '*/*'; // Accept all file types, you can restrict this as needed
    fileInput.multiple = false;
    
    fileInput.onchange = async (event) => {
      const file = event.target.files[0];
      if (file) {
        try {
          // Show loading state
          showAlert('Uploading file...', 'info');
          
          // Create FormData for upload (following DocumentManagement pattern)
          const formData = new FormData();
          formData.append('file', file);
          formData.append('folderPath', `workflow_nodes/${localStorage.getItem('id')}`); // Create a folder path for the node
          formData.append("documentName", file.name);
          formData.append('ownerName', localStorage.getItem('userName'));
          formData.append('employeeId', localStorage.getItem('id'));
          formData.append('uploadType', 'file');
          formData.append('type', 'file');
          formData.append('status', 'inbox');
          formData.append('nodeId', nodeId); // Add nodeId for reference
          
          // Upload using the inbox service
          const api = '/documentsattachmentdetail/saveDocument';
          const response = await uploadDocument(api, formData);
          
          if (response.status === 'success' || response.data) {
            // Update the node with the uploaded file information
            setNodes((nds) => {
              const updatedNodes = nds.map((node) => {
                if (node.id === nodeId) {
                  const existingFiles = node.data.uploadedFiles || [];
                  const newFile = {
                    id: response.data?.id || `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    uploadedAt: new Date().toISOString(),
                    filePath: response.data?.filePath || null,
                    documentId: response.data?.id || null,
                    serverResponse: response.data // Store the full server response
                  };
                  
                  return {
                    ...node,
                    data: {
                      ...node.data,
                      uploadedFiles: [...existingFiles, newFile]
                    }
                  };
                }
                return node;
              });
              return updateNodeConnectionTypes(updatedNodes, edges);
            });
            
            setIsWorkflowModified(true);
            showAlert(`File "${file.name}" uploaded successfully to node`, 'success');
            loadWorkflow(workflowType,selectedWorkflowId)
          } else {
            loadWorkflow(workflowType,selectedWorkflowId)
            showAlert('Failed to upload file', 'danger');
          }
        } catch (error) {
          loadWorkflow(workflowType,selectedWorkflowId)
          console.error('Error uploading file:', error);
          showAlert('Failed to upload file', 'danger');
        }
      }
    };
    
    // Trigger the file picker
    fileInput.click();
  }, [setNodes, edges, locked]);

  // Add Serial Node Handler
  const handleAddSerialNode = useCallback(() => {
    if (!selectedNode) {
      showAlert('Please select a node first', 'warning');
      return;
    }
    
    if (locked) {
      showAlert('Cannot add nodes when diagram is locked', 'warning');
      return;
    }

    // Calculate position for new serial node (below the selected node)
    const newPosition = {
      x: selectedNode.position.x,
      y: selectedNode.position.y + 150 // Position below
    };

    // Create new node
    const newNode = createDiagramNode({
      type: 'process',
      position: newPosition,
      label: 'Serial Process Node',
      assignedUsers: [],
      levelStatus: false,
    });

    // Convert to React Flow format
    const reactFlowNode = {
      id: newNode.id,
      type: 'process',
      position: newNode.position,
      data: {
        ...newNode.data,
        onUpdateNode: handleUpdateNode
      },
    };

    // Create edge connecting selected node to new node (serial connection)
    const newEdge = createDiagramEdge({
      id: `edge_${selectedNode.id}_${newNode.id}`,
      source: selectedNode.id,
      target: newNode.id,
      sourceHandle: 'output-bottom', // Serial connection uses bottom output
      targetHandle: 'input-top',     // Serial connection uses top input
      label: 'Serial (→)',
      data: {
        connectionType: 'series',
        workflowRule: 'Serial execution - connects to bottom output'
      },
      style: {
        stroke: '#4caf50',
        strokeWidth: 2,
        strokeDasharray: 'none'
      }
    });

    // Update state
    setNodes((nds) => {
      const updatedNodes = nds.concat(reactFlowNode);
      return updateNodeConnectionTypes(updatedNodes, [...edges, newEdge]);
    });
    
    setEdges((eds) => [...eds, newEdge]);
    setIsWorkflowModified(true);
    
    showAlert('Serial node added successfully', 'success');
  }, [selectedNode, setNodes, setEdges, edges, locked, handleUpdateNode]);

  // Add Parallel Node Handler  
  const handleAddParallelNode = useCallback(() => {
    console.log('handleAddParallelNode called, selectedNode:', selectedNode); // Debug log
    
    if (!selectedNode) {
      showAlert('Please select a node first', 'warning');
      return;
    }
    
    if (locked) {
      showAlert('Cannot add nodes when diagram is locked', 'warning');
      return;
    }

    console.log('Creating parallel node for selected node:', selectedNode.id); // Debug log

    // Calculate position for new parallel node (to the right of selected node)
    const existingParallelNodes = edges.filter(edge => 
      edge.source === selectedNode.id && edge.sourceHandle === 'output-right'
    ).length;
    
    console.log('Existing parallel nodes count:', existingParallelNodes); // Debug log
    
    const newPosition = {
      x: selectedNode.position.x + 300, // Position to the right
      y: selectedNode.position.y + (existingParallelNodes * 120) // Offset if multiple parallel nodes
    };

    console.log('New parallel node position:', newPosition); // Debug log

    // Create new node
    const newNode = createDiagramNode({
      type: 'process',
      position: newPosition,
      label: 'Parallel Process Node',
      assignedUsers: [],
      levelStatus: false,
    });

    console.log('Created new parallel node:', newNode); // Debug log

    // Convert to React Flow format
    const reactFlowNode = {
      id: newNode.id,
      type: 'process',
      position: newNode.position,
      data: {
        ...newNode.data,
        // Note: callbacks will be added via useEffect
      },
    };

    // Create edge connecting selected node to new node (parallel connection)
    const newEdge = createDiagramEdge({
      id: `edge_${selectedNode.id}_${newNode.id}`,
      source: selectedNode.id,
      target: newNode.id,
      sourceHandle: 'output-right', // Parallel connection uses right output
      targetHandle: 'input-left',   // Parallel connection uses left input
      label: 'Parallel (∥)',
      data: {
        connectionType: 'parallel',
        workflowRule: 'Parallel execution - connects to right output'
      },
      style: {
        stroke: '#ff9800',
        strokeWidth: 2,
        strokeDasharray: '5,5'
      }
    });

    console.log('Created new parallel edge:', newEdge); // Debug log

    // Update state - simplified approach to avoid nested state updates
    const updatedEdges = [...edges, newEdge];
    const updatedNodes = [...nodes, reactFlowNode];
    
    console.log('Updated edges after adding parallel:', updatedEdges); // Debug log
    console.log('Updated nodes after adding parallel:', updatedNodes); // Debug log
    
    // Apply connection type updates
    const nodesWithConnectionTypes = updateNodeConnectionTypes(updatedNodes, updatedEdges);
    
    // Apply auto-layout for parallel nodes
    const finalNodes = autoLayoutParallelNodes(nodesWithConnectionTypes, updatedEdges);
    console.log('Final nodes after auto-layout:', finalNodes); // Debug log
    
    // Update both states together
    setNodes(finalNodes);
    setEdges(updatedEdges);
    setIsWorkflowModified(true);
    
    showAlert('Parallel node added successfully', 'success');
  }, [selectedNode, nodes, edges, locked, handleUpdateNode, autoLayoutParallelNodes]);

  // Enhanced expandable node system
  // Find all descendant nodes (both serial and parallel children)
  const findAllDescendantNodes = useCallback((nodeId, allNodes, allEdges) => {
    const descendants = [];
    const visited = new Set();

    const findDescendants = (currentNodeId) => {
      if (visited.has(currentNodeId)) return;
      visited.add(currentNodeId);

      // Find all outgoing connections (both serial and parallel)
      const outgoingEdges = allEdges.filter(edge => edge.source === currentNodeId);

      outgoingEdges.forEach(edge => {
        const targetNode = allNodes.find(node => node.id === edge.target);
        if (targetNode) {
          descendants.push({
            nodeId: edge.target,
            connectionType: edge.sourceHandle === 'output-right' ? 'parallel' : 'serial',
            level: 1 // Will be calculated properly later
          });
          // Recursively find descendants of this child
          findDescendants(edge.target);
        }
      });
    };

    findDescendants(nodeId);
    return descendants;
  }, []);

  // Calculate node hierarchy levels for proper nesting
  const calculateNodeLevels = useCallback((rootNodeId, allEdges) => {
    const levels = new Map();
    const visited = new Set();

    const calculateLevel = (nodeId, currentLevel = 0) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);
      levels.set(nodeId, currentLevel);

      const outgoingEdges = allEdges.filter(edge => edge.source === nodeId);
      outgoingEdges.forEach(edge => {
        calculateLevel(edge.target, currentLevel + 1);
      });
    };

    calculateLevel(rootNodeId);
    return levels;
  }, []);

  // Enhanced minimize/maximize toggle with nested behavior
  const handleExpandableToggle = useCallback((nodeId, isExpanded) => {
    setNodes((nds) => {
      // Find all descendant nodes
      const descendants = findAllDescendantNodes(nodeId, nds, edges);
      const descendantIds = descendants.map(d => d.nodeId);

      // Calculate hierarchy levels
      const nodeLevels = calculateNodeLevels(nodeId, edges);

      // Update nodes with expand/collapse logic
      const updatedNodes = nds.map((node) => {
        if (node.id === nodeId) {
          // Update the root node with expanded state
          return {
            ...node,
            data: {
              ...node.data,
              isExpanded: isExpanded,
              hasChildren: descendantIds.length > 0,
              expandIcon: isExpanded ? '▼' : '▶',
              statusText: isExpanded ? 'Expanded' : 'Collapsed'
            }
          };
        } else if (descendantIds.includes(node.id)) {
          // Handle nested collapse: collapsing a parent hides all descendants
          // Handle nested expand: expanding shows direct children only
          const nodeLevel = nodeLevels.get(node.id) || 0;
          const rootLevel = nodeLevels.get(nodeId) || 0;

          let shouldBeVisible = isExpanded;

          if (isExpanded) {
            // When expanding, only show direct children (level 1 from root)
            // Grandchildren remain hidden unless their parent is also expanded
            shouldBeVisible = (nodeLevel - rootLevel) === 1;

            // For nested expand: check if all parent nodes in the chain are expanded
            if (nodeLevel > rootLevel + 1) {
              // This is a grandchild or deeper - check if immediate parent is expanded
              const parentEdges = edges.filter(edge => edge.target === node.id);
              if (parentEdges.length > 0) {
                const immediateParent = nds.find(n => n.id === parentEdges[0].source);
                if (immediateParent && !immediateParent.data.isExpanded) {
                  shouldBeVisible = false;
                }
              }
            }
          }

          return {
            ...node,
            hidden: !shouldBeVisible,
            data: {
              ...node.data,
              parentExpanded: isExpanded,
              hierarchyLevel: nodeLevel - rootLevel
            }
          };
        }
        return node;
      });

      return updateNodeConnectionTypes(updatedNodes, edges);
    });

    // Handle edge visibility - edges automatically hide/show with their connected nodes
    setEdges((eds) => {
      const descendants = findAllDescendantNodes(nodeId, nodes, eds);
      const descendantIds = descendants.map(d => d.nodeId);

      return eds.map(edge => {
        // Hide edges connected to descendant nodes when collapsed
        const shouldHideEdge = !isExpanded && (
          descendantIds.includes(edge.source) ||
          descendantIds.includes(edge.target)
        );

        return {
          ...edge,
          hidden: shouldHideEdge,
          style: {
            ...edge.style,
            opacity: shouldHideEdge ? 0 : 1
          }
        };
      });
    });

    setIsWorkflowModified(true);
  }, [setNodes, setEdges, edges, nodes, findAllDescendantNodes, calculateNodeLevels]);

  // Legacy support - map old minimize toggle to new expandable toggle
  const handleMinimizeToggle = useCallback((nodeId, isMinimized) => {
    handleExpandableToggle(nodeId, !isMinimized);
  }, [handleExpandableToggle]);

  // Handle background click (deselect)
  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    setSelectedNodes([]);
    setIsPanelVisible(false);
  }, []);

  // Delete selected node
  const handleDeleteNode = useCallback(() => {
    if (locked) {
      showAlert('Cannot delete nodes when diagram is locked', 'warning');
      return;
    }
    if (selectedNode) {
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== selectedNode.id);
        // Update connection types after deleting node
        return updateNodeConnectionTypes(updatedNodes, edges);
      });
      setEdges((eds) => eds.filter((edge) => 
        edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ));
      setSelectedNode(null);
      setIsPanelVisible(false);
      setIsWorkflowModified(true);
    }
  }, [selectedNode, setNodes, setEdges, edges, locked]);

  // Copy nodes function (handles both single and multiple nodes)
  const handleCopyNodes = useCallback(() => {
    if (selectedNodes.length > 0) {
      // Copy multiple nodes
      setCopiedNodes(selectedNodes);
      setCopiedNode(null); // Clear single node copy
      showAlert(`${selectedNodes.length} nodes copied to clipboard`, 'success');
    } else if (selectedNode) {
      // Copy single node
      setCopiedNode(selectedNode);
      setCopiedNodes([]); // Clear multi-node copy
      showAlert('Node copied to clipboard', 'success');
    }
  }, [selectedNode, selectedNodes]);

  // Paste nodes function (handles both single and multiple nodes)
  const handlePasteNodes = useCallback(() => {
    if (copiedNodes.length > 0) {
      // Paste multiple nodes
      const newNodes = copiedNodes.map((node, index) => ({
        ...node,
        id: `node_${Date.now()}_${Math.random().toString(36).substring(2, 11)}_${index}`,
        position: {
          x: node.position.x + 50 + (index * 20), // Slight offset for each node
          y: node.position.y + 50 + (index * 20),
        },
        data: {
          ...node.data,
          label: `${node.data.label} (Copy)`,
        },
      }));

      setNodes((nds) => {
        const updatedNodes = nds.concat(newNodes);
        // Update connection types after pasting nodes
        return updateNodeConnectionTypes(updatedNodes, edges);
      });
      setIsWorkflowModified(true);
      showAlert(`${newNodes.length} nodes pasted successfully`, 'success');
    } else if (copiedNode) {
      // Paste single node
      const newNode = {
        ...copiedNode,
        id: `node_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        position: {
          x: copiedNode.position.x + 50,
          y: copiedNode.position.y + 50,
        },
        data: {
          ...copiedNode.data,
          label: `${copiedNode.data.label} (Copy)`,
        },
      };

      setNodes((nds) => {
        const updatedNodes = nds.concat(newNode);
        // Update connection types after pasting node
        return updateNodeConnectionTypes(updatedNodes, edges);
      });
      setIsWorkflowModified(true);
      showAlert('Node pasted successfully', 'success');
    }
  }, [copiedNode, copiedNodes, setNodes, edges]);

  // Delete nodes function (handles both single and multiple nodes)
  const handleDeleteNodes = useCallback(() => {
    if (selectedNodes.length > 0) {
      // Delete multiple nodes
      const nodeIdsToDelete = selectedNodes.map(node => node.id);
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => !nodeIdsToDelete.includes(node.id));
        // Update connection types after deleting nodes
        return updateNodeConnectionTypes(updatedNodes, edges);
      });
      setEdges((eds) => eds.filter((edge) => 
        !nodeIdsToDelete.includes(edge.source) && !nodeIdsToDelete.includes(edge.target)
      ));
      setSelectedNodes([]);
      setIsPanelVisible(false);
      setIsWorkflowModified(true);
      showAlert(`${nodeIdsToDelete.length} nodes deleted`, 'success');
    } else if (selectedNode) {
      // Delete single node
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== selectedNode.id);
        // Update connection types after deleting node
        return updateNodeConnectionTypes(updatedNodes, edges);
      });
      setEdges((eds) => eds.filter((edge) => 
        edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ));
      setSelectedNode(null);
      setIsPanelVisible(false);
      setIsWorkflowModified(true);
      showAlert('Node deleted', 'success');
    }
  }, [selectedNode, selectedNodes, setNodes, setEdges, edges]);

  // Handle keyboard shortcuts
  const handleKeyPress = useCallback((event) => {
    // Check if user is currently typing in an input field
    const isTypingInInput = event.target.tagName === 'INPUT' ||
                           event.target.tagName === 'TEXTAREA' ||
                           event.target.contentEditable === 'true' ||
                           event.target.isContentEditable;

    if (event.key === 'Delete' && (selectedNode || selectedNodes.length > 0) && !isTypingInInput) {
      handleDeleteNodes();
    }
    if (event.key === 'Escape') {
      // If user is typing in input, don't interfere
      if (isTypingInInput) {
        return;
      }
      // If a node is selected or panel is visible, just clear selection
      if (selectedNode || selectedNodes.length > 0 || isPanelVisible) {
        setSelectedNode(null);
        setSelectedNodes([]);
        setIsPanelVisible(false);
      } else {
        // If no node is selected, trigger cancel
        handleCancel();
      }
    }
    // Copy functionality (Ctrl+C) - only when not typing in input
    if (event.ctrlKey && event.key === 'c' && (selectedNode || selectedNodes.length > 0) && !isTypingInInput) {
      handleCopyNodes();
    }
    // Paste functionality (Ctrl+V) - only when not typing in input
    if (event.ctrlKey && event.key === 'v' && (copiedNode || copiedNodes.length > 0) && !isTypingInInput) {
      handlePasteNodes();
    }
    // Expand/Collapse functionality (Space key) - only when a single node is selected
    if (event.key === ' ' && selectedNode && !isTypingInInput) {
      event.preventDefault(); // Prevent page scroll
      const nodeHasChildren = selectedNode.data.hasChildren;
      if (nodeHasChildren) {
        const isCurrentlyExpanded = selectedNode.data.isExpanded !== false; // Default to true
        handleExpandableToggle(selectedNode.id, !isCurrentlyExpanded);
      }
    }
    // Expand All (Ctrl+Shift+E) - expand all nodes with children
    if (event.ctrlKey && event.shiftKey && event.key === 'E' && !isTypingInInput) {
      event.preventDefault();
      nodes.forEach(node => {
        if (node.data.hasChildren) {
          handleExpandableToggle(node.id, true);
        }
      });
    }
    // Collapse All (Ctrl+Shift+C) - collapse all nodes with children
    if (event.ctrlKey && event.shiftKey && event.key === 'C' && !isTypingInInput) {
      event.preventDefault();
      nodes.forEach(node => {
        if (node.data.hasChildren) {
          handleExpandableToggle(node.id, false);
        }
      });
    }
  }, [selectedNode, selectedNodes, handleDeleteNodes, copiedNode, copiedNodes, isPanelVisible, handleCancel, handleCopyNodes, handlePasteNodes, handleExpandableToggle, nodes]);

  // Legacy single node functions for backward compatibility
  const handleCopyNode = useCallback(() => {
    handleCopyNodes();
  }, [handleCopyNodes]);

  const handlePasteNode = useCallback(() => {
    handlePasteNodes();
  }, [handlePasteNodes]);

  // Update all nodes to include the callbacks and expandable state whenever they change
  useEffect(() => {
    setNodes((nds) => nds.map(node => {
      // Check if this node has children
      const hasChildren = edges.some(edge => edge.source === node.id);
      const currentlyExpanded = node.data.isExpanded !== undefined ? node.data.isExpanded : true;

      return {
        ...node,
        data: {
          ...node.data,
          onUpdateNode: handleUpdateNode,
          onMinimizeToggle: handleExpandableToggle,
          onDownloadFile: handleDownloadFileForNode,
          onUploadFile: handleUploadFileForNode,
          hasChildren: hasChildren,
          isExpanded: currentlyExpanded,
          expandIcon: hasChildren ? (currentlyExpanded ? '▼' : '▶') : '',
          statusText: hasChildren ? (currentlyExpanded ? 'Expanded' : 'Collapsed') : ''
        }
      };
    }));
  }, [handleUpdateNode, handleExpandableToggle, handleDownloadFileForNode, handleUploadFileForNode, edges, setNodes]);

  // Attach keyboard listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [handleKeyPress]);

  const diagramStyle = embedded ? {
    width: '100%',
    height: '100%',
    backgroundColor: '#f5f5f5',
    display: 'flex',
  } : {
    width: '100vw',
    height: '100vh',
    backgroundColor: '#f5f5f5',
  };

  return (
    <div style={diagramStyle}>
      <ReactFlowProvider>
        {/* Toolbar */}
        <Toolbar 
          onAddNode={handleAddNode}
          onAddSerialNode={handleAddSerialNode}     // New prop
          onAddParallelNode={handleAddParallelNode} // New prop
          selectedNode={selectedNode}               // New prop
          embedded={embedded}
          usersLoading={usersLoading}
          onCopyNode={handleCopyNode}
          onPasteNode={handlePasteNode}
          canCopy={!!(selectedNode || selectedNodes.length > 0)}
          canPaste={!!(copiedNode || copiedNodes.length > 0)}
          selectedNodeCount={selectedNodes.length > 0 ? selectedNodes.length : (selectedNode ? 1 : 0)}
          copiedNodeCount={copiedNodes.length > 0 ? copiedNodes.length : (copiedNode ? 1 : 0)}
          onSave={embedded && onSave ? onSave : null}
          onCancel={embedded ? handleCancel : (onCancel || null)}
          isEditMode={!!workflowId}
          canSave={hasProcessNode && !locked}
          locked={locked}
          onDownloadFile={handleDownloadFileForNode}  // New prop
          onUploadFile={handleUploadFileForNode}      // New prop
        />
        
        <div style={embedded ? { flex: 1, position: 'relative' } : { width: '100%', height: '100%', position: 'relative' }}>
          {/* Workflow Tiles - Moved outside ReactFlow for better click handling */}
          {workflows.length > 0 && (
            <div style={{
              position: 'absolute',
              top: '10px',
              left: '10px',
              right: '10px',
              zIndex: 10,
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '8px',
              padding: '16px',
              height: '140px',
              overflowX: 'auto',
              overflowY: 'hidden',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}>
              <div style={{
                display: 'flex',
                flexWrap: 'nowrap',
                gap: '16px',
                justifyContent: 'flex-start',
                minWidth: 'max-content',
              }}>
                {workflows.map((workflow) => {
                  const isSelected = selectedWorkflowId === workflow.id;
                  return (
                    <div
                      key={workflow.id}
                      className="workflow-tile"
                      style={{
                        background: isSelected ? '#e3f2fd' : '#fff',
                        border: isSelected ? '2px solid #2196f3' : '1px solid #ddd',
                        borderRadius: '8px',
                        boxShadow: isSelected ? '0 4px 12px rgba(33, 150, 243, 0.3)' : '0 2px 8px rgba(0,0,0,0.05)',
                        padding: '12px',
                        minWidth: '160px',
                        maxWidth: '160px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        flexShrink: 0,
                        position: 'relative',
                        outline: 'none',
                      }}
                      onMouseEnter={(e) => {
                        if (!isSelected) {
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                          e.currentTarget.style.transform = 'translateY(-2px)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isSelected) {
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
                          e.currentTarget.style.transform = 'translateY(0)';
                        }
                      }}
                      tabIndex={0}
                      onKeyDown={(e) => handleWorkflowKeyDown(e, workflow)}
                      onClick={(e) => handleWorkflowClick(e,workflow)}>
                      {isSelected && (
                        <div style={{
                          position: 'absolute',
                          top: '-8px',
                          right: '-8px',
                          backgroundColor: '#2196f3',
                          color: 'white',
                          borderRadius: '50%',
                          width: '24px',
                          height: '24px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                          zIndex: 10,
                        }}>
                          ✓
                        </div>
                      )}
                     <div style={{ 
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '6px'
        }}>
          <div style={{ 
            fontWeight: 'bold', 
            fontSize: '14px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis', 
            whiteSpace: 'nowrap',
            flex: 1
          }}>
            {workflow.name}
          </div>
        </div>
                    <div style={{ fontSize: '12px', color: '#888', marginBottom: '4px',textTransform:"cap" }}>
                      {/* Type: */}
                      {workflow.workflow_type}
                    </div>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      fontSize: '10px', 
                      color: '#aaa' 
                    }}>
                      {/* <span>Click to load</span> */}
                      <div style={{
            display: 'flex',
            gap: '8px',
            zIndex: 40,
            position: 'relative'
          }}>
            <span 
              onClick={(e) => {
                e.stopPropagation();
                handlePrivacyChange('private',workflow);
              }}
              style={{
                color: workflow.visibleMode === 'private' ? '#2196f3' : '#aaa',
                fontWeight: workflow.visibleMode === 'private' ? 'bold' : 'normal',
                cursor: 'pointer',
                fontSize: '12px',
                transition: 'all 0.2s ease' 
              }}
            >
              private
            </span>
            <span 
              onClick={(e) => {
                e.stopPropagation();
                handlePrivacyChange('public',workflow);
              }}
              style={{
                color: workflow.visibleMode === 'public' ? 'green' : '#aaa',
                fontWeight: workflow.visibleMode === 'public' ? 'bold' : 'normal',
                cursor: 'pointer',
                fontSize: '12px',
                transition: 'all 0.2s ease' 
              }}
            >
              public
            </span>
            <span 
              onClick={(e) => {
                e.stopPropagation();
                handleWorkflowDuplicate(workflow);
              }}
              style={{
                color: "blue",
                cursor: 'pointer',
                fontSize: '12px',
                transition: 'all 0.2s ease' 
              }}
              title="Duplicate workflow"
            >
              <i className='fa fa-copy'></i>
            </span>
          </div>
                      <span 
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDeleteModal(true);
                          setwfId(workflow.id);
                        }}
                        style={{
                          color: "red",
                          cursor: 'pointer',
                          fontSize: '12px',
                          transition: 'all 0.2s ease',
                          padding: '2px'
                        }}
                        title="Delete workflow (or Press Delete Key)"
                      >
                        <i className='fa fa-trash'></i>
                      </span>
                    </div>
                  </div>
                  );
                })}
              </div>
            </div>
          )}
          
          <ReactFlow
            nodes={nodes.map(node => ({
              ...node,
              data: {
                ...node.data,
                ...node.docList,
                isMultiSelected: selectedNodes.some(n => n.id === node.id)
              }
            }))}
            edges={edges}
            onNodesChange={locked ? () => {} : onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onPaneClick={onPaneClick}
            onSelectionChange={onSelectionChange}
            nodeTypes={nodeTypes}
            nodesDraggable={!locked}
            nodesConnectable={!locked}
            elementsSelectable={!locked}
            multiSelectionKeyCode="Shift"
            selectionKeyCode="Shift"
            // fitView
            attributionPosition="bottom-left"
          >
            <Background variant="dots" gap={20} size={1} />
            {/* <Controls /> */}
            <Controls 
              position="left"
              style={{
                position: 'absolute',
                left: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                display: 'flex',
                flexDirection: 'column',
                gap: '8px'
              }}
              onInteractiveChange={(interactivity) => setLocked(!interactivity)}
            />
            <MiniMap
              nodeStrokeColor={(n) => '#333'}
              nodeColor={(n) => {
                if (n.type === 'custom') return '#7b1fa2';
                if (n.type === 'approval') return '#388e3c';
                if (n.type === 'esign') return '#fbc02d';
                if (n.type === 'acknowledge') return '#1976d2';
                if (n.type === 'sub_process') return '#f57c00';
                if (n.type === 'process') return '#0277bd';
                if (n.type === 'start') return '#4caf50';
                if (n.type === 'end') return '#f44336';
                return '#0277bd';
              }}
              nodeBorderRadius={2}
              style={{
                background: 'rgba(255, 255, 255, 0.9)',
                border: '1px solid #ddd',
                borderRadius: '8px',
              }}
            />
          </ReactFlow>
        </div>
        
        {/* User Assignment Panel */}
        <UserAssignmentPanel
          selectedNode={selectedNode}
          availableUsers={availableUsers}
          onUpdateNode={handleUpdateNode}
          isVisible={isPanelVisible}
          onClose={() => setIsPanelVisible(false)}
          usersLoading={usersLoading}
          onRefreshUsers={refreshUsers}
          locked={locked}
        />
        
        {/* Instructions overlay */}
        {nodes.length === 0 && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            color: '#666',
            fontSize: '18px',
            pointerEvents: 'none',
            zIndex: 10,
          }}>
          </div>
        )}

        {/* Locked workflow indicator */}
        {locked && !isOwner && currentWorkflow && (
          <div style={{
            position: 'absolute',
            top: '160px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(255, 193, 7, 0.95)',
            color: '#856404',
            padding: '12px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '14px',
            fontWeight: '500',
            border: '1px solid #ffeaa7'
          }}>
            <i className="fa fa-lock" style={{ fontSize: '16px' }}></i>
            <span>
              Read-Only Mode: You are not the creator of this workflow
              {currentWorkflow.createdBy && (
                <span style={{ fontWeight: 'normal', marginLeft: '8px' }}>
                  (Created by: {currentWorkflow.createdBy})
                </span>
              )}
            </span>
          </div>
        )}

        {/* Alert */}
        {alert.show && (
          <Alert
            variant={alert.variant}
            style={{
              position: embedded ? 'absolute' : 'fixed',
              top: '20px',
              right: '20px',
              zIndex: 1000,
              minWidth: '300px',
            }}
            onClose={() => setAlert({ show: false, message: '', variant: 'info' })}
            dismissible
          >
            {alert.message}
          </Alert>
        )}

        {/* Save Workflow Modal */}
        <Modal show={showSaveModal} onHide={() => {
          if (isWorkflowModified) {
            handleCancel();
          } else {
            setShowSaveModal(false);
          }
        }} centered>
          <Modal.Header closeButton>
            <Modal.Title>
              {currentWorkflow ? 'Update Workflow' : 'Save New Workflow'}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Workflow Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter workflow name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                />
              </Form.Group>
              {!currentWorkflow && <Form.Group className="mb-3">
                <Form.Label>Workflow Type</Form.Label>
                <Form.Select
                  value={workflowType}
                  onChange={(e) => setWorkflowType(e.target.value)}
                >
                  <option value="approve">Approval</option>
                  <option value="acknowledgement">Acknowledgement</option>
                  <option value="eSign">Electronic Sign</option>
                  {/* <option value="custom">Custom</option> */}
                </Form.Select>
              </Form.Group>
            }
            {!hasProcessNode && (
              <Alert variant="warning" className="mt-3">
                <strong>⚠️ Workflow Validation Required:</strong> You must add at least one process node to the workflow before saving.
              </Alert>
            )}
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => {
              if (isWorkflowModified) {
                handleCancel();
              } else {
                setShowSaveModal(false);
              }
            }}>
              Close
            </Button>
            <Button 
              variant="primary" 
              onClick={saveWorkflow} 
              disabled={loading || !hasProcessNode || locked}
              title={locked ? 'Cannot save when diagram is locked' : (!hasProcessNode ? 'Add at least one process node to save the workflow' : '')}
            >
              {loading ? <Spinner size="sm" className="me-2" /> : null}
              {currentWorkflow ? 'Update' : 'Save'}
            </Button>
          </Modal.Footer>
        </Modal>

        <Modal show={showCancelConfirm} onHide={() => setShowCancelConfirm(false)}>
        <Modal.Header closeButton 
            className="modal-header-modern"
            style={{ padding: "8px 15px", height: "45px" }}>
          <Modal.Title>Confirm Workflow Closure</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Do you want to close the current workflow? Any unsaved changes will be lost.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCancelConfirm(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={confirmCancel}>
            Yes, Close
          </Button>
        </Modal.Footer>
      </Modal>

        <Modal show={showPrivacyModal} onHide={() => setShowPrivacyModal(false)} centered>
        <Modal.Header closeButton 
            className="modal-header-modern"
            style={{ padding: "8px 15px", height: "45px" }}>
          <Modal.Title style={{ fontSize: "18px", color: "white" }}>Change Workflow Visibility</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to make workflow "{currentWorkflow?.name}" {selectedPrivacy}?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPrivacyModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={confirmPrivacyChange}>
            Make {selectedPrivacy}
          </Button>
        </Modal.Footer>
      </Modal>

      <Modal show={showDuplicateModal} onHide={() => setShowDuplicateModal(false)} >
        <Modal.Header closeButton 
            className="modal-header-modern"
            style={{ padding: "8px 15px", height: "45px" }}>
          <Modal.Title style={{ fontSize: "18px", color: "white" }}>Duplicate Workflow</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to make a duplicate of workflow "{currentWorkflow?.name}"?
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDuplicateModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={makeDuplicateWorkflow}>
            Make Duplicate
          </Button>
        </Modal.Footer>
      </Modal>

         {/* Save Workflow Modal */}
         <Modal show={showDeleteModal} onHide={() => {
          setShowDeleteModal(false);setwfId(null);}} centered>
          <Modal.Header closeButton 
            className="modal-header-modern"
            style={{ padding: "8px 15px", height: "45px" }}>
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              Delete Workflow
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div> 
              Do you want to delete this workflow?
            </div>
          </Modal.Body>
          <Modal.Footer>
             <Button variant="primary" onClick={()=>{setShowDeleteModal(false);setwfId(null);}}>
                Close
            </Button>
            <Button variant="danger" onClick={()=>handleDeleteWorkflow(wfId)}>
              Yes,Delete
            </Button>
          </Modal.Footer>
        </Modal>
      </ReactFlowProvider>
    </div>
  );
});
export default DiagramEditor;
