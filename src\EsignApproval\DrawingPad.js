import React, { useRef, useEffect, useState } from 'react';
import { Button } from 'react-bootstrap';

const DrawingPad = ({ onSave }) => {
    const canvasRef = useRef(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [context, setContext] = useState(null);
    const [color,setColor]=useState("black");
    const [hasContent, setHasContent] = useState(false);

    useEffect(() => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        setContext(ctx);

        // Set canvas size
        canvas.width = 300; // Adjust width as needed
        canvas.height = 100; // Adjust height as needed
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
    }, []);

    const startDrawing = (e) => {
        setIsDrawing(true);
        setHasContent(true); 
        draw(e);
    };

    const endDrawing = () => {
        setIsDrawing(false);
        context.beginPath(); // Reset path for new drawing
    };

    const draw = (e) => {
        if (!isDrawing) return;

        context.lineWidth = 2; // Adjust line width as needed
        context.lineCap = 'round';
        context.strokeStyle = color; // Adjust stroke color as needed

        context.lineTo(e.nativeEvent.offsetX, e.nativeEvent.offsetY);
        context.stroke();
        context.beginPath();
        context.moveTo(e.nativeEvent.offsetX, e.nativeEvent.offsetY);
    };

    const clearCanvas = () => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height); // Clear the canvas
        setHasContent(false); // Reset hasContent
    };

    const saveCanvas = () => {
        const canvas = canvasRef.current;
        const image = canvas.toDataURL('image/png'); // Get image data
        //const link = document.createElement('a');
        //link.href = image;
        //link.download = 'drawing.png'; // Set the filename
        //link.click(); // Simulate a click to download
        if (onSave) {
            onSave(image); // Send the image data to the parent
        }
    };

    const handleColor=(event)=>{
        setColor(event.target.value);
    }

    return (
        <div>
            <input type='color' onChange={handleColor} style={{cursor:"pointer"}} />
            <canvas
                ref={canvasRef}
                onMouseDown={startDrawing}
                onMouseUp={endDrawing}
                onMouseLeave={endDrawing}
                onMouseMove={draw}
                onTouchStart={startDrawing}
                onTouchEnd={endDrawing}
                onTouchMove={draw}
                style={{
                    border: '1px solid black',
                    cursor: 'crosshair',
                    marginTop: '20px',
                }}
            />
            <div style={{ marginTop: '10px' }}>
                <Button variant='gray' onClick={clearCanvas} style={{ marginRight: '10px' }}>
                    Clear
                </Button>
                <Button variant='success' onClick={saveCanvas} disabled={!hasContent}>
                    Save 
                </Button>
            </div>
        </div>
    );
};

export default DrawingPad;
