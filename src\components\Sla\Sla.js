import React, { Component } from "react";
import classes from "./Sla.module.css";
import { Modal, Button } from "react-bootstrap";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

class Sla extends Component {
  state = {
    isOpen: false,
    isExpirationEnabled: false,
    expirationDate: new Date(),
    email: "",
    userGroup: "",
    isCustomSelected: false,
    isInputGiven:false,
  };

  openModal = () => {
    this.setState({ isOpen: true });
    console.log("Modal opened");
  };

  closeModal = () => {
    this.setState({ isOpen: false, email: "", userGroup: "" });
  };

  toggleExpiration = () => {
    this.setState((prevState) => ({
      isExpirationEnabled: !prevState.isExpirationEnabled,
      expirationDate: new Date(),
    }));
  };

  handleDateChange = (date) => {
    this.setState({ expirationDate: date });
  };

  handleEmailChange = (event) => {
    this.setState({ email: event.target.value, userGroup: "",});
  };

  handleUserGroupChange = (event) => {
    this.setState({ userGroup: event.target.value, email: "",});
  };
  selectedValue = (event) => {
    const selectedOption = event.target.value;
    console.log("Selected value:", selectedOption);
    if (selectedOption === "Custom") {
      this.setState({ isCustomSelected: true });
    } else {
      this.setState({ isCustomSelected: false });
    }
  };

  render() {
    const { isOpen, isExpirationEnabled, expirationDate, email, userGroup } =
      this.state;
    const isEmailDisabled = !!userGroup;
    const isUserGroupDisabled = !!email;

    return (
      <>
        <div>
          <button className={classes.button} onClick={this.openModal}>
            share <i className="fa fa-share-alt"></i>
          </button>
        </div>
        <div>
          <Modal show={isOpen}
            onHide={this.closeModal}
            size="md"
            dialogClassName={classes.centeredModal}
          >
            <Modal.Header closeButton className="modal-header-modern" style={{padding:'4px 10px', height:'36px', alignItems:'center'}}>
              <Modal.Title style={{fontSize:'16px', textAlign:'center', width:'100%', margin:0}}>Share File</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div>Share file "New Document.docx"</div>
              <div className="d-flex align-items-center mt-3">
                <input
                  type="email"
                  placeholder="share to (e-mail)"
                  className="form-control me-2"
                  value={email}
                  onChange={this.handleEmailChange}
                  disabled={isEmailDisabled}
                  onInput={()=>{this.setState({isInputGiven:true})}}
                />
                or
                <select
                  className="form-control bg-info text-white"
                  value={userGroup}
                  onChange={this.handleUserGroupChange}
                  disabled={isUserGroupDisabled}
                  onSelect={()=>{this.setState({isInputGiven:true})}}
                >
                  <option value="">User Group</option>
                  <option value="group1">Group 1</option>
                  <option value="group2">Group 2</option>
                </select>
              </div>
              <div className="mt-3">
                <select
                  className="form-control bg-info text-white"
                  name="viewType"
                  onChange={this.selectedValue}
                >
                  <option value="Preview">Preview</option>
                  <option value="Viewer">Viewer</option>
                  <option value="Editor">Editor</option>
                  <option value="Custom">Custom</option>
                </select>
              </div>
              {this.state.isCustomSelected && (
                <div className="mt-3">
                  <strong>File</strong>
                  <div className="col-6 d-flex flex-wrap justify-content-between">
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 1
                    </label>
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 2
                    </label>
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 3
                    </label>
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 4
                    </label>
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 5
                    </label>
                    <label className="d-flex align-items-center w-80">
                      <input type="checkbox" className="me-2" /> Option 6
                    </label>
                  </div>
                </div>
              )}

              <div className="mt-3 d-flex align-items-center">
                <label className={classes.slider}>
                  <input
                    type="checkbox"
                    checked={isExpirationEnabled}
                    onChange={this.toggleExpiration}
                  />
                  <div className={classes.sliderTrack}>
                    <div className={classes.sliderThumb}></div>
                  </div>
                </label>
                &nbsp;&nbsp;
                <span className="ml-2">Expiration</span>
                {isExpirationEnabled && (
                  <div className="ms-auto mt-2" style={{ width: "200px" }}>
                    <DatePicker
                      selected={expirationDate}
                      onChange={this.handleDateChange}
                      showTimeSelect
                      timeIntervals={1}
                      timeCaption="Time"
                      dateFormat="Pp"
                      minDate={new Date()}
                      className="form-control"
                    />
                  </div>
                )}
              </div>
              <div className="mt-3">
                <Button variant="info" disabled={!this.state.isInputGiven} >Share</Button>
              </div>
            </Modal.Body>
          </Modal>
        </div>
      </>
    );
  }
}

export default Sla;
