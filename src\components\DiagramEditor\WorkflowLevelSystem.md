# Workflow Level Tracking System

## Overview

The workflow level tracking system has been implemented to properly display workflow structures with parallel and serial nodes. This system assigns specific level information to each node to enable proper visualization and understanding of workflow hierarchies.

## Level Structure

### 1. ROOT Node (First Node)
- **Level**: `level1`
- **Root Level**: `null`
- **Root Level Type**: `null`

### 2. Parallel Nodes (Connected via Right Output)
- **Level**: `level2`, `level3`, etc.
- **Root Level**: `level1`
- **Root Level Type**: `parallel`

### 3. Serial Nodes (Connected via Bottom Output)
- **Level**: `level2`, `level3`, etc.
- **Root Level**: `level1`
- **Root Level Type**: `serial`

## Implementation Details

### Connection Types

The system uses React Flow's connection handles to determine node relationships:

- **Right Output Handle** (`output-right`): Indicates parallel connections
- **Bottom Output Handle** (`output-bottom`): Indicates serial connections

### Visual Structure

Based on the level data, workflows can be displayed as:

```
  NODE1 (ROOT) --------> NODE2 (Parallel)
    |
    |
  NODE3 (Serial)
```

Where:
- NODE1 is the ROOT node (level1)
- NODE2 is connected via right output (parallel)
- NODE3 is connected via bottom output (serial)

## Code Changes

### 1. workflowService.js

Enhanced `convertDiagramToWorkflow` function to:
- Track node levels based on position and connections
- Assign appropriate root level and root level type
- Include level information in approval level DTOs

Enhanced `convertWorkflowToDiagram` function to:
- Reconstruct diagrams with proper level information
- Position nodes based on their root level type
- Create appropriate connections based on level data

### 2. types.js

Enhanced `updateNodeConnectionTypes` function to:
- Calculate and assign level information to nodes
- Update connection types based on edge analysis
- Maintain level hierarchy information

### 3. CustomNode.js

Enhanced tooltip display to show:
- Node level information
- Root level reference
- Root level type (parallel/serial)

## Usage Example

```javascript
// Create a workflow with level tracking
const workflowData = convertDiagramToWorkflow(nodes, edges, 'My Workflow', 'approve');

// The resulting approvalLevelDTO will contain:
[
  {
    name: "John Doe",
    usersId: 1,
    level: "level1",
    rootLevel: null,
    rootLevelType: null
  },
  {
    name: "Jane Smith", 
    usersId: 2,
    level: "level2",
    rootLevel: "level1",
    rootLevelType: "parallel"
  },
  {
    name: "Bob Johnson",
    usersId: 3, 
    level: "level3",
    rootLevel: "level1",
    rootLevelType: "serial"
  }
]
```

## Benefits

1. **Clear Hierarchy**: Each node has a defined level and relationship to the root
2. **Proper Visualization**: Nodes can be positioned correctly based on their type
3. **Data Consistency**: Level information is preserved when saving/loading workflows
4. **User Understanding**: Tooltips show clear level and relationship information

## Future Enhancements

- Support for more complex workflow patterns
- Multi-level parallel and serial combinations
- Conditional routing based on level information
- Advanced visualization options based on level data 