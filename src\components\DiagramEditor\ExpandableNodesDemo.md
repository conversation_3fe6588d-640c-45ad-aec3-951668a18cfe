# Expandable Nodes Functionality

## Overview
The DiagramEditor now supports expandable nodes with the following features:

### Key Features
1. **Clicking expandable nodes toggles their children's visibility**
   - Nodes with children show an expand/collapse button (▶/▼)
   - Click the button to toggle visibility of child nodes

2. **Nested collapse: collapsing a parent hides all descendants**
   - When you collapse a parent node, all its children and grandchildren are hidden
   - The entire subtree becomes invisible

3. **Nested expand: expanding shows direct children (not grandchildren unless they're also expanded)**
   - When you expand a parent node, only direct children become visible
   - Grandchildren remain hidden unless their immediate parent is also expanded

4. **Visual feedback with icons, colors, and status text**
   - Expand/collapse button with ▶ (collapsed) and ▼ (expanded) icons
   - Color coding: Green border for expanded nodes, Orange border for collapsed nodes
   - Status text showing "Expanded" or "Collapsed"
   - Hierarchy level indication with left margin

5. **Edges automatically hide/show with their connected nodes**
   - When nodes are hidden, their connecting edges are also hidden
   - Edges automatically reappear when nodes become visible

## Usage

### Mouse Interaction
- Click the expand/collapse button (▶/▼) on any node with children
- The button appears in the top-left corner of nodes that have child nodes

### Keyboard Shortcuts
- **Space**: Toggle expand/collapse for the selected node (if it has children)
- **Ctrl+Shift+E**: Expand all nodes with children
- **Ctrl+Shift+C**: Collapse all nodes with children

### Visual Indicators
- **Green left border**: Node is expanded and has children
- **Orange left border**: Node is collapsed and has children
- **Expand/Collapse button**: Shows ▶ when collapsed, ▼ when expanded
- **Status text**: Shows "Expanded" or "Collapsed" below expandable nodes
- **Hierarchy indentation**: Child nodes are slightly indented based on their level

## Implementation Details

### Node States
- `isExpanded`: Boolean indicating if the node is expanded (default: true)
- `hasChildren`: Boolean indicating if the node has child nodes
- `expandIcon`: Visual icon (▶ or ▼) for the expand/collapse button
- `statusText`: Text showing the current state ("Expanded" or "Collapsed")
- `hierarchyLevel`: Numeric level indicating depth in the hierarchy

### Automatic Detection
- The system automatically detects which nodes have children by analyzing edges
- Nodes are automatically marked as expandable when they have outgoing connections
- The expand/collapse button only appears on nodes that have children

### Edge Behavior
- Edges connected to hidden nodes are automatically hidden
- Edge visibility is managed automatically - no manual intervention needed
- Edges reappear when their connected nodes become visible

## Example Workflow
1. Create a workflow with multiple connected nodes
2. Notice that parent nodes automatically show expand/collapse buttons
3. Click the collapse button (▼) on a parent node
4. Observe that all child nodes and their edges disappear
5. Click the expand button (▶) to show direct children
6. Child nodes appear, but grandchildren remain hidden until their parents are expanded

This creates a clean, hierarchical view that helps manage complex workflows by allowing users to focus on specific parts of the diagram.
