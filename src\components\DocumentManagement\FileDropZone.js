import React, { Component } from "react";
import { Dropdown,But<PERSON> } from "react-bootstrap";

class FileDropZone extends Component {
  state = {
    isDragging: false,
  };

  handleDragEnter = (e) => {
    e.preventDefault();
    this.setState({ isDragging: true });
  };

  handleDragOver = (e) => {
    e.preventDefault();
  };

  handleDragLeave = () => {
    this.setState({ isDragging: false });
  };

  handleDrop = async (e) => {
    e.preventDefault();
    this.setState({ isDragging: false });

    const items = e.dataTransfer.items;
    const fileEntries = [];
    const files = [];

    // Process all items (files and folders)
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === 'file') {
        const entry = item.webkitGetAsEntry ? item.webkitGetAsEntry() : null;
        if (entry) {
          fileEntries.push(entry);
        } else {
          // Fallback for browsers that don't support webkitGetAsEntry
          const file = item.getAsFile();
          if (file) files.push(file);
        }
      }
    }

    // Process directory entries
    if (fileEntries.length > 0) {
      try {
        const allFiles = await this.processFileEntries(fileEntries);
        files.push(...allFiles);
      } catch (error) {
        console.error('Error processing folders:', error);
      }
    }

    if (files.length > 0 && this.props.onFilesDropped) {
      this.props.onFilesDropped(files);
    }
  };

  // Recursively process directory entries
  processFileEntries = async (entries, path = '') => {
    const files = [];
    
    for (const entry of entries) {
      const currentPath = path ? `${path}/${entry.name}` : entry.name;
      
      if (entry.isFile) {
        const file = await this.getFileFromEntry(entry, currentPath);
        files.push(file);
      } else if (entry.isDirectory) {
        const dirFiles = await this.readDirectory(entry, currentPath);
        files.push(...dirFiles);
      }
    }
    
    return files;
  };

  // Read directory contents
  readDirectory = async (directoryEntry, path) => {
    const files = [];
    const reader = directoryEntry.createReader();
    
    const readEntries = async () => {
      const entries = await new Promise((resolve) => {
        reader.readEntries(resolve, () => resolve([]));
      });
      
      if (entries.length === 0) return;
      
      const newFiles = await this.processFileEntries(entries, path);
      files.push(...newFiles);
      
      // Continue reading until no more entries
      await readEntries();
    };
    
    await readEntries();
    return files;
  };

  // Convert FileEntry to File object with proper path
  getFileFromEntry = (fileEntry, fullPath) => {
    return new Promise((resolve) => {
      fileEntry.file((file) => {
        // Create a new File object with the webkitRelativePath property
        const fileWithPath = new File([file], file.name, {
          type: file.type,
          lastModified: file.lastModified
        });
        
        // Set the path information
        Object.defineProperty(fileWithPath, 'webkitRelativePath', {
          value: fullPath,
          writable: false,
          enumerable: true
        });
        
        // Also add relativePath for backward compatibility
        fileWithPath.relativePath = fullPath;
        
        resolve(fileWithPath);
      });
    });
  };

  render() {
    const { isDragging } = this.state;

    return (
      <div
        style={{
          border: isDragging ? '2px dashed #4a90e2' : '2px dashed #ccc',
          borderRadius: '5px',
          padding: '20px',
          textAlign: 'center',
          backgroundColor: isDragging ? '#f0f7ff' : '#f9f9f9',
          transition: 'all 0.3s ease',
        }}
        onDragEnter={this.handleDragEnter}
        onDragOver={this.handleDragOver}
        onDragLeave={this.handleDragLeave}
        onDrop={this.handleDrop}
      >
        <p>Drag and drop files or folders here</p>
        {/* Hidden file input for files */}
        <input
          type="file"
          multiple
          style={{ display: 'none' }}
          ref={ref => (this.fileInputFilesRef = ref)}
          onChange={e => {
            const files = Array.from(e.target.files);
            if (files.length > 0 && this.props.onFilesDropped) {
              this.props.onFilesSelected(files);
            }
            e.target.value = null;
          }}
        />
        {/* Hidden file input for folders */}
        {/* <input
          type="file"
          multiple
          webkitdirectory="true"
          directory=""
          style={{ display: 'none' }}
          ref={ref => (this.fileInputFolderRef = ref)}
          onChange={e => {
            const files = Array.from(e.target.files);
            if (files.length > 0 && this.props.onFilesDropped) {
              this.props.onFilesDropped(files);
            }
            e.target.value = null;
          }}
        /> */}
        <Button
          variant="primary"
          onClick={() => this.fileInputFilesRef && this.fileInputFilesRef.click()}
          title="upload"
          className="me-1"
        >
          Upload a File
        </Button>
        {/* <Dropdown>
          <Dropdown.Toggle variant="primary" id="upload-dropdown">
            Upload
          </Dropdown.Toggle>
          <Dropdown.Menu>
            <Dropdown.Item onClick={() => this.fileInputFilesRef && this.fileInputFilesRef.click()}>
              <i className="fa fa-file" /> Files
            </Dropdown.Item>
            <Dropdown.Item onClick={() => this.fileInputFolderRef && this.fileInputFolderRef.click()}>
              <i className="fa fa-folder" /> Folder
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown> */}
      </div>
    );
  }
}

export default FileDropZone;