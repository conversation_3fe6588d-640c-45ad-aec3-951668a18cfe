.widgetCard {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.widgetCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.selectedWidget {
  border-color: #0d6efd !important;
  background-color: #f8f9ff;
}

.widgetIcon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.formContainer {
  max-width: 800px;
  margin: 0 auto;
}

.sectionTitle {
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
} 