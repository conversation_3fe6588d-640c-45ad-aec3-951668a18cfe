import React, { Component } from "react";
import classes from "./AuditLog.module.css";
import DatePicker from "react-datepicker";
import { format } from "date-fns";
import "react-datepicker/dist/react-datepicker.module.css";
import Notification from "../Notification/Notification";
import { getList, formatDate } from "../../services/apiService";
import axios from "../../services/api";
import { GlobalConstants } from "../../constants/global-constants";
import Loader from "../loader/Loader";
import { DataTable } from "../Table/DataTable";
import DownloadReport from '../common/DownloadReport';

class AuditLog extends Component {
  constructor(props) {
    super(props);
    this.searchTimeout = null;
    this.state = {
      notification: {
        message: "",
        type: "",
        show: false,
      },
      isLoading:false,
      showStartError: "",
      showEndError: "",
      userError:"",
      startDate: "",
      endDate: "",
      user: "",
      userList:[],
      objectTypes: ["All", "File", "Folder", "Link", "Tag", "Metadata", "Group"],
      sortColumn: "dateTime",
      sortDirection: "desc",
      // actions: [
      //   "Added",
      //   // "Create",
      //   // "Duplicate",
      //   "Delete",
      //   "Restore",
      //   "Rename",
      //   "Edit Note",
      //   // "Edit date",
      //   "Edit Due Date",
      //   "Added Due Date",
      //   // "OCR language",
      //   "Edit Document Number",
      //   "Download",
      //   "Download Preview",
      //   "Lock",
      //   "Unlock",
      //   "Move",
      //   "Add a version",
      //   "Remove a version",
      //   "Restore a version",
      //   "Retention end",
      //   // "Add a tag",
      //   // "Remove a tag",
      //   // "Add a signee",
      //   // "Remove a signee",
      //   "Add meta",
      //   "Remove meta",
      //   "Change meta value",
      //   "Document approval started",
      //   "Document approval cancelled",
      //   // "Document approved",
      //   "Document rejected",
      //   "User approved the document",
      //   "User rejected the document",
      //   "Document eSign started",
      //   "Document eSign cancelled",
      //   "Document eSign finished",
      //   "User eSign the document",
      //   "User acknowledged the document",
      //   "Document acknowledgement started",
      //   "Document acknowledgement cancelled",
      //   "Document approval finished",
      //   "Document eSign finished",
      // ],
      actions: [
        "Added file",
        "Added Link",
        "Deleted Link",
        "Folder uploaded",
        "Folder created",
        "Locked File",
        "Unlocked File",
        "Folder deleted",
        "Deleted File",
        "duplicate created",
        "Document name changed",
        "Added Due Date",
        "Meta Data updated",
        "Tags updated",
        "Document moved",
        "File Edited",
        "Added document mappings",
        "Edit Note",
        "Document approval assigned",
        "Document approval updated",
        "approved the document",
        "approval finished",
        "rejected the document",
        "Document eSign assigned",
        "Document eSign updated",
        "eSign finished",
        "eSign the document",
        "rejected eSign the document",
        "Document acknowledgement assigned",
        "Document acknowledgement updated",
        "acknowledgement finished",
        "acknowledged the document",
        "Added Retention",
        "Added reminder",
        "Restored Archived File",
        "Permanently Deleted File",
        "Restored File",
        "Folder name changed",
        "Added shared to",
        "Shared a folder",
        "New Version",
        "Version Deleted",
        "Version Restored",
      ],
      selectedObjectType: "",
      selectedActions: "",
      fileName: "",
      logList: [],
      currentPage: 0,
      totalItems: 0,
      itemsPerPage: 50,
      searchTerm: "",
      currentFilter:null,
    };
  }

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true,
      },
    });
  };

  onHandleChange = (event) => {
    this.setState({ selectedValue: event.target.value });
  };

  onHandleObjectTypeChange = (event) => {
    this.setState({ selectedObjectType: event.target.value });
  };

  onHandleActionsChange = (event) => {
    this.setState({ selectedActions: event.target.value });
  };

  componentDidMount() {
    this.fetchAuditLogs();
    this.fetchUserList();
  }

  fetchUserList = async () => {
    const api = `/user/list?page=0&size=100&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        userList: data.content,
      });
    } catch (error) {
        this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      }
    };

  componentWillUnmount() {
    // Clear the search timeout if it exists
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.setState({ logList: [] });
  }

  fetchAuditLogs = async (page = 0, itemsPerPage = this.state.itemsPerPage) => {
    const filter = {
      startDate: this.state.startDate,
      endDate: this.state.endDate,
      user: this.state.user,
      // objectType: this.state.selectedObjectType ,
      actions: this.state.selectedActions,
      fileName: this.state.fileName,
      searchTerm: this.state.searchTerm,
    };
    this.setState({isLoading:true, currentFilter:filter})

    const api = `/logs-history/getlogslist?page=${page}&size=${itemsPerPage}`;

    try {
      this.componentWillUnmount();
      const response = await getList(api, filter);
      const data = response.data;
      //console.log(data.content, "Filtered Logs");

      this.setState({
        logList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading:false,
      });
    } catch (error) {
      console.log(error);
      this.setState({isLoading:false,});
      // this.setState({
      //   notification: {
      //     message: "Something went Wrong",
      //     type: "error",
      //     show: true,
      //   },
      // });
    }
  };

  handlePageChange = (page) => {
    this.fetchAuditLogs(page);
  };

  handleItemsPerPageChange = (newSize) => {
    // Always reset to first page when changing items per page
    this.setState({
      itemsPerPage: newSize,
      currentPage: 0  // Reset to first page
    }, () => {
      this.fetchAuditLogs(0, newSize);
    });
  };

  exportToExcel = async (event) => {
    event.preventDefault();

    const filter = {
      startDate: this.state.startDate,
      endDate: this.state.endDate,
      user: this.state.user,
      // objectType: this.state.selectedObjectType ,
      actions: this.state.selectedActions,
      fileName: this.state.fileName,
    };
    const api = `${GlobalConstants.globalURL}/documentreports/docLogs/exportAllExcel`;
    console.log(api);

    try {
      const response = await axios.post(api, filter, {
        responseType: "blob", // Specify that we want a blob response
        headers: {
          Accept:
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // Excel MIME type
        },
      });

      // Check if the response is valid
      if (response.data.size === 0) {
        throw new Error("Received an empty response from the server.");
      }

      // Use a fallback for content type
      const contentType =
        response.headers["content-type"] ||
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      // Create a blob link to download the Excel file
      const blob = new Blob([response.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `documentLogAll.xls`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link); // Clean up
    } catch (error) {
      console.error("Error downloading Excel:", error.message);
    }
  };

  exportToPDF = async (event) => {
    event.preventDefault();

    const filter = {
      startDate: this.state.startDate,
      endDate: this.state.endDate,
      user: this.state.user,
      // objectType: this.state.selectedObjectType ,
      actions: this.state.selectedActions,
      fileName: this.state.fileName,
    };
    const api = `${GlobalConstants.globalURL}/documentreports/docLogs/exportAllPDF`;

    try {
      const response = await axios.post(api, filter, {
        responseType: "blob",
        headers: {
          Accept: "application/pdf", // Add Accept header
        },
      });
      console.log(response);
      // Create a blob link to download the PDF
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `documentLogAll.pdf`);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
    } catch (error) {
      console.error("Error downloading PDF:", error);
    }
  };

  handleStartDateChange = (date) => {
    this.setState({ showStartError: "" });
    if (!date) {
      this.setState({ startDate: null });
      return;
    }
    const formattedDate = format(date, "yyyy-MM-dd");

    const { endDate } = this.state;
    if (endDate && date > endDate) {
      console.log("Start date cannot be after the end date");
    } else {
      this.setState({ startDate: formattedDate });
    }
  };

  handleEndDateChange = (date) => {
    this.setState({ showEndError: "" });
    if (!date) {
      this.setState({ endDate: null });
      return;
    }
    const formattedDate = format(date, "yyyy-MM-dd");
    this.setState({ endDate: formattedDate });
  };

  handleUser = (event) => {
    this.setState({ user: event.target.value,userError:"" });
  };

  handleObjectChange = (event) => {
    this.setState({ fileName: event.target.value });
  };

  applyFilter = async () => {
    const {startDate,endDate,user}=this.state;
    if(startDate === "" || endDate === ""){
      if(startDate === ""){
        this.setState({
          showStartError: "Start Date is required",
        });
      }
      if(endDate === ""){
        this.setState({
          showEndError: "End Date is Required",
        });
      }
      // if(user === ""){
      //   this.setState({
      //     userError: "User is required"
      //   });
      // }
      return;
    }
    await this.fetchAuditLogs(0, this.state.itemsPerPage);
  };

  clearFilter = () => {
    this.setState(
      {
        startDate: "",
        endDate: "",
        user: "",
        selectedValue: "",
        fileName: "",
        selectedActions: "",
        showStartError: "",
        showEndError: "",
        userError:"",
        logList: [],
      },
      () => this.fetchAuditLogs()
    );
  };
  
  // Handle search term changes with debouncing
  handleSearchTermChange = (searchTerm) => {
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    
    // Update the search term immediately for UI feedback
    this.setState({ searchTerm });
    
    // Set a timeout to actually perform the search after typing stops
    this.searchTimeout = setTimeout(() => {
      if (this.state.startDate && this.state.endDate) {
        this.fetchAuditLogs(0, this.state.itemsPerPage);
      }
    }, 500); // 500ms debounce time
  };

  handleSort = (column) => {
    console.log('Sorting by:', column);
    const { sortColumn, sortDirection, logList } = this.state;
    
    // Map columns to their actual object keys
    const columnKeyMap = {
      'sno': 'sno',
      'dateTime': 'dateTime',
      'createdBy': 'createdBy',
      'entityType': 'entityType',
      'fileName': 'fileName',
      'logMessage': 'logMessage'
    };
    
    const key = columnKeyMap[column];
    let newDirection = "asc";
    
    if (sortColumn === column && sortDirection === "asc") {
      newDirection = "desc";
    }
    
    // Create a copy of the data to sort
    const sortedList = [...logList].sort((a, b) => {
      let aValue, bValue;
      
      // Special handling for date columns
      if (column === "dateTime") {
        aValue = new Date(a.modifiedDate || a.createdDate).getTime();
        bValue = new Date(b.modifiedDate || b.createdDate).getTime();
      } 
      // Special handling for sno column
      else if (column === "sno") {
        const aIndex = logList.indexOf(a);
        const bIndex = logList.indexOf(b);
        
        if (newDirection === "asc") {
          // For ascending order, smaller indices come first
          aValue = aIndex;
          bValue = bIndex;
        } else {
          // For descending order, larger indices come first
          aValue = -aIndex;
          bValue = -bIndex;
        }
      } 
      else {
        // For other columns, use the string value
        aValue = String(a[key] || '').toLowerCase();
        bValue = String(b[key] || '').toLowerCase();
      }
      
      if (aValue < bValue) return newDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return newDirection === "asc" ? 1 : -1;
      return 0;
    });
    
    this.setState({
      logList: sortedList,
      sortColumn: column,
      sortDirection: newDirection,
    });
  };

  render() {
    const {
      startDate,
      endDate,
      user,
      fileName,
      actions,
      logList,
    } = this.state;

    const columns = [
      {
        key: 'sno',
        header: 'SNo.',
        sortable: false, // Serial numbers are just visual aids, sorting by them doesn't provide meaningful data ordering
        render: (_, __, index) => index + 1,
        width: '60px'
      },
      {
        key: 'dateTime',
        header: 'Date & Time',
        sortable: true,
        render: (value, row) => row.dateTimeFormatted || (row.modifiedDate ? formatDate(row.modifiedDate) : formatDate(row.createdDate)),
        sortValue: (row) => new Date(row.modifiedDate || row.createdDate).getTime(),
        width: '220px' // Increased width to accommodate full date/time format
      },
      {
        key: 'createdBy',
        header: 'User',
        sortable: true,
        render: (value, row) => row.createdByStr,
        sortValue: (row) => String(row.createdBy || '').toLowerCase(),
        width: '120px'
      },
      {
        key: 'entityType',
        header: 'Type',
        sortable: true,
        render: (value, row) => row.entityTypeStr,
        sortValue: (row) => String(row.entityType || '').toLowerCase(),
        width: '100px'
      },
      {
        key: 'fileName',
        header: 'Object Name',
        sortable: true,
        render: (value, row) => row.fileNameStr,
        sortValue: (row) => String(row.fileName || '').toLowerCase(),
        width: '200px'
      },
      {
        key: 'logMessage',
        header: 'Action',
        sortable: true,
        render: (value, row) => row.logMessageStr,
        sortValue: (row) => String(row.logMessage || '').toLowerCase(),
        width: '150px'
      },
    ];

    // Process log list for DataTable - ensure all required fields are properly mapped
    const tableData = logList.map(log => ({
      ...log, // Spread first so explicit keys below take precedence
      dateTime: log.modifiedDate || log.createdDate,
      createdBy: log.createdBy || '',
      entityType: log.entityType || '',
      fileName: log.fileName || '',
      logMessage: log.logMessage || '',
      // Add explicit string conversion for proper filtering
      createdByStr: String(log.createdBy || ''),
      entityTypeStr: String(log.entityType || ''),
      fileNameStr: String(log.fileName || ''),
      logMessageStr: String(log.logMessage || ''),
      // Add formatted date string for search
      dateTimeFormatted: log.modifiedDate ? formatDate(log.modifiedDate) : formatDate(log.createdDate),
      // Add searchable fields for better filtering
      searchableText: [
        log.modifiedDate ? formatDate(log.modifiedDate) : formatDate(log.createdDate),
        String(log.createdBy || ''),
        String(log.entityType || ''),
        String(log.fileName || ''),
        String(log.logMessage || '')
      ].join(' ').toLowerCase()
    }));

    return (
      <div className="container mt-3">
        <div className="row d-flex justify-content-between">
          <div className="col-sm-4">
            <h4>Global Audit Log</h4>
          </div>
          <div className="col-sm-3">
            {this.state.notification.show && (
              <Notification
                message={this.state.notification.message}
                type={this.state.notification.type}
                onClose={this.closeNotification}
              />
            )}
            <DownloadReport
              excelEndpoint={`${GlobalConstants.globalURL}/documentreports/docLogs/exportAllExcel`}
              pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/docLogs/exportAllPDF`}
              reportName="GlobalAuditLog"
              filter={this.state.currentFilter} 
              onError={this.handleDownloadError}
            />
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <form className="row g-3 align-items-end">
              <div className="col-md-2">
                <label htmlFor="startDate">
                  Start Date <span className="text-danger">*</span>
                </label>
                <DatePicker
                  className="form-control"
                  selected={startDate}
                  onChange={this.handleStartDateChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="Start Date"
                  maxDate={endDate}
                />
                {this.state.showStartError && (
                  <div className={classes.errorMessage}>
                    {this.state.showStartError}
                  </div>
                )}
              </div>

              <div className="col-md-2">
                <label htmlFor="endDate">
                  End Date <span className="text-danger">*</span>
                </label>
                <DatePicker
                  className="form-control"
                  selected={endDate}
                  onChange={this.handleEndDateChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="End Date"
                  minDate={startDate}
                />
                {this.state.showEndError && (
                  <div className={classes.errorMessage}>
                    {this.state.showEndError}
                  </div>
                )}
              </div>

              {/* <div className="col-md-2">
                <label htmlFor="user">User</label>
                <input
                  type="text"
                  id="user"
                  value={user}
                  className="form-control"
                  onChange={this.handleUser}
                />
              </div> */}

              <div className="col-md-2">
                  <label htmlFor="user">User <span className="text-danger">*</span></label>
                  <select
                    id="user"
                    value={user}
                    className="form-control"
                    onChange={this.handleUser}
                  >
                    <option selected>-- Select User --</option>
                    <option value="<EMAIL>">Admin</option>
                    {this.state.userList &&
                      this.state.userList.map((user, index) => (
                        <option key={index} value={user.userName}>
                          {user.firstName} {user.lastName}
                        </option>
                      ))}
                  </select>
                  {this.state.userError && (
                  <div className={classes.errorMessage}>
                    {this.state.userError}
                  </div>
                )}
                </div>

              <div className="col-md-2">
                <label htmlFor="actions">Actions</label>
                <select
                  id="actions"
                  className="form-control"
                  value={this.state.selectedActions}
                  onChange={this.onHandleActionsChange}
                >
                  <option value="" disabled>
                    Select an Option
                  </option>
                  {actions.map((item, index) => (
                    <option key={index} value={item}>
                      {item}
                    </option>
                  ))}
                </select>
              </div>

              <div className="col-md-2">
                <label htmlFor="fileName">File Name</label>
                <input
                  type="text"
                  className="form-control"
                  value={fileName}
                  id="fileName"
                  placeholder="File Name"
                  onChange={this.handleObjectChange}
                />
              </div>

              <div className="col-md-2">
                <button
                  type="button"
                  className="btn btn-primary me-2"
                  onClick={this.applyFilter}
                >
                  Filter
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={this.clearFilter}
                >
                  Clear
                </button>
              </div>
            </form>
          </div>
        </div>

        <div className="row mt-5">
          <div className="col-12">
            {this.state.isLoading ? (
              <Loader />
            ) : (
              <DataTable
                data={tableData}
                columns={columns}
                searchable={true}
                itemsPerPage={this.state.itemsPerPage}
                totalItems={this.state.totalItems}
                currentPage={this.state.currentPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                onSearch={this.handleSearchTermChange}
                searchTerm={this.state.searchTerm}
                className="audit-log-table"
                onSort={this.handleSort}
                sortColumn={this.state.sortColumn}
                sortDirection={this.state.sortDirection}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
}

export default AuditLog;
