import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { DataTable } from "../Table/DataTable";
import {
  addNew,
  deleteById,
  getList,
  editById,
} from "../../services/apiService";
import Notification from "../Notification/Notification";

class Tags extends React.Component {
  state = {
    isOpen: false,
    isEditing: false,
    tagList: [],
    notification: {
      message: "",
      type: "",
      show: false,
    },
    newTag: {
      fieldName: "",
      fieldDesc: "",
      fieldType: "boolean",
      type: "tag",
      multiple: false,
    },
    showDeleteConfirm: false,
    tagIdToDelete: null,
    typeArray: ["boolean", "string", "number", "date"],
    itemsPerPage: 10,
    currentPage: 0,
    searchTerm: "",
    error:"",
  };

  componentDidMount() {
    this.fetchTagList();
  }

  fetchTagList = async () => {
    const api = `/metadata/list?type=tag`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({
        tagList: response.data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  handleInputChange = (e) => {
    const { name, type, checked, value } = e.target;
    this.setState((prevState) => ({
      error:"",
      newTag: {
        ...prevState.newTag,
        [name]: type === "checkbox" ? checked : value, // Handle checkbox correctly
      },
    }));
  };

  createTag = async (newTag) => {
    console.log("New Tag Submitted:", newTag);
    const api = `/metadata`;
    try {
      const response = await addNew(api, newTag);
      this.setState({
        notification: {
          message: `Tag (${newTag.fieldName}) created successfully`,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
      throw error;
    }
  };

  editTag = async (tagId, updatedTag) => {
    const api = `/metadata/${tagId}`;
    console.log(api);
    console.log("Updated Tag submitted : ", updatedTag);
    try {
      const response = await editById(api, updatedTag);
      this.setState({
        notification: {
          message: `Tag (${updatedTag.fieldName}) updated successfully`,
          type: "success",
          show: true,
        },
      });
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();
    const { newTag, isEditing, tagList } = this.state;
    const isDuplicate = tagList.some(item => 
      item.fieldName.toLowerCase() === newTag.fieldName.toLowerCase().trim() &&
        (!isEditing || item.id !== newTag.id) 
    );
    
    if (isDuplicate) {
      this.setState({
        error: `${newTag.fieldName} already exists as tag!`,
      });
      return;
    }

    try {
      if (isEditing) {
        await this.editTag(newTag.id, newTag);
      } else {
        await this.createTag(newTag);
      }
      this.fetchTagList();
      this.closeModal();
    } catch (error) {
      console.log(error);
    }
    this.closeModal();
  };

  openDeleteConfirm = (tagId) => {
    this.setState({ showDeleteConfirm: true, tagIdToDelete: tagId });
  };

  closeDeleteConfirm = () => {
    this.setState({ showDeleteConfirm: false, tagIdToDelete: null });
  };

  confirmDelete = async () => {
    const id = this.state.tagIdToDelete;
    const api = `/metadata/${id}`;
    try {
      const response = await deleteById(api);
      this.setState({
        notification: {
          message: response.message,
          type: "success",
          show: true,
        },
      });
      this.fetchTagList();
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    } finally {
      this.closeDeleteConfirm();
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openModal = (tag = null) => {
    if (tag) {
      this.setState({
        isOpen: true,
        isEditing: true,
        newTag: { ...tag },
      });
    } else {
      this.setState({
        isOpen: true,
        isEditing: false,
        newTag: {
          fieldName: "",
          fieldDesc: "",
          fieldType: "boolean",
          type: "tag",
          multiple: false,
        },
      });
    }
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      isEditing: false,
      newTag: {
        fieldName: "",
        fieldDesc: "",
        fieldType: "boolean",
        type: "tag",
        multiple: false,
      },
    });
  };

  handleItemsPerPageChange = (newItemsPerPage) => {
    this.setState({ 
      itemsPerPage: newItemsPerPage,
      currentPage: 0 // Reset to first page when changing items per page
    });
  };

  handlePageChange = (page) => {
    this.setState({ currentPage: page });
  };

  handleSearch = (searchTerm) => {
    this.setState({ 
      searchTerm: searchTerm,
      currentPage: 0 // Reset to first page when searching
    });
  };



  render() {
    const { typeArray, tagList, currentPage, itemsPerPage, searchTerm } = this.state;
    
    // Filter data based on search term
    let filteredData = tagList;
    if (searchTerm) {
      filteredData = tagList.filter(tag => 
        tag.fieldName && tag.fieldName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Calculate paginated data
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = filteredData.slice(startIndex, endIndex);
    
    return (
      <div className="container-fluid mt-3">
        <div className="row text-center">
          <div className="col-12">
            <h4>Tag Management</h4>
            <hr />
          </div>
        </div>
        
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        {/* Tag Content */}
        <div className="row mt-3">
          <div className="col-12">
            <div className="d-flex align-items-center mb-4">
              <h4 className="mb-0">Manage Custom Tags</h4>
              <Button
                variant="primary"
                className="ms-3"
                onClick={() => this.openModal()}
              >
                Add Tag
              </Button>
            </div>

            <div className="mt-4">
              <DataTable
                data={paginatedData}
                totalItems={filteredData.length}
                columns={[
                  {
                    key: "fieldName",
                    header: "Tag Name",
                    sortable: true,
                    width: "70%"
                  },
                  {
                    key: "actions",
                    header: "Actions",
                    width: "30%",
                    render: (_, row) => (
                      <div className="d-flex justify-content-start">
                        <Button
                          title="Edit tag"
                          variant="primary"
                          size="sm"
                          className="me-2"
                          onClick={() => this.openModal(row)}
                        >
                          <i className="fa fa-edit"></i>
                        </Button>
                        <Button
                          title="Delete tag"
                          variant="danger"
                          size="sm"
                          onClick={() => this.openDeleteConfirm(row.id)}
                        >
                          <i className="fa fa-trash"></i>
                        </Button>
                      </div>
                    )
                  }
                ]}
                searchable={true}
                searchTerm={this.state.searchTerm}
                onSearch={this.handleSearch}
                itemsPerPage={this.state.itemsPerPage}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                currentPage={this.state.currentPage}
                onPageChange={this.handlePageChange}
                className="table-striped"
                showSno={true}
              />
            </div>
          </div>
        </div>

        {/* add modal */}
        <div>
          <Modal
            show={this.state.isOpen}
            onHide={this.closeModal}
            centered
            size="md"
          >
            <Modal.Header
              className="modal-header-modern"
              closeButton
            >
              <Modal.Title style={{fontSize:'16px', color:'#111', textAlign:'center', width:'100%', margin:0}}>
                {this.state.isEditing ? "Edit Tag" : "New Tag"}
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <form onSubmit={this.handleSubmit}>
                <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <input
                        type="text"
                        id="name"
                        name="fieldName"
                        className="form-control"
                        value={this.state.newTag.fieldName}
                        required
                        placeholder="Tag Name"
                        onChange={this.handleInputChange}
                      />
                    </div>
                  </div>
                  <span className="text-danger"> {this.state.error}</span>
                </div>

                {/* <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <label htmlFor="description" className="form-label">Description</label>
                      <input
                        type="text"
                        id="description"
                        name="fieldDesc"
                        className="form-control"
                        value={this.state.newTag.fieldDesc || ""}
                        placeholder="Field Description"
                        onChange={this.handleInputChange}
                      />
                    </div>
                  </div>
                </div> */}

                {/* <div className="container my-3">
                  <div className="row">
                    <div className="col-12">
                      <label htmlFor="type" className="form-label">Field Type</label>
                      <select
                        id="type"
                        name="fieldType"
                        className="form-control"
                        value={this.state.newTag.fieldType}
                        onChange={this.handleInputChange}
                      >
                        {this.state.typeArray.map((type, index) => (
                          <option key={index} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div> */}

                <div className="container my-3">
                  <div className="row">
                    {/* <div className="col-8">
                      <div className="form-check">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          checked={this.state.newTag.multiple}
                          id="multiple"
                          name="multiple"
                          onChange={this.handleInputChange}
                        />
                        <label className="form-check-label" htmlFor="multiple">
                          Allow using multiple times for the same file
                        </label>
                      </div>
                    </div> */}

                    <div className="col-4 text-end">
                      <Button type="submit" variant="primary">
                        {this.state.isEditing ? "Update" : "Submit"}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </Modal.Body>
          </Modal>
        </div>
        {/* Delete Modal */}
        <div>
          <Modal
            show={this.state.showDeleteConfirm}
            onHide={this.closeDeleteConfirm}
            centered
          >
            <Modal.Header
              closeButton
              className="modal-header-modern"
            >
              <Modal.Title style={{ fontSize: "18px", color: "white" }}>
                Confirm Deletion
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>Are you sure you want to delete this tag?</Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.closeDeleteConfirm}>
                Cancel
              </Button>
              <Button variant="danger" onClick={this.confirmDelete}>
                Delete
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>
    );
  }
}

export default Tags;
