import React from 'react';
import { useParams } from 'react-router-dom';


const HomeChild = () => {
  const { id } = useParams(); // Get `id` from URL

  // Conditionally render based on `id`
  switch (id) {
    case 'duplicate':
        return <div>Duplicate Component</div>;
    case 'pendingworkflows':
        return <div>Pending WorkFlows Component</div>;
    case 'retention':
        return <div>Retention component</div>;
    case 'duedate':
        return <div>DueDate component</div>;
    case 'allfiles':
        return <div>All Files component</div>;
    case 'archivedfiles':
        return <div>Archive Files component</div>;
        
    default:
      return <div>Child component</div>;
  }
};

export default HomeChild;