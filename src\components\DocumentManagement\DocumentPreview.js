import React, { Component } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "react-file-viewer";
import { PDFDocument, rgb } from "pdf-lib";
import mammoth from "mammoth";
import * as XLSX from "xlsx";
import { jsPDF } from "jspdf";
import { renderAsync } from "docx-preview";
import { init } from "pptx-preview";

class DocumentPreview extends Component {
  constructor(props) {
    super(props);
    this.state = {
      pdfUrl: null,
      error: null,
      docxContent: null,
    };
    this.docxContainerRef = React.createRef();
    this.pptxContainerRef = React.createRef();
  }

  componentDidMount() {
    const { filePath, fileName } = this.props;
    const fileType = fileName
      .substring(fileName.lastIndexOf(".") + 1)
      .toLowerCase();
    console.log("File Type:", fileType);

    if (fileType === "docx") {
      this.previewDocx(filePath);
    } else if (fileType === "doc") {
      this.setState({
        error:
          "Preview for .doc files is not supported. Please convert to .docx.",
      });
    } else if (fileType === "xls" || fileType === "xlsx") {
      this.previewExcel(filePath);
    } else if (fileType === "pptx" || fileType === "ppt") {
      this.previewPptx(filePath);
    } else if (fileType !== "pdf") {
      this.convertToPdf(filePath, fileType);
    } else {
      this.setState({ pdfUrl: filePath });
    }
  }

  previewPptx = async (filePath) => {
    try {
      const blob = await fetch(filePath).then((res) => res.blob());
      const arrayBuffer = await blob.arrayBuffer();

      if (this.pptxContainerRef.current) {
        // Initialize the PPTX preview
        await init(this.pptxContainerRef.current);

        // Render the PPTX file
        init.render(new Uint8Array(arrayBuffer), this.pptxContainerRef.current);
      }
    } catch (error) {
      console.error("Error previewing PPTX:", error);
      this.setState({
        error:
          "PPTX preview is not supported. Please download the file to view it.",
      });
    }
  };

  previewDocx = async (filePath) => {
    try {
      console.log("Rendering DOCX file...");
      const blob = await fetch(filePath).then((res) => res.blob());
      const arrayBuffer = await blob.arrayBuffer();
      console.log(arrayBuffer);
      if (this.docxContainerRef.current) {
        await renderAsync(arrayBuffer, this.docxContainerRef.current);
      }
    } catch (error) {
      console.error("Error previewing DOCX:", error);
      this.setState({ error: "Failed to preview DOCX file." });
    }
  };

  previewExcel = async (filePath) => {
    try {
      console.log("Rendering Excel file...");
      const blob = await fetch(filePath).then((res) => res.arrayBuffer());
      const workbook = XLSX.read(blob, { type: "array" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const html = XLSX.utils.sheet_to_html(worksheet, { editable: false });

      this.setState({ docxContent: html });
    } catch (error) {
      console.error("Error previewing Excel file:", error);
      this.setState({ error: "Failed to preview Excel file." });
    }
  };

  extractTextFromDoc = async (arrayBuffer) => {
    try {
      // Convert the ArrayBuffer to a binary string
      const binaryString = String.fromCharCode.apply(
        null,
        new Uint8Array(arrayBuffer)
      );

      // Extract plain text from the binary string
      // This is a very basic implementation and may not work for all .doc files
      const text = binaryString.replace(/[^ -~]+/g, ""); // Remove non-printable characters
      return text;
    } catch (error) {
      console.error("Error extracting text from DOC:", error);
      throw error;
    }
  };

  convertToPdf = async (filePath, fileType) => {
    console.log("Converting file to PDF. File Type:", fileType);
    console.log("File Path (Blob URL):", filePath);

    try {
      // Convert the Blob URL to a Blob object
      const blob = await fetch(filePath).then((response) => response.blob());

      // Convert the Blob to an ArrayBuffer
      const arrayBuffer = await blob.arrayBuffer();

      // Log the ArrayBuffer to verify its content
      console.log("ArrayBuffer:", arrayBuffer);

      let pdfBytes;
      switch (fileType) {
        case "doc":
        case "docx":
          pdfBytes = await this.convertDocxToPdf(arrayBuffer);
          break;
        case "xls":
        case "xlsx":
          pdfBytes = await this.convertExcelToPdf(arrayBuffer);
          break;
        case "ppt":
        case "pptx":
          pdfBytes = await this.convertPptxToPdf(arrayBuffer);
          break;
        case "png":
        case "jpg":
        case "jpeg":
          pdfBytes = await this.convertImageToPdf(arrayBuffer, fileType);
          break;
        case "txt":
          pdfBytes = await this.convertTextToPdf(arrayBuffer);
          break;
        default:
          throw new Error("Unsupported file type");
      }

      // Create a Blob from the PDF bytes
      const pdfBlob = new Blob([pdfBytes], { type: "application/pdf" });

      // Generate a URL for the PDF Blob
      const pdfUrl = URL.createObjectURL(pdfBlob);
      this.setState({ pdfUrl });
    } catch (error) {
      this.setState({ error: "Failed to convert file to PDF." });
      console.error("Conversion error:", error);
    }
  };

  convertDocxToPdf = async (arrayBuffer) => {
    try {
      console.log("Converting DOCX to PDF...");

      // Log the arrayBuffer to verify its content
      console.log("ArrayBuffer:", arrayBuffer);

      // Convert DOCX to HTML using mammoth
      const { value: html } = await mammoth.convertToHtml({ arrayBuffer });
      console.log("HTML content:", html);

      // Create a new jsPDF instance
      const doc = new jsPDF();

      // Render HTML into the PDF
      await doc.html(html, {
        callback: (pdf) => pdf.output("arraybuffer"),
        margin: [10, 10, 10, 10],
        autoPaging: "text",
      });

      // Return the PDF as an ArrayBuffer
      return doc.output("arraybuffer");
    } catch (error) {
      console.error("Error converting DOCX to PDF:", error);

      // Fallback: Convert the file to plain text
      console.log("Falling back to plain text conversion.");
      return await this.convertTextToPdf(arrayBuffer);
    }
  };

  convertExcelToPdf = async (arrayBuffer) => {
    try {
      const workbook = XLSX.read(arrayBuffer, { type: "array" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const html = XLSX.utils.sheet_to_html(worksheet);

      const doc = new jsPDF();
      await doc.html(html, {
        callback: (pdf) => pdf.output("arraybuffer"),
        margin: [10, 10, 10, 10],
        autoPaging: "text",
      });
      return doc.output("arraybuffer");
    } catch (error) {
      console.error("Error converting Excel to PDF:", error);
      throw error;
    }
  };

  convertPptxToPdf = async (arrayBuffer) => {
    try {
      // PPTX conversion is not supported in this example.
      // You can use a server-side solution or a library like `pdf-lib` to handle PPTX files.
      throw new Error("PPTX conversion is not supported in this example.");
    } catch (error) {
      console.error("Error converting PPTX to PDF:", error);
      throw error;
    }
  };

  convertImageToPdf = async (arrayBuffer, fileType) => {
    try {
      const pdfDoc = await PDFDocument.create();
      const image =
        fileType === "png"
          ? await pdfDoc.embedPng(arrayBuffer)
          : await pdfDoc.embedJpg(arrayBuffer);
      const page = pdfDoc.addPage([image.width, image.height]);
      page.drawImage(image, {
        x: 0,
        y: 0,
        width: image.width,
        height: image.height,
      });
      return await pdfDoc.save();
    } catch (error) {
      console.error("Error converting image to PDF:", error);
      throw error;
    }
  };

  convertTextToPdf = async (arrayBuffer) => {
    try {
      const text = new TextDecoder("utf-8").decode(arrayBuffer);

      // Create a new jsPDF instance
      const doc = new jsPDF();

      // Set margins and line height
      const margin = 10; // Margin in mm
      const lineHeight = 10; // Line height in mm
      const maxWidth = doc.internal.pageSize.getWidth() - 2 * margin; // Maximum width for text

      // Split the text into lines that fit within the page width
      const lines = doc.splitTextToSize(text, maxWidth);

      // Add each line to the PDF
      let y = margin; // Starting Y position
      lines.forEach((line) => {
        if (y + lineHeight > doc.internal.pageSize.getHeight() - margin) {
          // Add a new page if the current line exceeds the page height
          doc.addPage();
          y = margin; // Reset Y position for the new page
        }
        doc.text(line, margin, y);
        y += lineHeight; // Move to the next line
      });

      // Return the PDF as an ArrayBuffer
      return doc.output("arraybuffer");
    } catch (error) {
      console.error("Error converting text to PDF:", error);
      throw error;
    }
  };

  render() {
    const { fileName, closePreview } = this.props;
    const { pdfUrl, error, docxContent } = this.state;
    const fileType = fileName
      .substring(fileName.lastIndexOf(".") + 1)
      .toLowerCase();

    if (error) {
      return <div className="text text-danger">Error: {error}</div>;
    }

    if (fileType === "docx") {
      return (
        <div
          ref={this.docxContainerRef}
          style={{ width: "100%", height: "500px", overflow: "auto" }}
        ></div>
      );
    }

    if (fileType === "xls" || fileType === "xlsx") {
      return (
        <div
          style={{ width: "100%", height: "500px", overflow: "auto" }}
          dangerouslySetInnerHTML={{ __html: docxContent }}
        ></div>
      );
    }

    if (fileType === "pptx" || fileType === "ppt") {
      return (
        <div
          ref={this.pptxContainerRef}
          style={{ width: "100%", height: "500px", overflow: "auto" }}
        ></div>
      );
    }

    if (!pdfUrl) {
      return <div>Loading...</div>;
    }

    return (
      <div>
        <FileViewer fileType="pdf" filePath={pdfUrl} onError={console.error} />
      </div>
    );
  }
}

export default DocumentPreview;
