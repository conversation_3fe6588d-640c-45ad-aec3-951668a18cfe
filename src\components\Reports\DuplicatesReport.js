import React, { Component } from "react";
import Loader from "../loader/Loader";
import Notification from "../Notification/Notification";
import { getDisplayPath, getList } from "../../services/apiService";
import { formatDate } from "../../services/apiService";
import { Modal, Button } from "react-bootstrap";
import axios from "../../services/api";
import { GlobalConstants } from "../../constants/global-constants";
import DownloadReport from "../common/DownloadReport";
import FileDetail from "../DocumentManagement/FileDetail";
import { DataTable } from "../Table/DataTable";
import CustomBreadcrumb from "../common/CustomBreadcrumb";
import { format, isBefore } from "date-fns";
import DatePicker from "react-datepicker";

class DuplicatesReport extends Component {
  state = {
    duplicateFiles: [],
    isLoading: false,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    showColumns: false,
    expandedRows: {},
    isDeleteModalOpen: false,
    fileIdToDelete: null,
    showFileDetail: false,
    selectedFile: null,
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    deployedURL: "",
    expandedFile: null,
    duplicateDetails: [],
    startDate: "",
    endDate: "",
    currentFilter:null,
  };

  componentDidMount() {
    const today = new Date().toISOString().split("T")[0];
    this.setState({
      startDate: today,
      endDate: today,
    });
    // this.fetchDuplicateDocuments();
    const deployedURL = window.location.href.split("/#")[0];
    this.setState({ deployedURL: deployedURL });
  }

  fetchDuplicateDocuments = async (
    filter,
    page = 0,
    itemsPerPage = this.state.itemsPerPage
  ) => {
    const api = `/documentreports/list_by_duplicates_count?page=${page}&size=${itemsPerPage}`;
    this.setState({ isLoading: true, currentFilter: filter });
    try {
      const response = await getList(api, filter);
      const data = response.data;
      this.setState({
        duplicateFiles: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        itemsPerPage,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    const { startDate, endDate } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
    };
    this.fetchDuplicateDocuments(filter, page);
  };

  handleItemsPerPageChange = (newSize) => {
    const { startDate, endDate } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
    };
    this.setState({ itemsPerPage: newSize }, () => {
      this.fetchDuplicateDocuments(filter, 0, newSize);
    });
  };

  handleStartDateChange = (date) => {
    const formattedDate = format(date, "yyyy-MM-dd");

    const { endDate } = this.state;
    if (endDate && date > new Date(endDate)) {
      console.log("Start date cannot be after the end date");
    } else {
      this.setState({ startDate: formattedDate });
    }
  };

  handleEndDateChange = (date) => {
    this.setState({ showEndError: "" });
    if (!date) {
      this.setState({ endDate: null });
      return;
    }
    const formattedDate = format(date, "yyyy-MM-dd");

    const { startDate } = this.state;
    if (startDate && isBefore(date, new Date(startDate))) {
      console.log("End date cannot be before the start date");
    } else {
      this.setState({ endDate: formattedDate });
    }
  };

  applyFilter = () => {
    this.setState({ duplicateFiles: [] });
    const { startDate, endDate } = this.state;
    const filter = {
      startDate: startDate,
      endDate: endDate,
    };
    this.fetchDuplicateDocuments(filter);
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openDeleteModal = (file) => {
    this.setState({
      isDeleteModalOpen: true,
      fileIdToDelete: file.documentsAttachmentId,
    });
  };

  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false, fileIdToDelete: null });
  };

  deleteFile = async () => {
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/${this.state.fileIdToDelete}`;
    this.setState({ isLoading: true });
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Deleted Successfully",
          type: "success",
          show: true,
        },
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
    this.fetchDuplicateDocuments();
    this.closeDeleteModal();
  };

  permanentDeleteFile = async () => {
    let api = `${GlobalConstants.globalURL}/documentsattachmentdetail/perminentdelete/${this.state.fileIdToDelete}`;
    this.setState({ isLoading: true });
    try {
      await axios.delete(api);
      this.setState({
        notification: {
          message: "File Deleted Successfully",
          type: "success",
          show: true,
        },
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
    this.fetchDuplicateDocuments();
    this.closeDeleteModal();
  };

  toggleRow = async (file) => {
    if (this.state.expandedFile === file.documentName) {
      // If already expanded, collapse
      this.setState({
        expandedFile: null,
        duplicateDetails: [],
        showColumns: false,
      });
    } else {
      // Otherwise, fetch duplicates and expand
      this.setState({ showColumns: true, isLoading: true });
      await this.fetchDuplicates(file);
      this.setState({ expandedFile: file.documentName, isLoading: false });
    }
  };

  fetchDuplicates = async (file) => {
    const api = `/documentreports/list_by_duplicates?documentName=${file.documentName}`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        duplicateDetails: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Failed to fetch duplicate details",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleDownloadError = (message) => {
    this.setState({
      notification: {
        message: message,
        type: "error",
        show: true,
      },
    });
  };

  openFileDetail = (file) => {
    this.setState({ showFileDetail: true, selectedFile: file });
  };

  closeFileDetail = () => {
    this.setState({ showFileDetail: false, selectedFile: null });
  };

  render() {
    const {
      duplicateFiles,
      duplicateDetails,
      expandedFile,
      isLoading,
      notification,
      selectedFile,
      deployedURL,
      itemsPerPage,
      currentPage,
      totalItems,
      startDate,
      endDate,
    } = this.state;

    const columns = [
      {
        key: "duplicateCount",
        header: "Count",
        sortable: true,
        render: (value, row) => (
          <button
            style={{
              color: "blue",
              cursor: "pointer",
              background: "none",
              border: "none",
              fontWeight: "900",
            }}
            onClick={() => this.toggleRow(row)}
          >
            {value}
          </button>
        ),
      },
      {
        key: "documentName",
        header: "File Name",
        sortable: true,
        render: (value, row) => <span style={{ color: "#333" }}>{value}</span>,
      },
    ];

    const duplicateDetailsColumns = [
      {
        key: "index",
        header: "",
        width: "5%",
        render: () => "",
      },
      {
        key: "documentName",
        header: "File Name",
        width: "25%",
        sortable: true,
      },
      {
        key: "filePath",
        header: "Location",
        width: "30%",
        sortable: true,
        render: (value) => value && getDisplayPath(value),
      },
      {
        key: "createdDate",
        header: "Added On",
        width: "20%",
        sortable: true,
        render: (value) => formatDate(value),
      },
      {
        key: "createdBy",
        header: "Owner",
        width: "15%",
        sortable: true,
        render: (value) => value,
      },
      {
        key: "actions",
        header: "Action",
        width: "10%",
        render: (_, row) => (
          <button
            className="btn btn-danger btn-sm"
            onClick={() => this.openDeleteModal(row)}
          >
            <i className="fa fa-trash"></i>
          </button>
        ),
      },
    ];

    return (
      <div className="container mt-3">
        {notification.show && (
          <Notification
            message={notification.message}
            type={notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row align-items-center">
          <div className="col-8">
            <h5 className="pt-1" style={{ margin: 0 }}>
              Duplicates Report
              {/* ({totalItems}) */}
            </h5>
          </div>
          <div className="col-4 text-end">
            <div className="d-inline-flex align-items-center gap-2">
              <DownloadReport
                excelEndpoint={`${GlobalConstants.globalURL}/documentreports/duplicateFilesReport/duplicateFilesExcel`}
                pdfEndpoint={`${GlobalConstants.globalURL}/documentreports/duplicateFilesReport/duplicateFilesPDF`}
                reportName="DuplicateFilesReport"
                filter={this.state.currentFilter}
                onError={this.handleDownloadError}
                buttonStyle={{
                  background: "#fff",
                  color: "#333",
                  border: "1px solid #ccc",
                  fontSize: "16px",
                }}
              />
              <button
                className="btn btn-link"
                style={{
                  textDecoration: "underline",
                  color: "#1976d2",
                  fontWeight: 500,
                  fontSize: "16px",
                }}
                onClick={() => window.history.back()}
              >
                Back
              </button>
            </div>
          </div>
        </div>

        <div className="row mt-3">
          <div className="col-12">
            <form className="row g-3 align-items-end">
              <div className="col-md-2">
                <label htmlFor="startDate">Start Date</label>
                <DatePicker
                  className="form-control"
                  selected={startDate ? new Date(startDate) : null}
                  onChange={this.handleStartDateChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="Start Date"
                  maxDate={endDate ? new Date(endDate) : null}
                />
              </div>

              <div className="col-md-2">
                <label htmlFor="endDate">End Date</label>
                <DatePicker
                  className="form-control"
                  selected={endDate ? new Date(endDate) : null}
                  onChange={this.handleEndDateChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="End Date"
                  minDate={startDate ? new Date(startDate) : null}
                />
              </div>
              <div className="col-md-2">
                <button
                  type="button"
                  className="btn btn-primary me-2"
                  onClick={this.applyFilter}
                >
                  Filter
                </button>
                {/* <button
                  type="button"
                  className="btn btn-primary"
                  onClick={this.clearFilter}
                >
                  Clear
                </button> */}
              </div>
            </form>
          </div>
        </div>

        {totalItems > 0 ? (
          <div className="row mt-3">
            <div className="col-12">
              <DataTable
                data={duplicateFiles}
                columns={columns}
                totalItems={totalItems}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                onPageChange={this.handlePageChange}
                onItemsPerPageChange={this.handleItemsPerPageChange}
                className="font-weight-normal"
                showSno
              />

              {expandedFile && duplicateDetails.length > 0 && (
                <div className="mt-3">
                  <h6>Duplicate files for: {expandedFile}</h6>
                  <DataTable
                    data={duplicateDetails}
                    columns={duplicateDetailsColumns}
                    searchable={true}
                    className="font-weight-normal"
                    showSno
                  />
                </div>
              )}
            </div>
          </div>
        ) : (
          <p>
            <strong>0</strong> Duplicates found
          </p>
        )}

        <Modal
          show={this.state.isDeleteModalOpen}
          onHide={this.closeDeleteModal}
        >
          <Modal.Header closeButton></Modal.Header>
          <Modal.Body>Do you want to delete the file?</Modal.Body>
          <Modal.Footer>
            <Button variant="danger" onClick={this.deleteFile}>
              Delete
            </Button>
            &nbsp;
            <Button variant="danger" onClick={this.permanentDeleteFile}>
              Permanent Delete
            </Button>
          </Modal.Footer>
        </Modal>

        {isLoading && <Loader />}

        {/* {this.state.showFileDetail && selectedFile && (
          <FileDetail
            fileName={selectedFile.documentName}
            fileUrl={selectedFile.filePath}
            fileId={selectedFile.documentsAttachmentId}
            onClose={this.closeFileDetail}
          />
        )} */}
      </div>
    );
  }
}

export default DuplicatesReport;
