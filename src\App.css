/* Global table styles */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

thead tr {
  background-color: #2c3e50;
  color: white;
}

th {
  background-color: #2c3e50;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  padding: 12px 15px;
  text-align: left;
  border-right: 1px solid #34495e;
}

th.sortable {
  cursor: pointer;
}

th.sortable:hover {
  background-color: #34495e;
}

td {
  padding: 12px 15px;
  vertical-align: middle;
  border-bottom: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

tr:hover {
  background-color: #f1f5f9;
}

/* Sort indicator */
.sort-indicator {
  margin-left: 5px;
}

/* Responsive tables */
@media (max-width: 768px) {
  th, td {
    padding: 8px 10px;
    font-size: 0.8125rem;
  }
  
  th {
    text-transform: none;
  }
}
