import React, { useEffect, useState } from "react";
import styles from "./Home.module.css";
import { Link, Outlet, useNavigate } from "react-router-dom";
import { deleteById, getList } from "../../services/apiService";
import <PERSON><PERSON><PERSON> from "./Charts/BarChart";
import Pie<PERSON><PERSON> from "./Charts/PieChart";
import CustomBreadcrumb from "../common/CustomBreadcrumb";
import StaticDashboardView from "./StaticDashboardView";
import { Badge } from "react-bootstrap";

function Home(props) {
  const [count, setCount] = useState(null);
  const [duplicateCount, setDuplicateCount] = useState(null);
  const [pendingWFCount, setPendingWFCount] = useState(null);
  const [retentionCount, setRetentionCount] = useState(null);
  const [dueCount, setDueCount] = useState(null);
  const [archiveCount, setArchiveCount] = useState(null);
  const [usersCount, setUsersCount] = useState(null);
  const [specificDocCount, setSpecificDocCount] = useState(null);
  const [showDashboard, setShowDashboard] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [defaultDashboard, setDefaultDashboard] = useState(null);
  const [hasDefaultDashboard, setHasDefaultDashboard] = useState(false);
  const [loadingDefaultDashboard, setLoadingDefaultDashboard] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchDocumentCount();
    fetchDuplicateCount();
    fetchPendingWorkFlowCount();
    fetchDueDateCount();
    fetchRetentionEndCount();
    fetchArchivedFilesCount();
    fetchUsersCount();
    fetchDocumentCount();
    fetchDefaultDashboard();
  }, []);

  const fetchDocumentCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setDocuments(data.content);
      setCount(data.totalElements);
      setSpecificDocCount(count);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchDuplicateCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_duplicates_count?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setDuplicateCount(data.totalElements);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchPendingWorkFlowCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_pending_workflows_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;

      const awaitingCount = data.content.reduce((count, flow) => {
        const hasPendingLevel = flow.approvalLevelDTO?.some(
          (level) => !level.levelStatus
        );
        return hasPendingLevel ? count + 1 : count;
      }, 0);
      setPendingWFCount(awaitingCount);
    } catch (e) {
      console.log(e);
    }
  };

  const fetchRetentionEndCount = async () => {
    const api = `/documentreports/list_by_all_retention_end_files?page=0&size=2000`;
    try {
      const response = await getList(api);
      const data = response.data;
      setRetentionCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };

  const fetchDueDateCount = async () => {
    const api = `/documentreports/list_by_due_date_documents?page=0&size=2000`;
    try {
      const response = await getList(api);
      const data = response.data;
      setDueCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };

  const fetchArchivedFilesCount = async () => {
    const filter = {
      startDate: "startDate",
      endDate: "endDate",
    };
    const api = `/documentreports/list_by_all_archive_files?page=0&size=2000`;
    try {
      const response = await getList(api,filter);
      const data = response.data;
      setArchiveCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };

  const fetchUsersCount = async () => {
    const api = `/user/list?page=&size=2000&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      setUsersCount(data.totalElements);
    } catch (e) {
      console.log(e);
    }
  };

  const fetchDefaultDashboard = async () => {
    setLoadingDefaultDashboard(true);
    try {
      console.log('Fetching default dashboard...');
      
      const currentUserRole = localStorage.getItem("role");
      const isAdmin = currentUserRole === "ROLE_SUPER_ADMIN";
      
      let api;
      let dashboardList = [];
      
      if (isAdmin) {
        // Admin users: Get dashboards they created
        api = `/managedashboard/list?id=${localStorage.getItem("id")}`;
        const response = await getList(api);
        dashboardList = response.data || [];
      } else {
        // Assignee users: Get dashboards assigned to them
        api = `/managedashboard/assigned?userId=${localStorage.getItem("id")}`;
        const response = await getList(api);
        dashboardList = response.data || [];
        
        // If no assigned dashboards, fallback to admin dashboards
        if (dashboardList.length === 0) {
          console.log('No assigned dashboards found, fetching admin dashboards as fallback');
          const fallbackApi = `/managedashboard/admin-fallback`;
          try {
            const fallbackResponse = await getList(fallbackApi);
            dashboardList = fallbackResponse.data || [];
          } catch (fallbackError) {
            console.log('Failed to fetch admin fallback dashboards:', fallbackError);
          }
        }
      }
      
      if (dashboardList && Array.isArray(dashboardList)) {
        let defaultDash = dashboardList.find(dashboard => dashboard.isDefault === true);
        
        // Handle automatic default dashboard logic for assignee users
        if (!isAdmin && !defaultDash && dashboardList.length > 0) {
          if (dashboardList.length === 1) {
            // Only one dashboard assigned - set as default automatically
            console.log('Auto-setting single assigned dashboard as default');
            await setDashboardAsDefault(dashboardList[0].id);
            defaultDash = { ...dashboardList[0], isDefault: true };
          } else if (dashboardList.length > 1) {
            // Multiple dashboards - set latest assigned as default
            const sortedByAssignmentDate = dashboardList.sort((a, b) => 
              new Date(b.assignedDate || b.createdDate) - new Date(a.assignedDate || a.createdDate)
            );
            console.log('Auto-setting latest assigned dashboard as default');
            await setDashboardAsDefault(sortedByAssignmentDate[0].id);
            defaultDash = { ...sortedByAssignmentDate[0], isDefault: true };
          }
        }
        
        console.log('Found default dashboard:', defaultDash);
        
        if (defaultDash) {
          // Ensure dashboard has required properties
          if (!defaultDash.widgets || !Array.isArray(defaultDash.widgets)) {
            console.warn('Default dashboard has no widgets or invalid widgets array');
            setHasDefaultDashboard(false);
            setDefaultDashboard(null);
          } else {
            // Transform dashboard data to the format expected by StaticDashboardView
            const transformedDashboard = transformDashboardForView(defaultDash);
            console.log('Transformed dashboard:', transformedDashboard);
            
            setDefaultDashboard(transformedDashboard);
            setHasDefaultDashboard(true);
          }
        } else {
          console.log('No default dashboard found');
          setHasDefaultDashboard(false);
          setDefaultDashboard(null);
        }
      } else {
        console.log('Invalid response data format');
        setHasDefaultDashboard(false);
        setDefaultDashboard(null);
      }
    } catch (error) {
      console.error('Error fetching default dashboard:', error);
      setHasDefaultDashboard(false);
      setDefaultDashboard(null);
    } finally {
      setLoadingDefaultDashboard(false);
    }
  };

  // Helper function to set dashboard as default
  const setDashboardAsDefault = async (dashboardId) => {
    try {
      const api = `/managedashboard/setDefault/${dashboardId}`;
      await deleteById(api);
      return true;
    } catch (error) {
      console.log('Error setting dashboard as default:', error);
      return false;
    }
  };

  const transformDashboardForView = (dashboardData) => {
    const { name, description, widgets = [], widgetReports = {} } = dashboardData;

    // Helper to normalise widget keys (barChart -> bar-chart)
    const normalizeKey = (key) =>
      key
        .replace(/([a-z])([A-Z])/g, '$1-$2') // camelCase -> hyphen-case
        .toLowerCase();

    // Build a map with both raw and normalised keys pointing to the same reports array
    const reportsMap = {};
    Object.entries(widgetReports).forEach(([key, value]) => {
      reportsMap[key] = value;
      const hyphenKey = normalizeKey(key);
      reportsMap[hyphenKey] = value;
    });

    const transformedWidgets = [];
    let currentX = 20;
    let currentY = 20;
    let maxHeightInRow = 0;
    const containerWidth = 1200;

    widgets.forEach((item, index) => {
      // Determine if item is string (widget type) or object (saved layout)
      let widgetType;
      if (typeof item === 'string') {
        // If the widget is a string, it may already include an instance number (e.g., "bar-chart-1")
        // Strip any trailing hyphen + digits to get the base widget type
        widgetType = item.replace(/-\d+$/, '');
      } else {
        widgetType = item.type || 'custom';
      }

      // Default sizes for different widget types
      const getDefaultSize = (type) => {
        switch (type) {
          case 'table':
            return { width: 600, height: 400 };
          case 'stats-card':
            return { width: 300, height: 200 };
          case 'bar-chart':
          case 'pie-chart':
          case 'line-chart':
            return { width: 500, height: 400 };
          default:
            return { width: 400, height: 300 };
        }
      };

      const defaultSize = getDefaultSize(widgetType);

      // Decide final size (use saved size if present, else default)
      const size = typeof item === 'object' && item.size ? item.size : defaultSize;

      // Flow layout for ALL widgets (ignore any saved absolute position to keep within viewport)
      if (currentX + size.width > containerWidth && index > 0) {
        // Move to next row
        currentX = 20;
        currentY += maxHeightInRow + 20;
        maxHeightInRow = 0;
      }

      const position = { x: currentX, y: currentY };

      const widgetId = typeof item === 'object' && item.id ? item.id : `${widgetType}-${index + 1}`;

      const widget = {
        id: widgetId,
        type: widgetType,
        position,
        size,
        reports: reportsMap[widgetType] || reportsMap[widgetId] || [],
      };

      transformedWidgets.push(widget);

      // Update cursor for next widget
      currentX += size.width + 20;
      maxHeightInRow = Math.max(maxHeightInRow, size.height);
    });

    return {
      name,
      description,
      widgets: transformedWidgets,
      createdAt: dashboardData.createdDate || new Date().toISOString(),
    };
  };

  const sampleDashboard = {
    name: "Sales Dashboard ",
    description: "Dashboard showing sales metrics and performance",
    widgets: [
      {
        id: "bar-chart-1",
        type: "bar-chart",
        position: {
          x: 20,
          y: 20,
        },
        size: {
          width: 373,
          height: 400,
        },
      },
      {
        id: "stats-card-1",
        type: "stats-card",
        position: {
          x: 413,
          y: 20,
        },
        size: {
          width: 373,
          height: 400,
        },
      },
      {
        id: "table-1",
        type: "table",
        position: {
          x: 806,
          y: 20,
        },
        size: {
          width: 373,
          height: 400,
        },
      },
      {
        id: "pie-chart-1",
        type: "pie-chart",
        position: {
          x: 20,
          y: 440,
        },
        size: {
          width: 373,
          height: 400,
        },
      },
      {
        id: "line-chart-1",
        type: "line-chart",
        position: {
          x: 413,
          y: 440,
        },
        size: {
          width: 373,
          height: 400,
        },
      },
    ],
    createdAt: "2025-06-09T09:59:58.618Z",
  };

  const counts = {
    duplicateCount: duplicateCount || 0,
    pendingWFCount: pendingWFCount || 0,
    retentionCount: retentionCount || 0,
    dueCount: dueCount || 0,
    archiveCount: archiveCount || 0,
    totalCount: count || 0,
  };

  return (
    <div className="container mt-4">
      <div className="d-flex align-items-center justify-content-between mb-2">
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => {
              navigate("/newDS/createDashboard");
            }}
          >
            <i className="fa fa-plus me-2"></i>
            Create Dashboard
          </button>
          <button
            className="btn btn-outline-primary"
            onClick={() => {
              navigate("/newDS/dashboardManagement");
            }}
          >
            <i className="fa fa-th-large me-2"></i>
            Manage Dashboards
          </button>
        </div>
        {hasDefaultDashboard && (
          <ul className="nav nav-tabs mb-3">
            <li className="nav-item">
              <button
                className={`nav-link ${!showDashboard ? "active" : ""}`}
                onClick={() => setShowDashboard(false)}
              >
                Default Dashboard
              </button>
            </li>
            <li className="nav-item">
              <button
                className={`nav-link ${showDashboard ? "active" : ""}`}
                onClick={() => setShowDashboard(true)}
              >
                Classic View
              </button>
            </li>
          </ul>
        )}
        <CustomBreadcrumb
          companyName={localStorage.getItem("companyName") || "Company"}
          featureName="Dashboard"
        />
      </div>

      {hasDefaultDashboard && defaultDashboard && !showDashboard ? (
        <div>
          <div className="alert alert-info d-flex align-items-center mb-3">
            <i className="fa fa-info-circle me-2"></i>
            <span>Displaying your default dashboard: <strong>{defaultDashboard.name}</strong></span>
            <div className="ms-auto d-flex gap-2">
              <button 
                className="btn btn-sm btn-outline-secondary"
                onClick={fetchDefaultDashboard}
                title="Refresh dashboard"
              >
                <i className="fa fa-refresh"></i>
              </button>
              <button 
                className="btn btn-sm btn-outline-primary"
                onClick={() => navigate('/newDS/dashboardManagement')}
              >
                <i className="fa fa-cog me-1"></i>
                Manage Dashboards
              </button>
            </div>
          </div>
          <StaticDashboardView
            dashboardConfig={defaultDashboard}
            counts={counts}
            documents={documents}
          />
        </div>
      ) : (
        <>
        { localStorage.getItem("role") !== "ROLE_SUPER_ADMIN" ?  
        <div>
          <h4 className="text-center p-5 m-5">
            <Badge bg="danger">
              Restricted Content, Visible only to admin!
            </Badge>
          </h4>
        </div> 
        :
        <>
          <div>
            {/* Top Stats Row */}
            <div className="row mb-4">
              {/* Total Files */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/allfilesReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#2563eb" }}
                      >
                        <i
                          className="fa fa-file-text"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Total Files</div>
                      <div className={styles.statValue}>
                        <b>{count || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Duplicates */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/duplicatesReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#4338ca" }}
                      >
                        <i
                          className="fa fa-clone"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Duplicate</div>
                      <div className={styles.statValue}>
                        <b>{duplicateCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Pending Workflows */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/pendingWorkFlows"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#059669" }}
                      >
                        <i
                          className="fa fa-tasks"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Pending WorkFlows</div>
                      <div className={styles.statValue}>
                        <b>{pendingWFCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Due Soon */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/duedateReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#db2777" }}
                      >
                        <i
                          className="fa fa-calendar"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Nearing Due Date</div>
                      <div className={styles.statValue}>
                        <b>{dueCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </div>

            {/* Second Stats Row */}
            <div className="row mb-4">
              {/* Archive Files */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/archivedReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#8b5cf6" }}
                      >
                        <i
                          className="fa fa-archive"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Archive Files</div>
                      <div className={styles.statValue}>
                        <b>{archiveCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Users Report */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/usersReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#e11d48" }}
                      >
                        <i
                          className="fa fa-users"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>Users Report</div>
                      <div className={styles.statValue}>
                        <b>{usersCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Retention End */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/retentionReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#0891b2" }}
                      >
                        <i
                          className="fa fa-clock-o"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>
                        Nearing Retention End
                      </div>
                      <div className={styles.statValue}>
                        <b>{retentionCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Specific Doc Log */}
              <div className="col-md-3 mb-3">
                <Link
                  to="/newDS/specificDocLogReport"
                  style={{ textDecoration: "none", color: "inherit" }}
                >
                  <div className={`card shadow-sm ${styles.dashboardCard}`}>
                    <div className="card-body p-4">
                      <div
                        className={styles.iconContainer}
                        style={{ backgroundColor: "#ca8a04" }}
                      >
                        <i
                          className="fa fa-file-text-o"
                          style={{ color: "white", fontSize: "20px" }}
                        ></i>
                      </div>
                      <div className={styles.statLabel}>
                        Specific Doc Log Report
                      </div>
                      <div className={styles.statValue}>
                        <b>{specificDocCount || 0}</b>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
          {/* Charts Row */}
          <div className="row">
            {/* Bar Chart */}
            <div className="col-lg-6 mb-4">
              <div className={styles.chartContainer}>
                <div className={styles.chartHeader}>
                  <h3
                    className={styles.chartTitle}
                    style={{ textAlign: "center" }}
                  >
                    Bar Chart
                  </h3>
                </div>
                <div style={{ height: "350px" }}>
                  <BarChart
                    duplicateCount={duplicateCount}
                    pendingWFCount={pendingWFCount}
                    retentionCount={retentionCount}
                    dueCount={dueCount}
                    archiveCount={archiveCount}
                  />
                </div>
              </div>
            </div>

            {/* Pie Chart */}
            <div className="col-lg-6 mb-4">
              <div className={styles.chartContainer}>
                <div className={styles.chartHeader}>
                  <h3
                    className={styles.chartTitle}
                    style={{ textAlign: "center" }}
                  >
                    Pie Chart
                  </h3>
                </div>
                <div
                  style={{
                    height: "350px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <PieChart
                    duplicateCount={duplicateCount}
                    pendingWFCount={pendingWFCount}
                    retentionCount={retentionCount}
                    dueCount={dueCount}
                    archiveCount={archiveCount}
                  />
                </div>
              </div>
            </div>
          </div>
        </> }
        </>
      )}

      <Outlet />
    </div>
  );
}

export default Home;
