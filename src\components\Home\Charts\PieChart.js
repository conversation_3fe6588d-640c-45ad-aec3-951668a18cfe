import React from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register required Chart.js components
ChartJS.register(ArcElement, Title, Tooltip, Legend);

const PieChart = (props) => {
  // Default color palette
  const colorPalette = [
    '#4338ca', '#059669', '#0891b2', '#db2777', '#8b5cf6',
    '#f59e0b', '#10b981', '#3b82f6', '#6366f1', '#ec4899'
  ];

  // Prepare chart data based on props
  const prepareChartData = () => {
    // If data prop is provided, use that (from widgetReports)
    if (props.data) {
      const labels = Object.keys(props.data);
      const values = Object.values(props.data);
      return {
        labels: labels,
        datasets: [
          {
            data: values,
            backgroundColor: labels.map((_, index) => colorPalette[index % colorPalette.length]),
            borderColor: labels.map((_, index) => colorPalette[index % colorPalette.length]),
            borderWidth: 1,
          },
        ],
      };
    }
    
    // Fallback to individual props if data prop isn't provided
    return {
      labels: ['Duplicate', 'Pending WorkFlows', 'Retention End', 'Due Date', 'Archive Files'],
      datasets: [
        {
          data: [
            props.duplicateCount || 0,
            props.pendingWFCount || 0,
            props.retentionCount || 0,
            props.dueCount || 0,
            props.archiveCount || 0
          ],
          backgroundColor: [
            '#4338ca',
            '#059669',
            '#0891b2',
            '#db2777',
            '#8b5cf6'
          ],
          borderColor: [
            '#4338ca',
            '#059669',
            '#0891b2',
            '#db2777',
            '#8b5cf6'
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const data = prepareChartData();
  console.log("piechart",data)

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    layout: {
      padding: {
        left: 10,
        right: 10,
        top: 10,
        bottom: 10
      }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 12,
          padding: 6,
          usePointStyle: true,
          font: {
            size: 10
          }
        },
      },
      title: {
        display: true,
        text: props.title || 'All Files - Overview',
        align: 'center',
        font: {
          size: 14
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((acc, data) => acc + data, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
  };

  return (
    <div style={{ 
      position: 'relative',
      width: '100%', 
      height: '100%',
      minWidth: '200px',
      minHeight: '150px',
      overflow: 'hidden'
    }}>
      <Pie data={data} options={options} />
    </div>
  );
};

export default PieChart;