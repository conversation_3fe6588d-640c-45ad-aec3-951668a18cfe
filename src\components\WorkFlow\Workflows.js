import React, { Component } from "react";
import { getWorkflows } from "../../services/workflowService";
import Loader from "../loader/Loader";
import Notification from "../Notification/Notification";
import { Navigate } from "react-router-dom";
import { formatDate } from "../../services/apiService";

class Workflows extends Component {
  state = {
    workflowList: [],
    isLoading: false,
    navigate: false,
    selectedWorkflowId: null,
    selectedWorkflow: null,
    notification: {
      message: "",
      type: "",
      show: false,
    },
  };

  componentDidMount() {
    this.fetchWorkflowList();
  }

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  fetchWorkflowList = async () => {
    this.setState({ isLoading: true });
    try {
      const response = await getWorkflows("", 0, 1000);
      if (response.status === "success") {
        const workflowsData = response.data.content || [];
        console.log(`Loaded ${workflowsData.length} workflows:`, workflowsData);
        this.setState({ workflowList: workflowsData, isLoading: false });
      }
    } catch (e) {
      this.setState({ isLoading: false });
    }
  };

  handleWorkflowClick = (e, workflow) => {
    e.stopPropagation();
    sessionStorage.setItem("wfName",workflow.name);
    this.setState({ selectedWorkflow: workflow, navigate: true });
  };

  render() {
    const { workflowList, selectedWorkflowId, selectedWorkflow } = this.state;
    if (this.state.navigate) {
      return (
        <Navigate
          to="/newDS/workflow-management"
          state={{ workflowData: selectedWorkflow }}
        />
      );
    }

    const itemsPerRow = 10;
    const rowCount = Math.ceil(workflowList.length / itemsPerRow);
    const tileWidth = "calc((100% - (18px * 9)) / 10)";
    return (
      <>
         <div style={{ textAlign: "center", marginBottom: "16px",marginTop:"16px" }}>
          <h4>Workflows</h4>
        </div>
        <div>
          {workflowList.length > 0 && (
            <div
              style={{
                position: "relative",
                top: "10px",
                left: "10px",
                right: "10px",
                zIndex: 10,
                background: "rgba(255, 255, 255, 0.95)",
                borderRadius: "8px",
                padding: "16px",
                minHeight: "160px",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                overflow: "visible", // Changed from overflowX/overflowY
              }}
            >
              {Array.from({ length: rowCount }).map((_, rowIndex) => (
                <div
                  key={rowIndex}
                  style={{
                    display: "flex",
                    gap: "16px",
                    justifyContent: "flex-start",
                    marginBottom: rowIndex < rowCount - 1 ? "16px" : "0",
                    minWidth: "100%", 
                  }}
                >
                  {workflowList
                    .slice(rowIndex * itemsPerRow, (rowIndex + 1) * itemsPerRow)
                    .map((workflow, itemIndexInRow) => {
                      const isSelected = selectedWorkflowId === workflow.id;

                      const isOddRow = rowIndex % 2 !== 0;
                      const isOddItemInRow = itemIndexInRow % 2 !== 0;

                      let backgroundColor = "#ffffff";

                      if (!isSelected) {
                        if (isOddRow) {
                          backgroundColor = isOddItemInRow
                            ? "#ffffff"
                            : "#f5f5f5";
                        } else {
                          backgroundColor = isOddItemInRow
                            ? "#f5f5f5"
                            : "#ffffff";
                        }
                      } else {
                        backgroundColor = "#e3f2fd";
                      }
                      return (
                        <div
                          key={workflow.id}
                          className="workflow-tile"
                          style={{
                            background: backgroundColor,
                            border: isSelected
                              ? "2px solid #2196f3"
                              : "1px solid #ddd",
                            borderRadius: "8px",
                            boxShadow: isSelected
                              ? "0 4px 12px rgba(33, 150, 243, 0.3)"
                              : "0 2px 8px rgba(0,0,0,0.05)",
                            padding: "16px",
                            width: tileWidth,
                            minWidth: "200px",
                            cursor: "pointer",
                            transition: "all 0.2s ease",
                            position: "relative",
                            outline: "none",
                            flexShrink: 0, // Prevent items from shrinking
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-between" 
                          }}
                          onMouseEnter={(e) => {
                            if (!isSelected) {
                              e.currentTarget.style.boxShadow =
                                "0 4px 12px rgba(0,0,0,0.15)";
                              e.currentTarget.style.transform =
                                "translateY(-2px)";
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isSelected) {
                              e.currentTarget.style.boxShadow =
                                "0 2px 8px rgba(0,0,0,0.05)";
                              e.currentTarget.style.transform = "translateY(0)";
                            }
                          }}
                          tabIndex={0}
                          onClick={(e) => this.handleWorkflowClick(e, workflow)}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: "8px",
                            }}
                          >
                            <div
                              style={{
                                fontWeight: "bold",
                                fontSize: "14px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                                flex: 1,
                              }}
                            >
                              {workflow.name}
                            </div>
                            <div
                              style={{
                                display: "flex",
                                gap: "8px",
                                zIndex: 40,
                                position: "relative",
                              }}
                            ></div>
                          </div>
                          <div
                            style={{
                              fontSize: "12px",
                              color: "#888",
                              marginBottom: "8px", // Increased margin
                              textTransform: "capitalize",
                              flexGrow: 1 
                            }}
                          >
                            {workflow.workflow_type}
                          </div>
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              fontSize: "10px",
                              color: "#aaa",
                            }}
                          >
                            <span>{formatDate(workflow.createdAt)}</span>
                          </div>
                          {/* <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              fontSize: "10px",
                              color: "#aaa",
                            }}
                          >
                            <span>Click to load</span>
                          </div> */}
                        </div>
                      );
                    })}
                </div>
              ))}
            </div>
          )}
        </div>
        <div>
          {this.state.isLoading && <Loader />}
          {this.state.notification.show && (
            <Notification
              message={this.state.notification.message}
              type={this.state.notification.type}
              onClose={this.closeNotification}
            />
          )}
        </div>
      </>
    );
  }
}
export default Workflows;
