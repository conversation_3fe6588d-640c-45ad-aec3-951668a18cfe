import React from "react";
import classes from "./EmailApproval.module.css";
import Letter from "../images/Letter.jpg";
import {
  findById,
  getList,
  downloadeDocument,
} from "../../services/apiService";
import { GlobalConstants } from "../../constants/global-constants";
import { Navigate } from "react-router-dom";
import axios from "../../services/api";
import Notification from "../Notification/Notification";
import { Modal, Button } from "react-bootstrap";
import DocumentPreview from "../DocumentManagement/DocumentPreview";

class EmailAcknowledge extends React.Component {
  state = {
    fileName: "",
    name: "",
    description: "",
    fileId: null,
    emailFields: [{ id: "" }],
    userList: [],
    userEmails: [],
    comments: "",
    levelId: null,
    notification: {
      message: "",
      type: "",
      show: false,
    },
    isPreviewModalOpen: false,
    previewing: false,
    filePath: "",
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  componentDidMount() {
    this.getApprovalWorkFlowById();
    this.fetchUserList();
  }
  componentWillUnmount() {
    this.setState({
      fileName: "",
      fileId: null,
    });
  }

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=100&search=&sort=";
    try {
      const data = await getList(api);
      console.log("--------------", data.data.content);
      this.setState({ userList: data.data.content }, () =>
        console.log(this.state.userList)
      );
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  getApprovalWorkFlowById = async () => {
    const api = `/approvalworkflow/doc1/acknowledgement/${sessionStorage.getItem(
      "fileId"
    )}`;
    try {
      // alert("eneter");
      const response = await findById(api);
      console.log(response.data);

      const sessionId = localStorage.getItem("id");
      //alert(sessionId);
      const matchedApprovalLevel = response.data.approvalLevelDTO.find(
        (level) => level.usersId.toString() === sessionId
      );
      this.setState({
        isApproved: matchedApprovalLevel.levelStatus,
        isRejected: matchedApprovalLevel.rejectStatus,
      });

      if (matchedApprovalLevel) {
        //alert(matchedApprovalLevel.id);
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
          })),
          name: response.data.name,
          description: response.data.description,
          levelId: matchedApprovalLevel.id, // Storing the level id
        });
      } else {
        //alert("else");
        this.setState({
          emailFields: response.data.approvalLevelDTO.map((level) => ({
            id: level.usersId,
            levelStatus: level.levelStatus,
          })),
          name: response.data.name,
        });
      }
    } catch (error) {
      this.setState({});
    }
    //this.handleButtons();
  };

  handleCommentChange = (event) => {
    this.setState({ comments: event.target.value });
  };

  handleApprove = () => {
    console.log("Comments : ", this.state.comments);
    console.log(
      "Approved Document : ",
      sessionStorage.getItem("fileId"),
      " Approved by UserID: ",
      localStorage.getItem("id")
    );
    //const api=`${GlobalConstants.globalURL}/approvalworkflow/docapprove/${this.state.approvalDocId}/${localStorage.getItem("id")}`;
    const api = `${GlobalConstants.globalURL}/approvalworkflow/docUpdateLevelStatus/${this.state.levelId}?remarks=`;
    //alert(api)
    try {
      const response = axios.put(api);
      this.setState({
        notification: {
          message: `Document ${sessionStorage.getItem(
            "fileName"
          )} acknowledged successfully!`,
          type: "success",
          show: true,
        },
      });
      setTimeout(() => {
        this.setState({ navigate: true });
      }, 1000);
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleReject = () => {
    console.log(
      "Rejected Document : ",
      sessionStorage.getItem("fileId"),
      " Rejected by UserID : ",
      localStorage.getItem("id")
    );
    this.setState({
      notification: {
        message: "Reject Success",
        type: "error",
        show: true,
      },
      navigate: true,
    });
  };

  handleButtons = () => {
    // const approvalIds = this.state.emailFields.map((field) => ({
    //   id: field.id,
    //   levelStatus: field.levelStatus,
    // }));
    // const sessionId = localStorage.getItem("id");
    // const matchedApproval = approvalIds.find(
    //   (approval) => approval.id.toString() === sessionId
    // );
    // if (matchedApproval) {
    //   this.setState({ isApproved: matchedApproval.levelStatus });
    // } else {
    //   this.setState({ isApproved: false });
    // }
    // console.log(this.state.matchedApprovalLevel);
    // this.setState({
    //     isApproved: this.state.matchedApprovalLevel.levelStatus,
    //     isRejected: this.state.matchedApprovalLevel.rejectStatus,
    //     isLoading:false,
    //   });
  };

  downloadeDocumentAttachment = async (filePath, fileName) => {
    try {
      // alert("hi");
      const data = await downloadeDocument(filePath);
      let blob = new Blob([data], { type: "application/octet-stream" });
      let url = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = url;
      anchor.download = fileName;
      anchor.target = "_blank";
      anchor.click();
      //window.location.href = response.url;
      //fileSaver.saveAs(blob, 'employees.json');
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  previewDocument = async (filePath, fileName) => {
    this.setState({
      isPreviewModalOpen: false,
      previewing: false,
      filePath: "",
    });

    try {
      const data = await downloadeDocument(filePath);
      const blob = new Blob([data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);

      this.setState({
        filePath: url,
        previewing: true,
        isPreviewModalOpen: true,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Could not preview the document",
          type: "error",
          show: true,
        },
      });
    }
  };

  closePreviewModal = () => {
    this.setState({
      filePath: "",
      isPreviewModalOpen: false,
      previewing: false,
    });
  };

  render() {
    const approvalIds = this.state.emailFields.map((field) => ({
      id: field.id,
      levelStatus: field.levelStatus,
    }));

    if (this.state.navigate) {
      return <Navigate to="/newDS/document-management" />;
    }
    return (
      <div className="container">
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div className="row">
          <div className="col-sm-8 offset-sm-2">
            <div className="card mt-2">
              <h3 className="text-center mt-3">
                {sessionStorage.getItem("companyName")}
              </h3>
              <img
                src={Letter}
                className={classes.letterImg}
                alt="LetterHeadLogo"
              />
              <div className="card-body">
                {this.state.isApproved ? (
                  <h5 className="card-title text-center">
                    {" "}
                    Acknowledged document is
                  </h5>
                ) : (
                  <h5 className="card-title text-center">
                    {sessionStorage.getItem("assignedBy")} is asking you to
                    acknowledge the document
                  </h5>
                )}
                {/* <p className="card-text text-center">Locations : Projects</p> */}
                <p className={classes.fileName}>
                  <span
                    style={{ cursor: "pointer" }}
                    onClick={(e) => {
                      e.preventDefault();
                      this.previewDocument(
                        sessionStorage.getItem("filePath"),
                        sessionStorage.getItem("fileName")
                      );
                    }}
                  >
                    <i
                      className={`${classes.fileIcon} fa fa-file-text`}
                      aria-hidden="true"
                    ></i>
                    {sessionStorage.getItem("fileName")}
                  </span>
                  <a
                    style={{
                      color: "blue",
                      textDecoration: "underline",
                      cursor: "pointer",
                    }}
                  >
                    <i
                      title="download file"
                      onClick={() =>
                        this.downloadeDocumentAttachment(
                          sessionStorage.getItem("filePath"),
                          sessionStorage.getItem("fileName")
                        )
                      }
                      className={`${classes.downloadIcon} fa fa-download`}
                      aria-hidden="true"
                    ></i>
                  </a>
                </p>
                <h6 className="text-primary p-2">Note : {this.state.name}</h6>
                <p className="text-primary p-2">
                  Description : {this.state.description}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-sm-8 offset-sm-2">
            <div className="card">
              <div className="card-body mx-5 mt-3 mb-4">
                <h5>Users in this acknowledgement workflow</h5>
                <div>
                  {this.state.userList
                    .filter((user) =>
                      approvalIds.some((approval) => approval.id === user.id)
                    ) // Check if user.id is in approvalIds
                    .map((user, index) => {
                      // Find the corresponding approval object
                      const approval = approvalIds.find(
                        (approval) => approval.id === user.id
                      );
                      const isChecked = approval ? approval.levelStatus : false; // Check levelStatus
                      const isDisabled = approval
                        ? !approval.levelStatus
                        : false; // Disable if levelStatus is false

                      return (
                        <div className="form-check" key={index}>
                          <input
                            className="form-check-input"
                            type="checkbox" // Assuming you meant radio buttons
                            value={user.id}
                            id={`emailList-${index}`}
                            checked={isChecked}
                            disabled={isDisabled}
                          />
                          <label
                            className="form-check-label"
                            htmlFor={`emailList-${index}`}
                          >
                            {user.email}
                            {/* Adjust to show actual name if available */}
                          </label>
                        </div>
                      );
                    })}
                </div>
                <div className="row mt-4">
                  <div className="col-12 text-center">
                    <button
                      type="button"
                      className="btn btn-success mx-3"
                      onClick={this.handleApprove}
                      disabled={this.state.isApproved}
                    >
                      {this.state.isApproved
                        ? "ACKNOWLEDGED"
                        : "ACKNOWLEDGEMENT REQUIRED"}
                    </button>
                    {/* <button type="button" className="btn btn-danger mx-3" onClick={this.handleReject}>
                      REJECT
                    </button> */}
                    <br />
                    {/* <button
                      type="button"
                      className="btn btn-link mt-4"
                      style={{ color: "black", fontWeight: "bold" }}
                    >
                      Delegate
                    </button> */}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Modal
            show={this.state.isPreviewModalOpen}
            size="xl"
            onHide={this.closePreviewModal}
          >
            <Modal.Header className={classes.modalHeaderModern} closeButton>
              Previewing&nbsp;&nbsp;
              <strong>"{sessionStorage.getItem("fileName")}"</strong>
            </Modal.Header>
            <Modal.Body className={`${classes.previewModalBody}`}>
              {this.state.previewing && (
                <DocumentPreview
                  filePath={this.state.filePath}
                  fileName={sessionStorage.getItem("fileName")}
                  closePreview={this.closePreviewModal}
                />
              )}
            </Modal.Body>
            <Modal.Footer className={classes.modalFooterModern}>
              <Button
                onClick={this.closePreviewModal}
                className="btn btn-primary"
              >
                Close
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>
    );
  }
}

export default EmailAcknowledge;
