/* Main container styling */
.bgTop {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(to right, #6a11cb, #2575fc); /* Gradient background */
    padding: 20px;
}
  
  /* Block and row styling */
  .blockColor {
    background-color: #cbfbd8;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: 100%;
    min-height: 40vh;
  }
  
  .row {
    display: flex;
    flex-wrap: wrap;
    height: auto;
  }
  
  .pT {
    padding: 15px;
  }
  
  /* Company block styling */
  .companyBlock {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    margin-top: 40px;
  }
  
  .companyLogo img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
  }
  
  /* Logout button styling */
  .logoutBtn {
    display: block;
    margin-bottom: 20px;
    margin-left: 400px;
    color: white;
    text-align: center;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 5px;
  }
  
  /* Companies list styling */
  .companies {
    text-align: center;
    width: 100%;
  }
  
  .optionHeading {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
  }
  
  .listGroupItem {
    padding: 10px;
    font-size: 1rem;
    cursor: pointer;
    border: none;
    background-color: #f7f7f7;
    transition: background-color 0.3s;
  }
  
  .listGroupItem:hover {
    background-color: #e0e0e0;
  }

  @media (max-width: 1500px)
  {
    .logoutBtn {
      margin-left:300px;
    }
  }

  @media (max-width: 1200px)
  {
    .logoutBtn {
      margin-left:150px;
    }
  }
  
  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    .col-md-8, .col-md-4 {
      flex: 100%;
      max-width: 100%;
    }

    .companyBlock
    {
        margin-top: 10px;
    }
  
    .logoutBtn {
      width: 100%;
      margin-left:0px;
    }
    
    .companyLogo img {
      width: 80%;
    }
  }
  