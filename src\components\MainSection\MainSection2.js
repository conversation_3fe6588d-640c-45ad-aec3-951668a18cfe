import React,{useState} from 'react';
import { Button,Dropdown } from 'react-bootstrap';
import classes from './MainSection2.module.css';

function MainSection(props) {
    const [showDropdown,setShowDropdown]=useState(false);

    const toggleDropdown=()=>{
        setShowDropdown((prevState) => ({ showDropdown: !prevState.showDropdown }));
    }

    return (
        <div className={`${classes.bgTop}`}>
        <div className='container-fluid py-3'>
                <div className='row'>
                    <div className='col-2'>
                        Main Sections
                    </div>
                    <div className='col-4 offset-1 text-start'>
                            <div className='d-flex justify-content-between'>
                                {/* <div className='text-center'>
                                    <i className="fa fa-folder-o" aria-hidden="true"></i>   
                                    <br />
                                    Folder
                                </div>
                                <div className='text-center'>
                                    <i className="fa fa-file-text-o" aria-hidden="true"></i>
                                    <br />
                                    Audit Log
                                </div> */}
                            </div>
                    </div>

                    <div className='col-4 offset-1'>
                    <div className={classes.uploadDiv}>
                        <Button variant='primary'>Upload</Button>
                        <input
                            type="file"
                            style={{ display: 'none' }}
                        />
                        <Dropdown show={showDropdown} onToggle={toggleDropdown}>
                            <Dropdown.Toggle variant="secondary" id="dropdown-basic" className="bg-primary">
                            </Dropdown.Toggle>
                            <Dropdown.Menu className="modal-header-modern">
                            <Dropdown.Item eventKey="1">Upload a File</Dropdown.Item>
                            <Dropdown.Item eventKey="2">Add a Link</Dropdown.Item>
                            <Dropdown.Item eventKey="3">New Word Document</Dropdown.Item>
                            <Dropdown.Item eventKey="4">New Excel Spreadsheet</Dropdown.Item>
                            <Dropdown.Item eventKey="5">New PowerPoint Presentation</Dropdown.Item>
                            </Dropdown.Menu>
                        </Dropdown>
                        <input type="search" 
                               className='form-control ms-2'
                               placeholder='Search docs,tags,etc..'  />
                        <i className={`fa fa-search ${classes.searchIcon}`} aria-hidden="true"></i>
                    </div>
                    

                    </div>
                </div>
            </div>
        </div>
    );
}

export default MainSection;