import React from "react";
import FileDetail from "../FileDetail";
import { Table } from "react-bootstrap";
import { DataTable } from "../../Table/DataTable";
import { Component } from "react";
import classes from "../DocumentManagement.module.css";
import Loader from "../../loader/Loader";
import {
  deleteById,
  findById,
  getList,
  editById,
  addNew,formatDate
} from "../../../services/apiService";
// ReactPaginate is no longer needed as we're using DataTable's pagination
import "./NestedObjectTable.css"; // We'll create this file for pagination styling
import Notification from "../../Notification/Notification";
import { <PERSON><PERSON>, But<PERSON>, Form } from "react-bootstrap";
import ReactDatePicker from "react-datepicker";
import { BsTrash } from "react-icons/bs";

class NestedObjectTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      notification: {
        message: "",
        type: "",
        show: false,
      },
      currentData: props.data || {}, // Initialize with data from props
      path: [], // Navigation path
      selectedFile: null, // Store the selected file path
      fileSelected: props.fileSelected,
      currentPage: 0, // Current page for pagination
      itemsPerPage: 10, // Items per page
      sortKey: "", // Default sort key (empty means unsorted)
      sortDirection: "asc", // Default sort direction (ascending)
      isShareModalOpen: false,
      shareEmail: "",
      shareUserGroup: "",
      shareId: null,
      isShareInputGiven: false,
      usersGroup: [],
      userList: [],
      isShareExpirationEnabled: false,
      fullPath: "",
      isFolderRenameModalOpen: false,
      folderRename: "",
      uploadedFiles: props.uploadedFiles,
      isFolderDeleteModalOpen: false,
      key: "",
      isLoading: false,
      folderSharedList:[],
      isDeleteModalOpen:false,
      jobIdToDelete:null,
    };
  }

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  componentDidUpdate(prevProps) {
    // Update state when `data` prop changes
    if (prevProps.data !== this.props.data) {
      this.setState({ currentData: this.props.data });
    }
  }

  componentWillUnmount() {
    sessionStorage.setItem("currentPath", "");
  }

  handleFolderClick = (key) => {
    const { currentData, path } = this.state;
    const newPath = [...path, key];

    // Join the path elements into a proper folder structure string
    const currentPath = newPath.join("/");

    console.log(currentPath);
    sessionStorage.setItem("currentPath", currentPath);
    this.setState({
      currentData: currentData[key],
      path: [...path, key],
      selectedFile: null, // Clear selected file when navigating
      currentPage: 0, // Reset to the first page when navigating
    });
  };

  handleFileClick = (key, filePath) => {
    const empId = filePath?.split("/Emp_Id_")[1]?.split("/")[0] || null;
    console.log("Extracted Employee ID:", empId);

    const { path } = this.state;
    const fullPath = [...path, key].join("/"); // Construct the full file path
    this.fetchFileDetails(fullPath, empId);
  };

  handleDeleteFileClick = (key) => {
    console.log(key);
  };

  handleDeleteFolderModal = (key) => {
    this.setState({ isFolderDeleteModalOpen: true, key: key });
  };

  closeFolderDeleteModal = () => {
    this.setState({ isFolderDeleteModalOpen: false, key: "" });
  };

  handleDeleteFolderClick = async (key) => {
    this.setState({isLoading:true})
    const { path } = this.state;
    if (path.length === 0) {
      console.log(key);
    } else {
      console.log(path.join("/") + "/" + key);
    }
    const employeeid = localStorage.getItem("id");
    const api =
      path.length === 0
        ? `/documentsattachmentdetail/deleteFolder/${employeeid}?filePath=${key}`
        : `/documentsattachmentdetail/deleteFolder/${employeeid}?filePath=${
            path.join("/") + "/" + key
          }`;
    try {
      const response = await deleteById(api);
      console.log(response);
      this.setState(
        {
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          isLoading:false,
          isFolderDeleteModalOpen: false,
        },
        () => {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      );
    } catch (e) {
      this.setState({isLoading:false})
      this.closeFolderDeleteModal();
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  fetchFileDetails = async (filePath, empId) => {
    this.setState({ isLoading: true });
    const employeeId =
      empId == localStorage.getItem("id") ? localStorage.getItem("id") : empId;

    console.log(employeeId);

    let api =
      "/documentsattachmentdetail/list_by_employee_filepath/" +
      employeeId +
      "?filePath=" +
      filePath;
    try {
      const data = await findById(api);
      console.log("data received from api",data.data)
      
    await new Promise(resolve => this.setState({
      selectedFile: data.data,
      isLoading: false
    }, resolve));
    
      this.props.toggleDetailView(this.state.selectedFile);
      this.props.fileSelected(this.state.selectedFile);
    } catch (error) {
      //console.log(error);
    }
  };

  handleBack = () => {
    const { data } = this.props;
    const { path } = this.state;

    if (path.length > 0) {
      const newPath = path.slice(0, -1); // Remove the last element from the path
      const parentData = newPath.reduce((acc, key) => acc[key], data); // Traverse to the parent object
      const currentPath = newPath.join("/");
      sessionStorage.setItem("currentPath", currentPath);

      this.setState({
        currentData: parentData,
        path: newPath,
        selectedFile: null, // Clear selected file when going back
      });
    }
  };

  handlePageChange = (selectedPage) => {
    // Update the current page in state
    this.setState({ currentPage: selectedPage.selected });
  };

  handleItemsPerPageChange = (newSize) => {
    // When items per page changes, reset to first page
    this.setState({
      itemsPerPage: newSize,
      currentPage: 0
    });
  };

  handleSort = (key) => {
    const { sortKey, sortDirection } = this.state;

    // Toggle sort direction if the same column is clicked
    const newDirection =
      sortKey === key && sortDirection === "asc" ? "desc" : "asc";

    // Set the new sort key and direction
    this.setState({
      sortKey: key,
      sortDirection: newDirection,
    });
  };

  sortData = (data, sortKey, sortDirection) => {
    if (sortKey === "") return Object.keys(data); // If no sort key is selected, return unsorted keys

    // Get the keys from the data object and sort them alphabetically based on the selected sortKey
    const sortedKeys = Object.keys(data).sort((a, b) => {
      const comparison = a.localeCompare(b); // Alphabetical sorting
      return sortDirection === "asc" ? comparison : -comparison;
    });

    return sortedKeys;
  };

  fetchUserGroup = async () => {
    let api = `/team/team_dropdown_list`;
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        usersGroup: data,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  fetchSharedFolderList = async (path) => {
    const api =
      `/job-scheduling/sharedFolderList/${localStorage.getItem("id")}?path=${path}`;
    try {
      const response = await getList(api);
      this.setState({folderSharedList:response.data});
      return response.data;
    } catch (error) { }
  }

  handleShareModal = (key) => {
    this.fetchUserList();
    const { path } = this.state;
    if (path.length === 0) {
      this.setState({ fullPath: key },()=> this.fetchSharedFolderList(this.state.fullPath));
    } else {
      this.setState({ fullPath: path.join("/") + "/" + key },()=>this.fetchSharedFolderList(this.state.fullPath));
    }
    this.fetchUserGroup();
    this.setState({ isShareModalOpen: true });
  };

  fetchUserList = async () => {
    const api = "/user/list?page=0&size=300&search=&sort=";
    try {
      const data = await getList(api);
      //console.log("--------------", data.data.content);
      const filteredList=data.data.content.filter(user=>user.id!= localStorage.getItem("id"));
      this.setState({ userList: filteredList});
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
      });
    }
  };

  handleFolderRenameModal = (key) => {
    console.log(key);
    sessionStorage.setItem("oldFolderName", key);
    this.setState({ folderRename: key });
    this.setState({ isFolderRenameModalOpen: true });
  };

  handleSubmitFolderRenameModal = async () => {
    const { folderRename } = this.state;
    const path = sessionStorage.getItem("currentPath") || "";
    const oldFolderRename = path
      ? path + "/" + sessionStorage.getItem("oldFolderName")
      : sessionStorage.getItem("oldFolderName");
    const fullPath = path ? `${path}/${folderRename}` : folderRename;
    //console.log("New Folder Name :", folderRename);
    console.log("New Folder Name with path:", fullPath);

    const employeeid = localStorage.getItem("id");
    const api = `/documentsattachmentdetail/renameFolder/${employeeid}?oldFilePath=${oldFolderRename}&newFilePath=${fullPath}`;

    try {
      const response = await deleteById(api);
      console.log(response);
      this.setState(
        {
          notification: {
            message: "Folder Rename Successful",
            type: "success",
            show: true,
          },
        },
        () => {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      );
    } catch (e) {
      console.log(e);
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    }

    this.closeFolderRenameModal();
  };

  toggleShareExpiration = () => {
    this.setState((prevState) => ({
      isShareExpirationEnabled: !prevState.isShareExpirationEnabled,
      shareExpirationDate: new Date(),
    }));
  };

  handleShareDateChange = (event) => {
    this.setState({ shareExpirationDate: event.target.value });
  };

  handleShareEmailChange = (event) => {
    this.setState({ shareEmail: event.target.value, shareUserGroup: "" });
  };

  handleShareUserGroupChange = (event) => {
    this.setState({ shareUserGroup: event.target.value, shareEmail: "" });
  };
  selectedShareValue = (event) => {
    const selectedOption = event.target.value;
    this.setState({ viewType: selectedOption });
    console.log("Selected value:", selectedOption);
    if (selectedOption === "Custom") {
      this.setState({ isShareCustomSelected: true });
    } else {
      this.setState({ isShareCustomSelected: false });
    }
  };

  closeShareModal = () => {
    this.setState({
      isShareModalOpen: false,
      shareEmail: "",
      shareUserGroup: "",
      shareId: null,
    });
  };

  closeFolderRenameModal = () => {
    this.setState({
      isFolderRenameModalOpen: false,
      folderRename: "",
    });
  };

  handleShareTo = async () => {
    const {
      shareEmail,
      shareUserGroup,
      viewType,
      isShareCustomSelected,
      shareExpirationDate,
      customPermissions,
      folderSharedList,
    } = this.state;

    const shareToData = {
      email: shareEmail || null,
      teamId: shareUserGroup || null,
      documentPath: this.state.fullPath,
      messageStatus: viewType,
      //customPermissions: customPermissions,
      jobDate: shareExpirationDate,
      jobType: "sharedto",
    };

    if (
      folderSharedList.some((item) => shareEmail !== "" && item.email === shareEmail)
    ) {
      this.setState({
        notification: {
          message: "Already shared to this User.",
          type: "error",
          show: true,
        },
      });
      return;
    }
    if (
      folderSharedList.some((item) => 
        shareUserGroup && 
        String(item.teamId) === String(shareUserGroup))
    ) {
      this.setState({
        notification: {
          message: "Already shared to this team.",
          type: "error",
          show: true,
        },
      });
      return;
    }

    if(this.state.shareUserGroup === "" && this.state.shareEmail === "" ){
      this.setState({
        notification: {
          message: "User/Team required",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      return;
    }

    console.log("sharedata", shareToData);
    //this.closeShareModal();
    await this.createJob(shareToData);
    this.closeShareModal();
  };

  createJob = async (jobDetails) => {
    this.setState({ isLoading: true });
    const api =
      "/job-scheduling/shareFolder?user=" + localStorage.getItem("id");
    try {
      const response = await addNew(api, jobDetails);
      if (response.status === "success") {
        this.setState({
          notification: {
            message: "Folder Shared Successfully!",
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      //throw error;
    }
  };

  openDeleteModal = (id) => {
    this.setState({ isDeleteModalOpen: true,jobIdToDelete:id });
  };
  closeDeleteModal = () => {
    this.setState({ isDeleteModalOpen: false,jobIdToDelete:null });
  };

  getTeamName = (id) => {
    const matchedTeam = this.state.usersGroup.find((team) => team.id === id);
    return matchedTeam ? matchedTeam.name : null;
  };

  confirmDeleteJob = async () => {
    const {jobIdToDelete}=this.state;
    this.setState({isLoading:true});
    const api=`/job-scheduling/remove-folder-shared/${jobIdToDelete}`;
    try{
      const resp=await deleteById(api);
      if (resp.status === "success") {
        this.setState({
          notification: {
            message: "Folder un-shared Successfully!",
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      this.closeDeleteModal();
      this.fetchSharedFolderList();
    }
    catch(e){
      this.setState({
        notification: {
          message: "Something went wrong!",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  }

  render() {
    console.log("Nested Object Table render");
    const {
      currentData,
      path,
      selectedFile,
      sortKey,
      sortDirection,
      uploadedFiles,
    } = this.state;
    const {
      currentPage,
      itemsPerPage,
      isShareModalOpen,
      isFolderRenameModalOpen,
      shareEmail,
      shareUserGroup,
      isShareExpirationEnabled,
    } = this.state;

    const isShareEmailDisabled = !!shareUserGroup;

    // Filter keys based on file size criteria
    const filteredKeys = Object.keys(currentData).filter((key) => {
      const file = currentData[key];
      const isFile = typeof file !== "object" || Object.keys(file).length === 0;

      if (isFile) {
        const fileDetail = this.props.uploadedFiles.find((item) => {
          const segments = item.filePath
            ? item.filePath.split("/").slice(5).filter(Boolean)
            : [];
          return segments.length > 0 && segments[segments.length - 1] === key;
        });

        return (
          !fileDetail || fileDetail.fileSize === null || fileDetail.fileSize > 2
        );
      }

      return true; // Always show folders
    });

    // Sort the filtered keys based on the sort key and direction
    const sortedKeys =
      filteredKeys.length > 0
        ? this.sortData(
            {
              ...Object.fromEntries(
                filteredKeys.map((key) => [key, currentData[key]])
              ),
            },
            sortKey,
            sortDirection
          )
        : [];

    const totalItems = sortedKeys.length; // Total number of items
    const startIndex = currentPage * itemsPerPage;
    const paginatedKeys = sortedKeys.slice(
      startIndex,
      startIndex + itemsPerPage
    );

    const getStatusColor = (status) => {
      if (status === "green") return "#22c55e";
      if (status === "red") return "#f87171";
      if (status === "yellow") return "#fbbf24";
      return "#94a3b8";
    };
    
    const getApprovalStatusTooltip = (approvalLevels) => {
      if (!approvalLevels || approvalLevels.length === 0) {
        return "Approval workflow started";
      }
    
      // Process all levels and generate status for each
      const statusLines = approvalLevels.map(level => {
        if (level.rejectStatus === true) {
          return `${level.name}: Rejected - ${level.remarks || 'No remarks'}`;
        }
        if (level.levelStatus === true) {
          return `${level.name}: Approved - ${level.remarks || 'No remarks'}`;
        }
        return `${level.name}: Not yet taken any action`;
      });
    
      return statusLines.join('\n');
    };
    
    const getAcknowledgementStatusTooltip = (acknowledgementLevels) => {
      if (!acknowledgementLevels || acknowledgementLevels.length === 0) {
        return "Acknowledgement workflow started";
      }
    
      // Process all levels and generate status for each
      const statusLines = acknowledgementLevels.map(level => {
        if (level.rejectStatus === true) {
          return `${level.name}: Rejected - ${level.remarks || 'No remarks'}`;
        }
        if (level.levelStatus === true) {
          return `${level.name}: Acknowledged - ${level.remarks || 'No remarks'}`;
        }
        return `${level.name}: Not yet acknowledged`;
      });
    
      return statusLines.join('\n');
    };

    const getEsignStatusTooltip = (esignLevels) => {
      if (!esignLevels || esignLevels.length === 0) {
        return "eSign workflow started";
      }
    
      // Process all levels and generate status for each
      const statusLines = esignLevels.map(level => {
        if (level.rejectStatus === true) {
          return `${level.name}: Rejected - ${level.remarks || 'No remarks'}`;
        }
        if (level.levelStatus === true) {
          return `${level.name}: Signed - ${level.remarks || 'No remarks'}`;
        }
        return `${level.name}: Awaiting signature`;
      });
    
      return statusLines.join('\n');
    };

    const getStatusTitle = (status,type) => {
      if(type === "approval"){
        if (status === "green") return "Approval Completed";
        if (status === "red") return "Approval Rejected";
        if (status === "yellow") return "Partially Approved";
        return "Yet to Approve";
      }
      if(type === "acknowledgement"){
        if (status === "green") return "Acknowledgement Completed";
        //if (status === "red") return "Acknowledgement Rejected";
        if (status === "yellow") return "Partially Acknowledged";
        return "Yet to Acknowledge";
      }
      if(type === "eSign"){
        if (status === "green") return "eSign Completed";
        if (status === "red") return "eSign Rejected";
        if (status === "yellow") return "Partially signed";
        return "Yet to sign";
      }      
    };

    if (selectedFile) {
      return (
        <div>
          <FileDetail
            from={this.props.from}
            fileName={selectedFile.documentName}
            fileUrl={selectedFile.filePath}
            fileId={selectedFile.documentsAttachmentId}
            onClose={() => this.setState({ selectedFile: null })}
          />
        </div>
      );
    }

    return (
      <div
        className="table table-hover table-striped"
        style={{ marginTop: "-10px" }}
      >
        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}
        <div style={{ marginTop: "0", paddingTop: "0", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {
            this.props.from !== "teams" ?  
              <h6 style={{ marginTop: "5px", marginBottom: "8px", flex: 1, paddingTop: "10px" }}>
                My Docs: {path.join(" / ") || "Inbox"}
              </h6>
              :
              <h6 style={{ marginTop: "5px", marginBottom: "8px", flex: 1, paddingTop: "10px" }}>
                My Docs: {path.join(" / ") || "Teams"}
              </h6>
          }
          {path.length > 0 && (
            <span style={{ cursor: "pointer" }}>
              <span className="text-danger" onClick={this.handleBack}>
                <i className="fa fa-arrow-left text-danger"></i>&nbsp;Back
              </span>
            </span>
          )}
        </div>

        {currentData && (
          <div>
            {/* Transform the data structure for DataTable */}
            {(() => {
  // Use the paginatedKeys from the render method
  // Create data array for DataTable
  const tableData = paginatedKeys.map((key, index) => {
    const isFile =
      typeof currentData[key] !== "object" ||
      Object.keys(currentData[key]).length === 0;
    const fileDetail = this.props.uploadedFiles.find((item) => {
      const segments = item.filePath
        ? item.filePath.split("/").slice(5).filter(Boolean)
        : [];
      return (
        segments.length > 0 && segments[segments.length - 1] === key
      );
    });

    console.log(fileDetail?.esignWorkflow?.approvalLevelDTO)

    // Calculate workflow status colors - only if workflow exists and has approvalLevelDTO
    let approvalColor = null;
    if (fileDetail?.approveWorkflow?.approvalLevelDTO?.length > 0) {
      const levels = fileDetail.approveWorkflow.approvalLevelDTO;
      const hasReject = levels.some(
        (level) => level.rejectStatus === true
      );
      const allApproved = levels.every(
        (level) => level.levelStatus === true
      );
      const hasPending = levels.some(
        (level) => level.levelStatus === true && !allApproved
      );

      if (hasReject) approvalColor = "red";
      else if (allApproved) approvalColor = "green";
      else if (hasPending) approvalColor = "yellow";
    }

    let ackColor = null;
    if (fileDetail?.acknowledgementWorkflow?.approvalLevelDTO?.length > 0) {
      const acklevels =
        fileDetail.acknowledgementWorkflow.approvalLevelDTO;
      const hasAckReject = acklevels.some(
        (level) => level.rejectStatus === true
      );
      const allAckApproved = acklevels.every(
        (level) => level.levelStatus === true
      );
      const hasAckPending = acklevels.some(
        (level) => level.levelStatus === true && !allAckApproved
      );

      if (hasAckReject) ackColor = "red";
      else if (allAckApproved) ackColor = "green";
      else if (hasAckPending) ackColor = "yellow";
    }

    let eSignColor = null;
    if (fileDetail?.esignWorkflow?.approvalLevelDTO?.length > 0) {
      const eSignlevels = fileDetail.esignWorkflow.approvalLevelDTO;
      const hasESignReject = eSignlevels.some(
        (level) => level.rejectStatus === true
      );
      const allESignApproved = eSignlevels.every(
        (level) => level.levelStatus === true
      );
      const hasESignPending = eSignlevels.some(
        (level) => level.levelStatus === true && !allESignApproved
      );

      if (hasESignReject) eSignColor = "red";
      else if (allESignApproved) eSignColor = "green";
      else if (hasESignPending) eSignColor = "yellow";
    }

    // Return the data object for this row
    return {
      sno: index + 1,
      key: key,
      name: key,
      isFile: isFile,
      fileDetail: fileDetail,
      type: isFile ? "file" : "folder",
      approvalColor: approvalColor,
      ackColor: ackColor,
      eSignColor: eSignColor,
    };
  });

  // Define columns for DataTable
  const columns = [
    {
      key: "sno",
      header: "SNo",
      width: "10%",
      sortable: false,
    },
    {
      key: "name",
      header: "Name",
      width: "50%",
      sortable: true,
      render: (value, row) => (
        <>
          <span
            onClick={() =>
              row.isFile
                ? this.handleFileClick(
                    row.key,
                    row.fileDetail?.filePath
                  )
                : this.handleFolderClick(row.key)
            }
            style={{
              color: "blue",
              cursor: "pointer",
            }}
          >
            {row.key}
          </span>
          {row.isFile && (
            <>
              {/* Approval Icon - only show if workflow exists and has approvalLevelDTO */}
              {row.fileDetail?.approveWorkflow?.approvalLevelDTO?.length > 0 && (
                <i
                  className="fa fa-check-circle"
                  style={{
                    color: row.approvalColor ? getStatusColor(row.approvalColor) : "gray",
                    fontSize: 20,
                    marginLeft: 8,
                    verticalAlign: "middle",
                    opacity: 1,
                  }}
                  title={`${row.approvalColor ? getStatusTitle(row.approvalColor, "approval") : "Approval workflow started"}\n\n${getApprovalStatusTooltip(row.fileDetail.approveWorkflow.approvalLevelDTO)}`}                />
              )}

              {/* Acknowledgement Icon - only show if workflow exists and has approvalLevelDTO */}
              {row.fileDetail?.acknowledgementWorkflow?.approvalLevelDTO?.length > 0 && (
                <i
                  className="fa fa-handshake-o"
                  style={{
                    color: row.ackColor ? getStatusColor(row.ackColor) : "gray",
                    fontSize: 20,
                    marginLeft: 8,
                    verticalAlign: "middle",
                    opacity: 1,
                  }}
                  title={`${row.ackColor ? getStatusTitle(row.ackColor, "acknowledgement") : "Acknowledgement workflow started"}\n\n${getAcknowledgementStatusTooltip(row.fileDetail.acknowledgementWorkflow.approvalLevelDTO)}`}                />
              )}

              {/* ESign Icon - only show if workflow exists and has approvalLevelDTO */}
              {row.fileDetail?.esignWorkflow?.approvalLevelDTO?.length > 0 && (
                <span
                  style={{
                    position: "relative",
                    display: "inline-block",
                    marginLeft: 8,
                    verticalAlign: "middle",
                  }}
                  title={`${row.esignColor ? getStatusTitle(row.esignColor, "eSign") : "eSign workflow started"}\n\n${getEsignStatusTooltip(row.fileDetail.esignWorkflow.approvalLevelDTO)}`}                >
                  <i
                    className="fa fa-hand-paper-o"
                    style={{
                      color: row.eSignColor ? getStatusColor(row.eSignColor) : "gray",
                      fontSize: 18,
                      transform: "rotate(-45deg)",
                      position: "relative",
                    }}
                  />
                  <i
                    className="fa fa-pencil"
                    style={{
                      color: row.eSignColor ? getStatusColor(row.eSignColor) : "gray",
                      fontSize: 14,
                      position: "absolute",
                      top: -2,
                      right: -2,
                    }}
                  />
                </span>
              )}
            </>
          )}
        </>
      ),
    },
    {
      key: "type",
      header: "Type",
      width: "15%",
      sortable: false,
      render: (value, row) => {
        let iconClass = "fa fa-file";
        let iconColor = "#94a3b8"; // default gray
        let fileTypeText = "File";
        
        if (!row.isFile) {
          iconClass = "fa fa-folder";
          iconColor = "#4361ee"; // blue for folder
          fileTypeText = "Folder";
        } else if (row.fileDetail && row.fileDetail.filePath) {
          const ext = row.fileDetail.filePath
            .split(".")
            .pop()
            .toLowerCase();
          
          // Check if it's a link type
          if (row.fileDetail.linkPath) {
            iconClass = "fa fa-link";
            iconColor = "#8b5cf6"; // purple for links
            fileTypeText = "Link";
          } else {
            // Handle different file extensions
            switch (ext) {
              case "pdf":
                iconClass = "fa fa-file-pdf-o";
                iconColor = "#dc2626"; // red for PDF
                fileTypeText = "PDF";
                break;
              case "xls":
              case "xlsx":
                iconClass = "fa fa-file-excel-o";
                iconColor = "#059669"; // green for Excel
                fileTypeText = "Excel";
                break;
              case "doc":
              case "docx":
                iconClass = "fa fa-file-word-o";
                iconColor = "#2563eb"; // blue for Word
                fileTypeText = "Word";
                break;
              case "ppt":
              case "pptx":
                iconClass = "fa fa-file-powerpoint-o";
                iconColor = "#ea580c"; // orange for PowerPoint
                fileTypeText = "PowerPoint";
                break;
              case "txt":
                iconClass = "fa fa-file-text-o";
                iconColor = "#6b7280"; // gray for text files
                fileTypeText = "Text";
                break;
              case "jpg":
              case "jpeg":
              case "png":
              case "gif":
              case "bmp":
              case "svg":
                iconClass = "fa fa-file-image-o";
                iconColor = "#d946ef"; // magenta for images
                fileTypeText = "Image";
                break;
              case "zip":
              case "rar":
              case "7z":
              case "tar":
                iconClass = "fa fa-file-archive-o";
                iconColor = "#7c3aed"; // purple for archives
                fileTypeText = "Archive";
                break;
              case "mp4":
              case "avi":
              case "mov":
              case "wmv":
              case "mkv":
                iconClass = "fa fa-file-video-o";
                iconColor = "#dc2626"; // red for videos
                fileTypeText = "Video";
                break;
              case "mp3":
              case "wav":
              case "flac":
              case "aac":
                iconClass = "fa fa-file-audio-o";
                iconColor = "#16a34a"; // green for audio
                fileTypeText = "Audio";
                break;
              default:
                iconClass = "fa fa-file-o";
                iconColor = "#6b7280";
                fileTypeText = "File";
            }
          }
        }
        
        return (
          <div className="d-flex align-items-center">
            <i
              className={iconClass}
              style={{ 
                fontSize: "24px", 
                color: iconColor,
                marginRight: "8px"
              }}
            ></i>
            <small className="text-muted">{fileTypeText}</small>
          </div>
        );
      },
    },
    {
      key: "action",
      header: "Action",
      width: "25%",
      sortable: false,
      render: (value, row) => (
        <>
          <button
            title="view"
            style={{ background: "none", border: "none" }}
            onClick={() =>
              row.isFile
                ? this.handleFileClick(
                    row.key,
                    row.fileDetail?.filePath
                  )
                : this.handleFolderClick(row.key)
            }
          >
            <i
              className="fa fa-eye"
              style={{ color: "#4361ee" }}
            ></i>
          </button>
          &nbsp;&nbsp;
          {!row.isFile && (
            <button
              title="delete folder"
              style={{ background: "none", border: "none" }}
              onClick={() => this.handleDeleteFolderModal(row.key)}
            >
              <i
                className="fa fa-trash"
                style={{ color: "#f87171" }}
              ></i>
            </button>
          )}
          {!row.isFile && (
            <button
              title="share folder"
              style={{ background: "none", border: "none" }}
              onClick={() => this.handleShareModal(row.key)}
            >
              <i
                className="fa fa-share-alt"
                style={{ color: "#4ade80" }}
              ></i>
            </button>
          )}
          {!row.isFile && (
            <button
              title="edit name"
              style={{ background: "none", border: "none" }}
              onClick={() => this.handleFolderRenameModal(row.key)}
            >
              <i
                className="fa fa-edit"
                style={{ color: "#4361ee" }}
              ></i>
            </button>
          )}
        </>
      ),
    },
  ];

  return (
    <DataTable
      data={tableData}
      columns={columns}
      className={classes.fileTable}
      searchable={false} // Disable internal search
      itemsPerPage={itemsPerPage} // Use the itemsPerPage from state
      totalItems={totalItems} // Use the totalItems calculated above
      currentPage={currentPage}
      onPageChange={(page) => this.handlePageChange({ selected: page })}
      onItemsPerPageChange={(size) => this.handleItemsPerPageChange(size)}
    />
  );
})()}
          </div>
        )}

        <div>
          <Modal
            show={isShareModalOpen}
            onHide={this.closeShareModal}
            size="md"
            dialogClassName={classes.centeredModal}
          >            <Modal.Header
              closeButton
              className="modal-header-modern"
            >
              <Modal.Title>
                Share Folder
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div>Share Folder {this.state.fullPath}</div>
              <div className="d-flex align-items-center mt-3">
                {/* <input
                  type="email"
                  placeholder="share to (e-mail)"
                  className="form-control me-2"
                  value={shareEmail}
                  onChange={this.handleShareEmailChange}
                  disabled={isShareEmailDisabled}
                  onInput={() => {
                    this.setState({ isShareInputGiven: true });
                  }}
                /> */}
                <select
                  className="form-control bg-info text-white"
                  value={shareEmail}
                  onChange={this.handleShareEmailChange}
                  disabled={isShareEmailDisabled}
                  onSelect={() => {
                    this.setState({ isShareInputGiven: true });
                  }}
                >
                  <option value="">-- Select User --</option>
                  {this.state.userList && this.state.userList.length > 0 ? (
                    this.state.userList.map((user, index) => (
                      <option key={index} value={user.email}>
                        {user.firstName} {user.lastName}
                      </option>
                    ))
                  ) : (
                    <option value="">No Users available</option>
                  )}
                </select>
                or&nbsp;&nbsp;
                <select
                  className="form-control bg-info text-white"
                  value={shareUserGroup}
                  onChange={this.handleShareUserGroupChange}
                  onSelect={() => {
                    this.setState({ isShareInputGiven: true });
                  }}
                >
                  <option value="">-- Select User Group --</option>
                  {this.state.usersGroup && this.state.usersGroup.length > 0 ? (
                    this.state.usersGroup.map((group, index) => (
                      <option key={index} value={group.id}>
                        {group.name}
                      </option>
                    ))
                  ) : (
                    <option value="">No User Groups Available</option>
                  )}
                </select>
              </div>
              <div className="mt-3">
                <select
                  className="form-control bg-info text-white"
                  name="viewType"
                  value={this.state.viewType}
                  onChange={this.selectedShareValue}
                >
                  {/* <option value="Preview">Preview</option> */}
                  <option value="Viewer">Viewer</option>
                  <option value="Editor">Editor</option>
                  {/* <option value="Custom">Custom</option> */}
                </select>
              </div>
              <div className="mt-3 d-flex align-items-center">
                <label className={classes.slider}>
                  <input
                    type="checkbox"
                    checked={isShareExpirationEnabled}
                    onChange={this.toggleShareExpiration}
                  />
                  <div className={classes.sliderTrack}>
                    <div className={classes.sliderThumb}></div>
                  </div>
                </label>
                &nbsp;&nbsp;
                <span className="ml-2">Expiration</span>
                {isShareExpirationEnabled && (
                  <div className="ms-auto mt-2">
                    <Form.Group controlId="shareExpirationDate">
                      {/* <Form.Control
                        type="datetime-local"
                        value={this.state.shareExpirationDate}
                        onChange={this.handleShareDateChange}
                      /> */}
                      <ReactDatePicker
                        selected={this.state.shareExpirationDate}
                        onChange={(date) =>
                          this.setState({ shareExpirationDate: date })
                        }
                        showTimeSelect
                        minDate={new Date()}
                        dateFormat="dd/MM/yyyy HH:mm"
                        locale="enIN"
                        timeIntervals={15}
                        minTime={
                          this.state.shareExpirationDate &&
                          new Date().getDate() === this.state.shareExpirationDate.getDate()
                            ? new Date()
                            : new Date(new Date().setHours(0, 0, 0, 0))
                        }
                        maxTime={new Date(new Date().setHours(23, 59, 59, 999))}
                      />
                    </Form.Group>
                  </div>
                )}
              </div>
              <div>
                {this.state.folderSharedList.length > 0 && 
                  this.state.folderSharedList.map((share, idx) => (
                                            <div
                                              className={`d-flex justify-content-between align-items-strech gap-1 mb-2 ${classes.auditlogHeader}`}
                                              key={idx}
                                            >
                                              <div className="text-start">
                                                <small className="d-block">
                                                  {share.email
                                                    ? share.email
                                                    : this.getTeamName(share.teamId)}
                                                </small>
                                                {share.messageStatus && (
                                                  <small className="text-secondary d-block" style={{ fontSize: "0.75rem" }}>
                                                    Permission: {share.messageStatus}
                                                  </small>
                                                )}
                                                {share.jobDate && (
                                                  <small className="text-muted d-block" style={{ fontSize: "0.75rem" }}>
                                                    Expires: {formatDate(share.jobDate)}
                                                  </small>
                                                )}
                                              </div>
                                              <div className={`d-flex align-items-center gap-1 ${classes.auditlogHeader}`}>
                                                <BsTrash
                                                  color="red"
                                                  size={14}
                                                  style={{ cursor: "pointer" }}
                                                  onClick={()=>this.openDeleteModal(share.id)}
                                                  title="Remove share"
                                                />
                                              </div>
                                            </div>
                                          )) 
                }
              </div>
              <div className="mt-3">
                <Button
                  variant="info"
                  // disabled={!this.state.isShareInputGiven}
                  onClick={this.handleShareTo}
                >
                  Share
                </Button>
              </div>
            </Modal.Body>
          </Modal>
        </div>


        <div>
           <Modal
                        show={this.state.isDeleteModalOpen}
                        onHide={this.closeDeleteModal}
                        size="sm"
                      >
                        <Modal.Header
                          closeButton
                          className="modal-header-modern py-2"
                        >
                          <Modal.Title style={{ fontSize: "1rem" }}>
                            Delete shared folder
                          </Modal.Title>
                        </Modal.Header>
                        <Modal.Body className="p-3">
                          <p className="small">Do you want to delete the shared file?</p>
                        </Modal.Body>
                        <Modal.Footer className="py-2">
                          <Button variant="danger" size="sm" onClick={this.closeDeleteModal}>
                            Close
                          </Button>
                          <Button variant="danger" size="sm" onClick={this.confirmDeleteJob}>
                            Yes, Unshare
                          </Button>
                        </Modal.Footer>
                      </Modal>
        </div>
        <div>
          <Modal
            show={this.state.isFolderRenameModalOpen}
            onHide={this.closeFolderRenameModal}
          >            <Modal.Header
              closeButton
              className="modal-header-modern"
            >
              <Modal.Title>
                Rename Folder
              </Modal.Title>
            </Modal.Header>
            <Modal.Body>
              {/* <h5>Create Folder</h5> */}
              <div>
                <div className="row">
                  <div className="col-sm-7">
                    <input
                      type="text"
                      name="folderRnName"
                      id="folderRename"
                      placeholder="Folder Name"
                      className="form-control"
                      required
                      value={this.state.folderRename}
                      onChange={(event) => {
                        this.setState({ folderRename: event.target.value });
                      }}
                    />
                  </div>
                </div>
                <div className="row mt-3">
                  <div className="col-12 text-center">
                    <Button
                      variant="primary"
                      onClick={this.handleSubmitFolderRenameModal}
                      disabled={!this.state.folderRename}
                    >
                      Rename
                    </Button>
                  </div>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </div>

        <Modal
          show={this.state.isFolderDeleteModalOpen}
          onHide={this.closeFolderDeleteModal}
        >          <Modal.Header
            closeButton
            className="modal-header-modern"
          >
            <Modal.Title>
              Delete Folder
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>Do you want to delete the folder?</Modal.Body>          <Modal.Footer className="modal-footer-modern">
            <Button
              variant="danger"
              onClick={() => this.handleDeleteFolderClick(this.state.key)}
            >
              Delete
            </Button>
          </Modal.Footer>
        </Modal>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default NestedObjectTable;
