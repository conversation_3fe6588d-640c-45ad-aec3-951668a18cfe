.bgTop {
    background-color: #f5f7fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Tab styles */
:global(.nav-tabs) {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

:global(.nav-tabs .nav-link) {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 10px 20px;
    margin-right: 5px;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
}

:global(.nav-tabs .nav-link:hover) {
    color: #4a90e2;
    background-color: #f8f9fa;
    border-color: transparent;
}

:global(.nav-tabs .nav-link.active) {
    color: #4a90e2;
    background-color: #fff;
    border-bottom: 3px solid #4a90e2;
    font-weight: 600;
}

:global(.tab-content) {
    padding: 15px 0;
}

.uploadDiv {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: row;
    margin-right: 20px;
    margin-left: 20px;
    gap: 10px;
}

.marginnSide {
    display: flex;
    justify-content: flex-end;
}

.searchIcon {
    font-size: 16px;
    color: white;
    background-color: #4a90e2;
    padding: 10px;
    margin-left: 0.7rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.searchIcon:hover {
    background-color: #3a7bc8;
    transform: scale(1.05);
}

.fileTable {
    margin-top: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.fileTable th {
    background-color: #f5f7fa;
    color: #333;
    font-weight: 600;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
}

.fileTable th:hover {
    background-color: #e9ecef;
}

.fileTable td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
}

.fileTable tr:hover {
    background-color: #f8f9fa;
}

.centeredModal {
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.slider input {
    opacity: 0;
    width: 0;
    height: 0;
}

.sliderTrack {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
}

.sliderThumb {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .sliderTrack {
    background-color: #4a90e2;
}

input:checked + .sliderTrack .sliderThumb {
    transform: translateX(20px);
}

@media (max-width: 768px) {
    .d-flex {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .marginnSide {
        margin-top: 15px;
        justify-content: center;
    }
    
    .fileTable th, .fileTable td {
        padding: 8px 10px;
    }
}

@media (max-width: 500px) {
    .d-flex {
        justify-content: center;
    }
    
    .uploadDiv {
        margin-right: 10px;
        margin-left: 10px;
    }
}
