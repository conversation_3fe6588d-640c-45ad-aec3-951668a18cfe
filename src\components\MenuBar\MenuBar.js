import React, { useState } from 'react';
import classes from './MenuBar.module.css';
import Container from 'react-bootstrap/Container';
import Nav from 'react-bootstrap/Nav';
import Navbar from 'react-bootstrap/Navbar';
import NavDropdown from 'react-bootstrap/NavDropdown';
import { Modal, Form, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router';
import axios from 'axios';
//import logo from '../images/DMSLogo.png';
import { GlobalConstants } from '../../constants/global-constants';
import { NavLink } from 'react-router-dom';


function MenuBar(props) {
  const [showModal, setShowModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword,setShowCurrentPassword]=useState(false);
  const [showNewPassword,setShowNewPassword]=useState(false);
  const [showConfirmPassword,setShowConfirmPassword]=useState(false);
  const [isPasswordMissMatch,setIsPasswordMissMatch] =useState(false);
  const [result,setResult]=useState(false);
  const [userName,setUserName]=useState(localStorage.getItem("userName"));

  const handleShow = () => setShowModal(true);
  const handleClose = () => {
    setShowModal(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
  };
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Current Password:', currentPassword);
    console.log('New Password:', newPassword);
    console.log('Confirm New Password',confirmPassword)
    if(newPassword!==confirmPassword){
      setIsPasswordMissMatch(true);
      setConfirmPassword('');
      return;
    }
    changePassword();
  };
  const changePassword=async ()=>{
    const existingPassword=currentPassword;
    let api = `${GlobalConstants.globalURL}/user/user_change_password/${localStorage.getItem("id")}`;
    try{
      const data=await axios.get(api,{params:{existingPassword,newPassword}})
      handleClose();
      navigate("/");
    }
    catch(error){
      setResult(true);
    }
  }

  const handleLogout = () => {
    // Clear the token and other relevant user information
    localStorage.removeItem('token');
    localStorage.removeItem('userName');
    localStorage.removeItem('firstName');
    localStorage.removeItem('id');
    localStorage.removeItem('companyId');
    localStorage.removeItem('role');
    localStorage.removeItem('companyName');

    // Redirect to the login page
    navigate('/', { replace: true });
  };

  return (<>

    <Navbar collapseOnSelect expand="lg" className={`${classes.myMenu} ${classes.myCustomNavbar}`}>
      <Container fluid>
        <Navbar.Brand as={NavLink} to="/newDS/home">
          <img className="" alt="logo" src={process.env.PUBLIC_URL + '/images/DMSLogo.png'} />
        </Navbar.Brand>
        <Navbar.Brand as={NavLink} to="/newDS/home" className={classes.companyContainer}>
          <p className={`${classes.company}`}>{localStorage.getItem("companyName")}</p>
        </Navbar.Brand>
        <Navbar.Toggle aria-controls="responsive-navbar-nav" />
        <Navbar.Collapse id="responsive-navbar-nav">


          {/* Profile Dropdown */}
            <Nav className="ms-auto">
            <NavDropdown title={<i className="fa fa-user"></i>}
                         id="profile"
                         className={classes.positionMenu}>
              <p style={{fontSize:"16px",fontWeight:"800",textAlign:"center"}}>{userName}</p>
              <div style={{ display: "flex",
                            justifyContent: "space-between",
                            width: "100%",
                            alignItems: "center",
                            gap: "40px",
                            padding: "0 10px"}}>
                  <NavDropdown.Item href="#"
                                    onClick={handleShow}
                                    style={{textAlign:"left",
                                      width: "100%",
                                      padding: "10px",
                                      display: "block"
                                    }}
                                    onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#8fc7e6'}
                                    onMouseOut={(e) => e.currentTarget.style.backgroundColor = ''}>
                            Change Password
                  </NavDropdown.Item>
                  <NavDropdown.Item onClick={handleLogout}
                                    style={{textAlign:"right",
                                      width: "100%",
                                      padding: "10px",
                                      display: "block"
                                    }}
                                    onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#8fc7e6'}
                                    onMouseOut={(e) => e.currentTarget.style.backgroundColor = ''}>
                            Logout
                  </NavDropdown.Item>
              </div>
            </NavDropdown>
          </Nav>
            <div>
              Welcome, {userName}
            </div>

        </Navbar.Collapse>
      </Container>
    </Navbar>

    <Modal show={showModal} onHide={handleClose} size="md" centered className='p-3'>
      <Modal.Header closeButton className='bg-info'>
        <Modal.Title style={{fontSize:'18px'}}>Change Password</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showCurrentPassword?'text':'password'}
              placeholder="Enter current password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
            <Button onClick={()=>setShowCurrentPassword(!showCurrentPassword)}>
              {showCurrentPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            </div>
            <br></br>
          </Form.Group>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showNewPassword?'text':'password'}
              placeholder="Enter new password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
            <Button onClick={()=>setShowNewPassword(!showNewPassword)}>
              {showNewPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            </div>
          </Form.Group>
            <br></br>
          <Form.Group>
          <div className="input-group">
            <Form.Control
              type={showConfirmPassword?'text':'password'}
              placeholder="Re-enter new password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
            <Button onClick={()=> setShowConfirmPassword(!showConfirmPassword)} >
              {showConfirmPassword?<i class="fa fa-eye-slash"></i>:<i className='fa fa-eye'></i>}
            </Button>
            {isPasswordMissMatch && <p className='text-danger'>Please Confirm New Password</p> }
            {result && <p className='text-danger'>Current Password does not match with DB</p> }
          </div>
          </Form.Group><br></br>
          <Button variant="primary" type="submit" >
            Submit
          </Button>
        </Form>
      </Modal.Body>
    </Modal></>
  );
}

export default MenuBar;