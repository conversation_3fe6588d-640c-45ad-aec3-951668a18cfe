import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register required Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const BarChart = (props) => {
  // Default color palette
  const colorPalette = [
    '#4338ca', '#059669', '#0891b2', '#db2777', '#8b5cf6',
    '#f59e0b', '#10b981', '#3b82f6', '#6366f1', '#ec4899'
  ];

  // Prepare chart data based on props
  const prepareChartData = () => {
    // If data prop is provided, use that (from widgetReports)
    if (props.data) {
      const labels = Object.keys(props.data);
      const values = Object.values(props.data);
      
      return {
        labels: labels,
        datasets: [
          {
            label: 'Document Count',
            data: values,
            backgroundColor: labels.map((_, index) => colorPalette[index % colorPalette.length]),
            borderColor: labels.map((_, index) => colorPalette[index % colorPalette.length]),
            borderWidth: 2,
            borderRadius: 4,
            borderSkipped: false,
          },
        ],
      };
    }
    
    // Fallback to individual props if data prop isn't provided
    return {
      labels: ['Duplicate', 'Pending Workflows', 'Retention End', 'Due Date', 'Archive'],
      datasets: [
        {
          label: 'Document Count',
          data: [
            props.duplicateCount || 0,
            props.pendingWFCount || 0,
            props.retentionCount || 0,
            props.dueCount || 0,
            props.archiveCount || 0
          ],
          backgroundColor: [
            '#4338ca',
            '#059669',
            '#0891b2',
            '#db2777',
            '#8b5cf6'
          ],
          borderColor: [
            '#4338ca',
            '#059669',
            '#0891b2',
            '#db2777',
            '#8b5cf6'
          ],
          borderWidth: 2,
          borderRadius: 4,
          borderSkipped: false,
        },
      ],
    };
  };

  const data = prepareChartData();
  console.log("barchart",data);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Document Overview',
        font: {
          size: 14,
          weight: 'bold'
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 6,
        displayColors: false,
        callbacks: {
          title: function(context) {
            return context[0].label;
          },
          label: function(context) {
            return `Count: ${context && context.parsed.y.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          lineWidth: 1
        },
        ticks: {
          font: {
            size: 10
          },
          color: '#6b7280',
          callback: function(value) {
            return value.toLocaleString();
          }
        },
        border: {
          display: false
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 10,
            weight: '500'
          },
          color: '#374151',
          maxRotation: 45,
          minRotation: 0
        },
        border: {
          display: false
        }
      },
    },
    layout: {
      padding: {
        top: 10,
        right: 15,
        bottom: 10,
        left: 15
      }
    },
    animation: {
      duration: 750,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index'
    }
  };

  return (
    <div style={{ 
      position: 'relative',
      width: '100%', 
      height: '100%',
      minWidth: '200px',
      minHeight: '150px',
      overflow: 'hidden'
    }}>
      <Bar data={data} options={options} />
    </div>
  );
};

export default BarChart;