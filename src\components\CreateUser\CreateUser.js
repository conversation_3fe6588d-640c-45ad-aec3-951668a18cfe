import React, { Component } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import classes from "./CreateUser.module.css";
import {
  getList,
  addNew,
  editById,
  deleteById,
} from "../../services/apiService";
import Notification from "../Notification/Notification";
import Loader from "../loader/Loader";
import { DataTable } from "../Table/DataTable";
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'

export class CreateUser extends Component {
  state = {
    isOpen: false,
    isEditing: false,
    isUserExist: false,
    userList: [],
    phoneError:'',
    newUser: {
      userName: "",
      firstName: "",
      lastName: "",
      mobile: "",
      email: "",
    },
    showDeleteConfirm: false,
    userIdToDelete: null,
    ownerId: null,
    showError: "",
    filteredUserList: [],
    notification: {
      message: "",
      type: "",
      show: false,
    },
    currentPage: 0,
    totalItems: 0,
    itemsPerPage: 10,
    search: "",
    isLoading: false,
    documentsToTransfer: [],
    selectedDocuments: [],
    docsExists: false,
  };

  componentDidMount() {
    this.fetchUserList();
  }

  handleSearchInputChange = (event) => {
    const search = event.target.value;
    this.setState({ search }, () => {
      console.log(search);
      this.fetchUserList();
    });
  };

  fetchUserList = async (page = 0) => {
    const { itemsPerPage } = this.state;
    const api = `/user/list?page=${page}&size=${itemsPerPage}&search=${this.state.search}&sort=`;
    this.setState({ isLoading: true });
    try {
      const response = await getList(api);
      const data = response.data;
      this.setState({
        userList: data.content,
        totalItems: data.totalElements,
        currentPage: page,
        isLoading: false,
      });
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handlePageChange = (page) => {
    this.fetchUserList(page);
  };

  timer = null;
  check = async (userName) => {
    if (!userName.trim()) return;
    //console.log(userName);
    this.setState({ isUserExist: false });
    const api = `/user/validateUser?userName=${userName}`;
    try {
      const response = await getList(api);
      console.log(response);
      this.setState({ isUserExist: response });
    } catch (error) {}
  };
  handleEmailChange = (e) => {
    const emailValue = e.target.value;
    this.setState((prevState) => ({
      newUser: {
        ...prevState.newUser,
        email: emailValue,
      },
    }));

    if (this.timer) clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.check(emailValue);
    }, 500);
  };

  handleInputChange = (e) => {
    const { name, value } = e.target;
    this.setState((prevState) => ({
      newUser: {
        ...prevState.newUser,
        [name]: value,
      },
    }));
  };

  createUser = async (newUser) => {
    console.log(newUser);
    const api = "/user";
    this.setState({ isLoading: true });
    try {
      const response = await addNew(api, newUser);
      if (response.status === "success") {
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
      throw error;
    }
  };

  editUser = async (userId, updatedUser) => {
    const api = `/user/${userId}`;
    this.setState({ isLoading: true });
    console.log(updatedUser);
    try {
      const response = await editById(api, updatedUser);
      if (response.status === "success") {
        this.setState({
          notification: {
            message: response.message,
            type: "success",
            show: true,
          },
          isLoading: false,
        });
      }
      return response.data;
    } catch (error) {
      this.setState({
        notification: {
          message: "Something went Wrong",
          type: "error",
          show: true,
        },
        isLoading: false,
      });
    }
  };

  handleSubmit = async (e) => {
    e.preventDefault();
    const { newUser, isEditing } = this.state;

    // if(this.state.phoneError){
    //   return;
    // }
    try {
      if (isEditing) {
        await this.editUser(newUser.id, newUser);
      } else {
        await this.createUser(newUser);
      }
      this.fetchUserList();
      this.closeModal();
    } catch (error) {
      console.log(error);
    }
    this.closeModal();
  };

  fetchDocumentCount = async (id) => {
    const api =
      "/documentsattachmentdetail/list_by_employee/" +
      id +
      `?page=0&size=10&searchParam=`;
    try {
      const response = await getList(api);
      const data = response.data;
      if (data.totalElements > 0) {
        this.setState({ docsExists: true });
      }
    } catch (error) {}
  };

  openDeleteConfirm = async (userId) => {
    await this.fetchDocumentCount(userId);
    await this.fetchSharedDocumentsByUserId(userId);
    const api = `/user/list?page=0&size=1000&search=&sort=`;
    try {
      const response = await getList(api);
      const data = response.data;
      const filteredUserList = data.content.filter(
        (user) => user.id !== userId
      );
      this.setState({
        showDeleteConfirm: true,
        userIdToDelete: userId,
        filteredUserList: filteredUserList,
      });
    } catch (error) {}
  };

  fetchSharedDocumentsByUserId = async (id) => {
    const api = `/documentsattachmentdetail/shared_documents_list_by_employee/${id}`;
    try {
      const response = await getList(api);
      const data = response.data;
      console.log("Documents : ", data);
      this.setState({
        documentsToTransfer: data || [],
        selectedDocuments: [], // Initialize as empty array
      });
    } catch (error) {
      console.error("Error fetching documents:", error);
      this.setState({ documentsToTransfer: [] });
    }
  };

  closeDeleteConfirm = () => {
    this.fetchUserList();
    this.setState({
      showDeleteConfirm: false,
      userIdToDelete: null,
      ownerId: null,
      showError: "",
      filteredUserList: [],
      docsExists: false,
    });
  };

  confirmDeleteUser = async () => {
    if (!this.state.docsExists) {
      // alert("docs does not exists");
      const api = `/user/${this.state.userIdToDelete}`;
      try {
        const resp = await deleteById(api);
        this.setState({
          notification: {
            message: resp.message,
            type: "success",
            show: true,
          },
        });
      } catch (error) {
        console.error(
          "Error deleting user:",
          error.response ? error.response.data : error.message
        );
        this.setState({
          notification: {
            message: "Something went wrong",
            type: "error",
            show: true,
          },
        });
      } finally {
        this.closeDeleteConfirm();
      }
      return;
    }

    const { userIdToDelete, ownerId, selectedDocuments } = this.state;
    if (!ownerId) {
      this.setState({
        showError: "User is required",
      });
      return;
    }

    const docs = {
      selectedDocs: [],
    };

    if (selectedDocuments.length > 0) {
      docs.selectedDocs = selectedDocuments;
    }

    const api = `/user/${userIdToDelete}?newOwnerId=${ownerId}`;

    try {
      const resp = await deleteById(api, docs);
      this.fetchUserList();
      this.setState({
        notification: {
          message: resp.message,
          type: "success",
          show: true,
        },
      });
    } catch (error) {
      console.error(
        "Error deleting user:",
        error.response ? error.response.data : error.message
      );
      this.setState({
        notification: {
          message: "Something went wrong",
          type: "error",
          show: true,
        },
      });
    } finally {
      this.closeDeleteConfirm();
    }
  };

  closeNotification = () => {
    this.setState({
      notification: { ...this.state.notification, show: false },
    });
  };

  openModal = (user = null) => {
    if (user) {
      this.setState({
        isOpen: true,
        isEditing: true,
        newUser: { ...user },
      });
    } else {
      this.setState({
        isOpen: true,
        isEditing: false,
        newUser: {
          userName: "",
          firstName: "",
          lastName: "",
          mobile: "",
          email: "",
        },
      });
    }
  };

  closeModal = () => {
    this.setState({
      isOpen: false,
      isEditing: false,
      newUser: {
        userName: "",
        firstName: "",
        lastName: "",
        mobile: "",
        email: "",
      },
    });
  };

  handlePhoneChange = (value, country) => {
    this.setState({ phoneError: '' });
    let phoneError = '';
  
    // 1. First check for invalid characters
    if (value.match(/[^0-9+() -]/)) {
      phoneError = 'Invalid phone number format';
    } 
    else {
      // 2. Get clean digits only (no formatting)
      const digitsOnly = value.replace(/\D/g, '');
      
      // 3. Extract number without country code
      const numberWithoutCountryCode = digitsOnly.startsWith(country.dialCode)
        ? digitsOnly.slice(country.dialCode.length)
        : digitsOnly;
      
      // 4. Get the exact expected digit count from country format
      const fullFormatDigits = country.format.replace(/\D/g, '');
      const expectedTotalDigits = fullFormatDigits.length;
      const expectedNumberDigits = expectedTotalDigits - country.dialCode.length;
      
      // 5. Strict length validation
      const currentTotalDigits = digitsOnly.length;
      const currentNumberDigits = numberWithoutCountryCode.length;
      
      // Case 1: Number is being entered (not yet complete)
      if (currentTotalDigits < expectedTotalDigits) {
        // No error yet, still typing
      }
      // Case 2: Number matches exact length
      else if (currentTotalDigits === expectedTotalDigits) {
        // Perfect match - no error but prevent further input
        // You might want to blur the field here
      }
      // Case 3: Number exceeds expected length
      // else if (currentTotalDigits > expectedTotalDigits) {
      //   phoneError = `Phone number too long for ${country.name}`;
      // }
      
      // 6. Special minimum length checks for certain countries
      const minDigitsRequired = this.getMinDigitsForCountry(country);
      if (currentNumberDigits < minDigitsRequired) {
        phoneError = `${country.name} numbers require at least ${minDigitsRequired} digits`;
      }
    }
  
    this.setState({
      phoneError,
      newUser: {
        ...this.state.newUser,
        mobile: value,
      }
    });
  };
  
  // Helper function for country-specific minimum lengths
  getMinDigitsForCountry = (country) => {
    const minDigitsMap = {
      'US': 7,  // US numbers are 7 digits after country code (1)
      'GB': 7,  // UK numbers are typically 9-10 digits
      'DE': 6,  // Germany
      'FR': 6,  // France
      'IN': 8   // India
      // Add other countries as needed
    };
    
    return minDigitsMap[country.countryCode] || 5; // Default minimum
  };

  render() {
    const { currentPage, totalItems, itemsPerPage } = this.state;
    const start = currentPage * itemsPerPage + 1;
    const end = Math.min((currentPage + 1) * itemsPerPage, totalItems);

    return (
      <div className={`${classes.bgColor} container mt-3`}>
        <div className="row text-center">
          <div className="col-12">
            <h4>Create User</h4>
            <hr />
            {/* <Button variant="primary" onClick={() => this.openModal()}>
              <i className="fa fa-user-plus"></i>&nbsp; Add User
            </Button> */}
          </div>
        </div>

        {this.state.notification.show && (
          <Notification
            message={this.state.notification.message}
            type={this.state.notification.type}
            onClose={this.closeNotification}
          />
        )}

        <div className="row mt-2">
          <div className="col-12 d-flex align-items-center">
            <h4>
              <i className="fa fa-user"></i>&nbsp;List of Users
            </h4>
            <Button
              variant="primary"
              onClick={() => this.openModal()}
              className="ms-3"
            >
              <i className="fa fa-user-plus"></i>&nbsp; Add
            </Button>
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            data={this.state.userList.map((user, i) => ({
              ...user,
              serialNo: start + i,
            }))}
            columns={[
              {
                key: "serialNo",
                header: "S.No",
                width: "60px",
                sortable: false,
              },
              {
                key: "firstName",
                header: "First Name",
                sortable: true,
              },
              {
                key: "lastName",
                header: "Last Name",
                sortable: true,
              },
              {
                key: "mobile",
                header: "Mobile",
                sortable: true,
              },
              {
                key: "email",
                header: "Email ID",
                sortable: true,
              },
              {
                key: "userName",
                header: "User Name",
                sortable: true,
              },
              {
                key: "actions",
                header: "Actions",
                sortable: false,
                render: (_, user) => (
                  <div>
                    <button
                      title="edit user"
                      className="btn btn-primary"
                      onClick={() => this.openModal(user)}
                    >
                      <i className="fa fa-edit"></i>
                    </button>
                    <button
                      title="delete user"
                      className="btn btn-danger ms-2"
                      onClick={() => this.openDeleteConfirm(user.id)}
                    >
                      <i className="fa fa-trash"></i>
                    </button>
                  </div>
                ),
              },
            ]}
            searchable={true}
            className="table-hover"
            itemsPerPage={this.state.itemsPerPage}
            totalItems={this.state.totalItems}
            currentPage={this.state.currentPage}
            onPageChange={(page) => this.fetchUserList(page)}
            onItemsPerPageChange={(size) => {
              this.setState({ itemsPerPage: size }, () => {
                this.fetchUserList(0);
              });
            }}
          />
        </div>

        {/* DataTable component has its own pagination */}

        <Modal
          show={this.state.isOpen}
          onHide={this.closeModal}
          size="md"
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              {this.state.isEditing ? "Edit User" : "New User"}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <form name="userCreate" onSubmit={this.handleSubmit}>
              {/* <div className="row">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="userName"
                >
                  User Name :
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="userName"
                    id="userName"
                    value={this.state.newUser.userName}
                    className="form-control"
                    placeholder="Enter User Name"
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div> */}

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="fname"
                >
                  First Name <span style={{ color: "red" }}>*</span>
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    id="fname"
                    name="firstName"
                    value={this.state.newUser.firstName}
                    className="form-control"
                    placeholder="Enter First Name"
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="lname"
                >
                  Last Name <span style={{ color: "red" }}>*</span>
                </label>
                <div className="col-sm-7">
                  <input
                    type="text"
                    name="lastName"
                    id="lname"
                    value={this.state.newUser.lastName}
                    className="form-control"
                    placeholder="Enter Last Name"
                    required
                    onChange={this.handleInputChange}
                  />
                </div>
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="lname"
                >
                  Mobile
                </label>
                <div className="col-sm-7">
    <PhoneInput
      country={'us'}
      value={this.state.newUser.mobile}
      onChange={(value, country) => this.handlePhoneChange(value, country)}
      inputProps={{
        name: 'mobile',
        required: false,
      }}
      inputStyle={{ width: '100%' }}
      dropdownStyle={{ zIndex: 2000 }}
      enableSearch
      disableCountryCode={false}
      countryCodeEditable={false}
      // isValid={(value, country) => {
      //   if (value.match(/[^0-9+]/)) return 'Invalid phone number';
      //   if (value.length < country.format.length - 3) {
      //     return `Phone number too short for ${country.name}`;
      //   }
      //   if (value.length > country.format.length + 3) {
      //     return `Phone number too long for ${country.name}`;
      //   }
      //   return true;
      // }}
    />
    {this.state.phoneError && (
      <div className="text-danger mt-1">{this.state.phoneError}</div>
    )}
  </div>
                {/* <div className="col-sm-7">
                  <input
                    type="text"
                    name="mobile"
                    id="mobile"
                    value={this.state.newUser.mobile}
                    className="form-control"
                    placeholder="Enter Mobile No."
                    onChange={this.handleInputChange}
                  />
                </div> */}
              </div>

              <div className="row mt-2">
                <label
                  className="col-sm-3 offset-sm-1 col-form-label"
                  htmlFor="email"
                >
                  Email Id <span style={{ color: "red" }}>*</span>
                </label>
                <div className="col-sm-7">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={this.state.newUser.email}
                    className="form-control"
                    placeholder="Enter Email ID"
                    required
                    onChange={this.handleEmailChange}
                  />
                  {this.state.isUserExist && (
                    <span style={{ color: "red" }}>
                      User already exists in DB
                    </span>
                  )}
                </div>
              </div>

              <div className="row mt-1">
                <div className="col-12">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={this.state.isUserExist}
                  >
                    {this.state.isEditing ? "Update" : "Submit"}
                  </Button>
                </div>
              </div>
            </form>
          </Modal.Body>
        </Modal>

        <Modal
          show={this.state.showDeleteConfirm}
          onHide={this.closeDeleteConfirm}
          size="lg" // Changed to lg to accommodate more content
          centered
        >
          <Modal.Header closeButton className="modal-header-modern">
            <Modal.Title style={{ fontSize: "18px", color: "white" }}>
              Confirm Deletion
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <div>
              <p>Are you sure to delete this user?</p>

              {this.state.docsExists && (
                <>
                  <div className="mb-3">
                    <label className="form-label">
                      Transfer documents and team-lead ownership to:
                    </label>
                    <select
                      className="form-control bg-info text-white mb-3"
                      onChange={(e) =>
                        this.setState({
                          ownerId: e.target.value,
                          showError: "",
                        })
                      }
                      value={this.state.ownerId}
                    >
                      <option value="">-- Select User --</option>
                      {this.state.filteredUserList?.length > 0 ? (
                        this.state.filteredUserList.map((user) => (
                          <option key={user.id} value={user.id}>
                            {user.firstName} {user.lastName}
                          </option>
                        ))
                      ) : (
                        <option value="">No users available</option>
                      )}
                    </select>
                  </div>

                  {this.state.documentsToTransfer?.length > 0 && (
                    <div className="mb-3">
                      <label className="form-label">
                        Select documents to transfer:
                      </label>
                      <div
                        style={{
                          maxHeight: "200px",
                          overflowY: "auto",
                          border: "1px solid #dee2e6",
                          borderRadius: "4px",
                        }}
                      >
                        {this.state.documentsToTransfer.map((doc) => (
                          <div
                            key={doc.documentsAttachmentId}
                            className="form-check"
                          >
                            <input
                              className="form-check-input"
                              type="checkbox"
                              id={`doc-${doc.documentsAttachmentId}`}
                              checked={this.state.selectedDocuments.includes(
                                doc.documentsAttachmentId // Changed from doc.id
                              )}
                              onChange={(e) => {
                                const { checked } = e.target;
                                this.setState(
                                  (prevState) => ({
                                    selectedDocuments: checked
                                      ? [
                                          ...prevState.selectedDocuments,
                                          doc.documentsAttachmentId,
                                        ]
                                      : prevState.selectedDocuments.filter(
                                          (id) =>
                                            id !== doc.documentsAttachmentId
                                        ),
                                  }),
                                  () => {
                                    console.log(
                                      "Selected Documents:",
                                      this.state.selectedDocuments
                                    ); // Log the current selection
                                  }
                                );
                              }}
                            />
                            <label
                              className="form-check-label"
                              htmlFor={`doc-${doc.documentsAttachmentId}`}
                            >
                              {doc.documentName ||
                                `Document ${doc.documentsAttachmentId}`}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {this.state.showError && (
                    <p className="text-danger">{this.state.showError}</p>
                  )}
                </>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={this.closeDeleteConfirm}>
              Cancel
            </Button>
            <Button variant="danger" onClick={this.confirmDeleteUser}>
              Delete
            </Button>
          </Modal.Footer>
        </Modal>
        {this.state.isLoading && <Loader />}
      </div>
    );
  }
}

export default CreateUser;
